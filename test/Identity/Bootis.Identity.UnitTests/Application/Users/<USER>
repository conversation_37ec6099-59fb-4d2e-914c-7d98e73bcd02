using Bootis.Identity.Application.Users;
using Bootis.Identity.Domain.Entities;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Identity.UnitTests.Fixtures;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common;

using FluentAssertions;
using Microsoft.Extensions.Localization;
using Moq;
using UUIDNext;
using Xunit;

namespace Bootis.Identity.UnitTests.Application.Users;

public class ChangePasswordRequestHandlerTests
{
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly Mock<IUserContext> _userContextMock;
    private readonly ChangePasswordRequestHandler _handler;
    private readonly Guid _userId;
    private static readonly Mock<IStringLocalizer> _mockLocalizer = new();

    static ChangePasswordRequestHandlerTests()
    {
        _mockLocalizer.Setup(l => l[It.IsAny<string>()])
            .Returns((string key) => new LocalizedString(key, key));

        Localizer.Initialize(_mockLocalizer.Object);
    }

    public ChangePasswordRequestHandlerTests()
    {
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _userRepositoryMock = new Mock<IUserRepository>();
        _userContextMock = new Mock<IUserContext>();
        _userId = Uuid.NewSequential();

        _unitOfWorkMock.Setup(x => x.GetRepository<IUserRepository>())
            .Returns(_userRepositoryMock.Object);
        
        _userContextMock.Setup(x => x.UserId)
            .Returns(_userId);

        _handler = new ChangePasswordRequestHandler(
            _unitOfWorkMock.Object,
            _userContextMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldChangePasswordSuccessfully()
    {
        // Arrange
        var user = UserFakes.CreateValidUser();
        var request = new ChangePasswordRequest(
            OldPassword: "TempPassword123!",
            NewPassword: "NewPassword123!",
            ConfirmPassword: "NewPassword123!");

        _userRepositoryMock.Setup(x => x.GetByIdAsync(_userId))
            .ReturnsAsync(user);

        var originalPasswordHash = user.PasswordHash;

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        user.PasswordHash.Should().NotBe(originalPasswordHash);
        user.UpdatedAt.Should().BeCloseTo(DateTimeOffset.UtcNow, TimeSpan.FromSeconds(5));
        
        _userRepositoryMock.Verify(x => x.GetByIdAsync(_userId), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithInvalidOldPassword_ShouldThrowDomainException()
    {
        // Arrange
        var user = UserFakes.CreateValidUser();
        var request = new ChangePasswordRequest(
            OldPassword: "WrongPassword",
            NewPassword: "NewPassword123!",
            ConfirmPassword: "NewPassword123!");

        _userRepositoryMock.Setup(x => x.GetByIdAsync(_userId))
            .ReturnsAsync(user);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<DomainException>();
        
        _userRepositoryMock.Verify(x => x.GetByIdAsync(_userId), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithEmptyUserId_ShouldThrowValidationException()
    {
        // Arrange
        _userContextMock.Setup(x => x.UserId)
            .Returns(Guid.Empty);

        var request = new ChangePasswordRequest(
            OldPassword: "TempPassword123!",
            NewPassword: "NewPassword123!",
            ConfirmPassword: "NewPassword123!");

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<ValidationException>()
            .Where(ex => ex.Errors.Any(e => e.PropertyName == "userId"));
        
        _userRepositoryMock.Verify(x => x.GetByIdAsync(It.IsAny<Guid>()), Times.Never);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithNonExistentUser_ShouldThrowValidationException()
    {
        // Arrange
        var request = new ChangePasswordRequest(
            OldPassword: "TempPassword123!",
            NewPassword: "NewPassword123!",
            ConfirmPassword: "NewPassword123!");

        _userRepositoryMock.Setup(x => x.GetByIdAsync(_userId))
            .ReturnsAsync((User)null);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<ValidationException>()
            .Where(ex => ex.Errors.Any(e => e.PropertyName == "userId"));
        
        _userRepositoryMock.Verify(x => x.GetByIdAsync(_userId), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("", "NewPassword123!", "NewPassword123!")]
    public async Task Handle_WithEmptyOldPassword_ShouldThrowDomainException(string oldPassword, string newPassword, string confirmPassword)
    {
        // Arrange
        var user = UserFakes.CreateValidUser();
        var request = new ChangePasswordRequest(
            OldPassword: oldPassword,
            NewPassword: newPassword,
            ConfirmPassword: confirmPassword);

        _userRepositoryMock.Setup(x => x.GetByIdAsync(_userId))
            .ReturnsAsync(user);

        // Act & Assert
        // Empty old password should fail verification
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<DomainException>();
        
        _userRepositoryMock.Verify(x => x.GetByIdAsync(_userId), Times.Once);
    }

    [Theory]
    [InlineData("TempPassword123!", "", "NewPassword123!")]
    [InlineData("TempPassword123!", "NewPassword123!", "")]
    public async Task Handle_WithEmptyNewPassword_ShouldSucceed(string oldPassword, string newPassword, string confirmPassword)
    {
        // Arrange
        var user = UserFakes.CreateValidUser();
        var request = new ChangePasswordRequest(
            OldPassword: oldPassword,
            NewPassword: newPassword,
            ConfirmPassword: confirmPassword);

        _userRepositoryMock.Setup(x => x.GetByIdAsync(_userId))
            .ReturnsAsync(user);

        // Act
        await _handler.Handle(request, CancellationToken.None);
        
        // Assert
        _userRepositoryMock.Verify(x => x.GetByIdAsync(_userId), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenRepositoryThrows_ShouldPropagateException()
    {
        // Arrange
        var request = new ChangePasswordRequest(
            OldPassword: "TempPassword123!",
            NewPassword: "NewPassword123!",
            ConfirmPassword: "NewPassword123!");

        var expectedException = new InvalidOperationException("Database error");
        _userRepositoryMock.Setup(x => x.GetByIdAsync(_userId))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage(expectedException.Message);
        
        _userRepositoryMock.Verify(x => x.GetByIdAsync(_userId), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WhenSaveChangesThrows_ShouldPropagateException()
    {
        // Arrange
        var user = UserFakes.CreateValidUser();
        var request = new ChangePasswordRequest(
            OldPassword: "TempPassword123!",
            NewPassword: "NewPassword123!",
            ConfirmPassword: "NewPassword123!");

        _userRepositoryMock.Setup(x => x.GetByIdAsync(_userId))
            .ReturnsAsync(user);

        var expectedException = new InvalidOperationException("Save failed");
        _unitOfWorkMock.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage(expectedException.Message);
        
        _userRepositoryMock.Verify(x => x.GetByIdAsync(_userId), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}