// <auto-generated />
using System;
using Bootis.Shared.Infrastructure;
using Bootis.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Bootis.CEP.Api.Infrastructure.Migrations
{
    [DbContext(typeof(BootisContext))]
    [Migration("20250201063036_Initial")]
    partial class Initial
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Bootis.CEP.Api.Models.Bairro", b =>
                {
                    b.Property<int>("id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<int>("cidade_id")
                        .HasColumnType("integer")
                        .HasColumnName("cidade_id");

                    b.HasKey("id")
                        .HasName("pk_bairros");

                    b.HasIndex("cidade_id")
                        .HasDatabaseName("ix_bairros_cidade_id");

                    b.ToTable("bairros", (string)null);
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Cep", b =>
                {
                    b.Property<int>("id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Logradouro")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("numero");

                    b.Property<int>("bairro_id")
                        .HasColumnType("integer")
                        .HasColumnName("bairro_id");

                    b.HasKey("id")
                        .HasName("pk_ceps");

                    b.HasIndex("bairro_id")
                        .HasDatabaseName("ix_ceps_bairro_id");

                    b.ToTable("ceps", (string)null);
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Cidade", b =>
                {
                    b.Property<int>("id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Cep")
                        .HasMaxLength(9)
                        .HasColumnType("character varying(9)")
                        .HasColumnName("cep");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<int>("estado_id")
                        .HasColumnType("integer")
                        .HasColumnName("estado_id");

                    b.HasKey("id")
                        .HasName("pk_cidades");

                    b.HasIndex("estado_id")
                        .HasDatabaseName("ix_cidades_estado_id");

                    b.ToTable("cidades", (string)null);
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Estado", b =>
                {
                    b.Property<int>("id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.Property<string>("Sigla")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)")
                        .HasColumnName("sigla");

                    b.Property<int>("pais_id")
                        .HasColumnType("integer")
                        .HasColumnName("pais_id");

                    b.HasKey("id")
                        .HasName("pk_estados");

                    b.HasIndex("pais_id")
                        .HasDatabaseName("ix_estados_pais_id");

                    b.ToTable("estados", (string)null);
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Pais", b =>
                {
                    b.Property<int>("id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("nome");

                    b.HasKey("id")
                        .HasName("pk_paises");

                    b.ToTable("paises", (string)null);
                });

            modelBuilder.Entity("Bootis.Shared.Infrastructure.Entities.SequenceControl", b =>
                {
                    b.Property<string>("EntityName")
                        .HasColumnType("text")
                        .HasColumnName("entity_name");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<int>("CurrentSequence")
                        .HasColumnType("integer")
                        .HasColumnName("current_sequence");

                    b.HasKey("EntityName", "TenantId", "GroupTenantId")
                        .HasName("pk_sequence_control");

                    b.ToTable("sequence_control", (string)null);
                });

            modelBuilder.Entity("Bootis.Shared.Infrastructure.SeedHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("ExecutedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("executed_at");

                    b.Property<string>("SeedName")
                        .IsRequired()
                        .HasMaxLength(-1)
                        .HasColumnType("text")
                        .HasColumnName("seed_name");

                    b.HasKey("Id")
                        .HasName("pk_seed_history");

                    b.ToTable("seed_history", (string)null);
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Bairro", b =>
                {
                    b.HasOne("Bootis.CEP.Api.Models.Cidade", "Cidade")
                        .WithMany("Bairros")
                        .HasForeignKey("cidade_id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_bairros_cidades_cidade_id");

                    b.Navigation("Cidade");
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Cep", b =>
                {
                    b.HasOne("Bootis.CEP.Api.Models.Bairro", "Bairro")
                        .WithMany("Ceps")
                        .HasForeignKey("bairro_id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ceps_bairros_bairro_id");

                    b.Navigation("Bairro");
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Cidade", b =>
                {
                    b.HasOne("Bootis.CEP.Api.Models.Estado", "Estado")
                        .WithMany("Cidades")
                        .HasForeignKey("estado_id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_cidades_estados_estado_id");

                    b.Navigation("Estado");
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Estado", b =>
                {
                    b.HasOne("Bootis.CEP.Api.Models.Pais", "Pais")
                        .WithMany("Estados")
                        .HasForeignKey("pais_id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_estados_paises_pais_id");

                    b.Navigation("Pais");
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Bairro", b =>
                {
                    b.Navigation("Ceps");
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Cidade", b =>
                {
                    b.Navigation("Bairros");
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Estado", b =>
                {
                    b.Navigation("Cidades");
                });

            modelBuilder.Entity("Bootis.CEP.Api.Models.Pais", b =>
                {
                    b.Navigation("Estados");
                });
#pragma warning restore 612, 618
        }
    }
}
