namespace Bootis.Setup.Api.RequestHandlers.Producao;

public record FormulaPadrao(
    Guid Id,
    string ProdutoDescricao,
    string FormaFarmaceuticaDescricao,
    int FormulaPadraoDesmembramento,
    Guid UnidadeMedidaId,
    decimal QuantidadePadrao,
    int DiasValidade,
    decimal Diluicao,
    decimal Densidade,
    string Procedimento,
    string Rodape);

public record MigrateFormulasPadrao(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateFormulasPadraoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateFormulasPadrao>(configuration)
{
    public override async Task Handle(MigrateFormulasPadrao request, CancellationToken cancellationToken)
    {
        var dataTable = await GetDataFromSourceDatabase(request, "SELECT * FROM formulas_padrao", cancellationToken);

        await InsertOrUpdateData(dataTable, "formulas_padrao", cancellationToken);
    }
}