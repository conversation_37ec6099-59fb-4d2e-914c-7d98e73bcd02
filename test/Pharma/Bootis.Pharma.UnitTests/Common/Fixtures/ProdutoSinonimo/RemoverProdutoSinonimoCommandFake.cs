using Bootis.Catalogo.Application.Requests.ProdutoSinonimo.Remover;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.ProdutoSinonimo;

public class RemoverProdutoSinonimoCommandFake
{
    public static RemoverRequest CreateRemoverProdutoSinonimoCommandValido()
    {
        var id = new List<Guid>
        {
            Uuid.NewSequential()
        };

        return new RemoverRequest
        {
            Id = id
        };
    }
}
