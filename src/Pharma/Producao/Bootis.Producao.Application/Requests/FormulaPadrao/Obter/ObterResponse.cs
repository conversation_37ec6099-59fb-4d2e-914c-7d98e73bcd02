using System.ComponentModel;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.FormulaPadrao.Obter;

public class ObterResponse
{
    public Guid Id { get; set; }
    public Guid ProdutoId { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }
    public string DescricaoFormaFarmaceutica { get; set; }
    public string DescricaoProdutoFormula { get; set; }
    public FormulaPadraoDesmembramento FormulaPadraoDesmembramentoId { get; set; }

    public virtual string FormulaPadraoDesmembramento
    {
        get
        {
            var descriptionAttribute = typeof(FormulaPadraoDesmembramento)
                    .GetField(FormulaPadraoDesmembramentoId.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : FormulaPadraoDesmembramentoId.ToString();
        }
    }

    public string UnidadeMedidaAbreviacao { get; set; }
    public decimal QuantidadePadrao { get; set; }
    public int DiasValidade { get; set; }
    public decimal Diluicao { get; set; }
    public decimal Densidade { get; set; }
    public string Procedimento { get; set; }
    public string Rodape { get; set; }
    public IEnumerable<ObterFormulaPadraoItemResponse> Produtos { get; set; }
}

public class ObterFormulaPadraoItemResponse
{
    public Guid Id { get; set; }
    public Guid ProdutoId { get; set; }
    public int CodigoProduto { get; set; }
    public string DescricaoProduto { get; set; }
    public int ClasseProdutoId { get; set; }
    public int UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public decimal Quantidade { get; set; }
    public int Fase { get; set; }
    public TipoComponente TipoItem { get; set; }
}