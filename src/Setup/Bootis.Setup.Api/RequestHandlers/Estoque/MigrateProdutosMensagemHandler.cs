namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record ProdutoMensagem(Guid Id, string ProdutoDescricao, string MensagemDescricao);

public record MigrateProdutosMensagem(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateProdutosMensagemHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateProdutosMensagem>(configuration)
{
    public override async Task Handle(MigrateProdutosMensagem request, CancellationToken cancellationToken)
    {
        var dataTable = await GetDataFromSourceDatabase(request, "SELECT * FROM produtos_mensagem", cancellationToken);

        await InsertOrUpdateData(dataTable, "produtos_mensagem", cancellationToken);
    }
}