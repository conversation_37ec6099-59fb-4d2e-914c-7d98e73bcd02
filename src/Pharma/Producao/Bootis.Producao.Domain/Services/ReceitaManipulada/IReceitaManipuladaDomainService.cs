using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoExcipienteAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Estoque.Domain.Dtos.Lote;
using Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Common.Interfaces;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public interface IReceitaManipuladaDomainService : IScopedService
{
    Task<IEnumerable<ProdutoAssociado>> ObterProdutosAssociadosAsync(Guid produtoId, Guid formaFarmaceuticaId);
    Task<FormulaPadrao> ObterFormulaPadraoAsync(Guid produtoId);

    Task<(DateOnly dataValidade, RegraDataValidade regra)> RegraDataValidadeItemAsync(Produto produto,
        FormaFarmaceutica formaFarmaceutica);

    Task<CalculoReceitaManipulada.ResultadoCapsula> EscolherCapsulaAsync(decimal volumeMinimoRecipiente);
    Task<ProdutoExcipiente> ObterExcipienteAsync(TipoClasseBiofarmaceutica classeBiofarmaceutica);
    Task<ProdutoEmbalagem> EscolherEmbalagemAsync(decimal quantidadeCapsulas, Guid capsulaTamanhoId);
    Task<decimal> ObterDescontoPrescritorAsync(Guid? prescritorId);
    Task<ProdutoTipoCapsula> ObterProdutoTipoCapsulaPorIdAsync(Guid produtoCapsulaId);

    Task<ReceitaManipuladaRastreioCalculo.FatoresTotais> ObterFatoresTotaisAsync(Produto produtoUsado,
        Produto produtoOrigem, ReceitaManipuladaItem itemOrigem, LoteIdComSaldoDto lote = null);

    Task<List<CalculoReceitaManipulada.EmbalagemQspFracionada>> EscolherEmbalagensQspFracionadoAsync(
        decimal volumeTotalMl);
}