using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Catalogo.Domain.Dtos.ProdutoDiluido;

public interface IProdutoDiluidoDto
{
    public decimal DosagemMinima { get; set; }
    public decimal DosagemMaxima { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public decimal Diluicao { get; set; }
    public bool SeTodasFormasFarmaceuticas { get; set; }
    public bool SeQualquerDosagem { get; set; }
}