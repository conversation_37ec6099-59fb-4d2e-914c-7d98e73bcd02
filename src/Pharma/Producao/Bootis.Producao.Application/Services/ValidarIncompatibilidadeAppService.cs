using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;
using Bootis.Producao.Application.Services.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Application.Services;

public class ValidarIncompatibilidadeAppService : IValidarIncompatibilidadeAppService, IScopedService
{
    private readonly IProdutoIncompativelRepository _produtoIncompativelRepository;

    public ValidarIncompatibilidadeAppService(IProdutoIncompativelRepository produtoIncompativelRepository)
    {
        _produtoIncompativelRepository = produtoIncompativelRepository;
    }

    public async Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Guid> produtoIds)
    {
        var ids = produtoIds?.Distinct().ToList() ?? [];
        if (ids.Count < 2) return;

        var incompatibilidades = await _produtoIncompativelRepository
            .ObterTodasIncompatibilidadesEntreAsync(ids);

        var dedup = DeduplicarPares(incompatibilidades.ToList());
        
        dedup = dedup
            .Where(i => i.Produto?.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima
                        && i.ProdutoIncompatibilidade?.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima)
            .ToList();

        var bloqueios = dedup
            .Where(i => i.NivelIncompatibilidade == NivelIncompatibilidade.Bloqueio)
            .ToList();

        if (!bloqueios.Any()) return;

        var first = bloqueios[0];
        var msg = bloqueios.Count == 1
            ? $"Incompatibilidade de Bloqueio detectada entre '{first.Produto?.Descricao}' e '{first.ProdutoIncompatibilidade?.Descricao}'. Descrição: {first.Descricao}. A manipulação não pode ser realizada com estes componentes."
            : $"Múltiplas incompatibilidades de bloqueio detectadas ({bloqueios.Count} pares). A manipulação não pode ser realizada com estes componentes.";

        throw new ValidationException(msg);
    }

    public async Task<IncompatibilidadeResponse> ObterCenariosIncompatibilidadeAsync(IEnumerable<Guid> produtoIds)
    {
        var produtoIdsList = produtoIds.ToList();
        if (produtoIdsList.Count < 2)
            return new IncompatibilidadeResponse { NivelMaisRestritivo = NivelIncompatibilidade.Ok };

        var todasIncompatibilidades = await _produtoIncompativelRepository
            .ObterTodasIncompatibilidadesEntreAsync(produtoIdsList);

        var lista = DeduplicarPares(todasIncompatibilidades.ToList());

        lista = lista
            .Where(i => i.Produto?.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima
                        && i.ProdutoIncompatibilidade?.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima)
            .ToList();

        if (lista.Count == 0)
            return new IncompatibilidadeResponse { NivelMaisRestritivo = NivelIncompatibilidade.Ok };
        
        var incompatibilidadesDto = lista.Select(i => new ParIncompativelDto
        {
            ProdutoId = i.ProdutoId,
            ProdutoDescricao = i.Produto?.Descricao ?? "Produto não encontrado",
            ProdutoIncompativelId = i.ProdutoIncompativelId,
            ProdutoIncompativelDescricao = i.ProdutoIncompatibilidade?.Descricao ?? "Produto não encontrado",
            Descricao = i.Descricao
        }).ToList();
        
        var nivelMaisRestritivo = lista.Max(i => i.NivelIncompatibilidade);
        
        var incompatibilidadesDoNivelMaisRestritivo = lista
            .Where(i => i.NivelIncompatibilidade == nivelMaisRestritivo)
            .ToList();

        var tipo = incompatibilidadesDoNivelMaisRestritivo.Count == 1
            ? IncompatibilidadeTipo.Unica
            : IncompatibilidadeTipo.Multipla;

        return new IncompatibilidadeResponse
        {
            NivelMaisRestritivo = nivelMaisRestritivo,
            Tipo = tipo,
            Incompatibilidades = incompatibilidadesDto
        };
    }
    
    private static List<ProdutoIncompativel> DeduplicarPares(List<ProdutoIncompativel> incompatibilidades)
    {
        var pares = new Dictionary<string, ProdutoIncompativel>();

        foreach (var incomp in incompatibilidades)
        {
            var a = incomp.ProdutoId;
            var b = incomp.ProdutoIncompativelId;
            var chave = a.CompareTo(b) <= 0 ? $"{a}-{b}" : $"{b}-{a}";

            if (pares.TryGetValue(chave, out var atual))
            {
                if ((int)incomp.NivelIncompatibilidade > (int)atual.NivelIncompatibilidade)
                    pares[chave] = incomp;
            }
            else
            {
                pares[chave] = incomp;
            }
        }

        return pares.Values.ToList();
    }
}