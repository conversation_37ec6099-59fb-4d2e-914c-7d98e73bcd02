using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.UnidadeMedida.Listar;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Infrastructure.Query;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.UnidadeMedida.Listar;

public class ListarReceitaRequestHandler(
    IUserContext userContext,
    IDbConnection connection,
    ILoteRepository loteRepository,
    IProdutoRepository produtoRepository,
    IFormaFarmaceuticaRepository farmaceuticaRepository)
    : IRequestHandler<ListarReceitaRequest, PaginatedResult<ListarReceitaResponse>>
{
    public async Task<PaginatedResult<ListarReceitaResponse>> Handle(ListarReceitaRequest request,
        CancellationToken cancellationToken)
    {
        var classeProduto = await produtoRepository.ObterClasseProdutoAsync(request.ProdutoId);
        var tipoCalculo = await farmaceuticaRepository.ObterTipoCalculoAsync(request.FormaFarmaceuticaId);

        var sql = new StringBuilder("""
                                    SELECT un.id AS unidade_medida_id,
                                           un.abreviacao,
                                           un.unidade_alternativa,
                                           un.descricao,
                                           pm.unidade_prescricao_id
                                    FROM classes_produto_unidades_prescricao up
                                         LEFT JOIN unidades_medida un ON
                                             un.id = up.unidade_medida
                                         LEFT JOIN classes_produto cp ON
                                             cp.id = up.classe_produto_id
                                         LEFT JOIN produtos_materia_prima pm ON
                                             pm.unidade_prescricao_id = un.id
                                    WHERE un.unidade_alternativa = false
                                    """);

        if (classeProduto == TipoClasseProdutoAbreviacao.MateriaPrima)
        {
            var unidadeAlternativaIdLote =
                await loteRepository.ObterLoteUnidadeAlternativaIdPorProdutoIdAsync(request.ProdutoId);

            if (unidadeAlternativaIdLote.Any())
            {
                var filtroUnidadeAlternativa = string.Join(",", unidadeAlternativaIdLote);

                sql.AppendLine($"""
                                     OR (
                                                                     UN.Id IN ({filtroUnidadeAlternativa})
                                                                 )
                                """);
            }
        }

        if (tipoCalculo == TipoCalculo.QSP && classeProduto == TipoClasseProdutoAbreviacao.MateriaPrima)
            sql.AppendLine($"            AND UP.classe_produto_id = '{(int)classeProduto}'");
        else
            sql.AppendLine($"""
                                       AND UP.classe_produto_id = '{(int)classeProduto}'
                                                                     AND UP.unidade_medida != '{(int)UnidadeMedidaAbreviacao.PER}'
                            """);

        sql.AppendLine("""
                                  GROUP BY UN.id,
                                                                    UN.abreviacao,
                                                                    UN.unidade_alternativa,
                                                                    UN.descricao,
                                                                    PM.unidade_prescricao_id
                       """);


        return await PaginatedQueryBuilder<ListarReceitaRequest, ListarReceitaResponse>
            .Create(connection, sql.ToString(), request, userContext)
            .ExecuteAsync();
    }
}