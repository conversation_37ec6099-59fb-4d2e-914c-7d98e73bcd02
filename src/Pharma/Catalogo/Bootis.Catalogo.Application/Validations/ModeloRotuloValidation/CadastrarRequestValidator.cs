using Bootis.Catalogo.Application.Requests.ModeloRotulo.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.ModeloRotuloValidation;

public class CadastrarRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Desc<PERSON>o)
            .MaximumLength(100)
            .NotEmpty();

        RuleFor(c => c.TipoRotulo)
            .IsInEnum()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.TipoRotulo)));
    }
}