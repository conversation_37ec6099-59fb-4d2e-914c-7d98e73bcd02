using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Bootis.Venda.Application.UseCases.StatusAtendimento;
using Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate;
using Bootis.Venda.UnitTests.Fixtures.StatusAtendimento;
using Moq;

namespace Bootis.Venda.UnitTests.Application.StatusAtendimento;

public class AtualizarStatusRequestHandlerTests : BaseTest
{
    private readonly AtualizarStatusRequestHandler _handler;
    private readonly Mock<IStatusAtendimentoRepository> _statusAtendimentoRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarStatusRequestHandlerTests()
    {
        _statusAtendimentoRepositoryMock = new Mock<IStatusAtendimentoRepository>();
        _handler = new AtualizarStatusRequestHandler(unitOfWork.Object, _statusAtendimentoRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarStatusAtendimento_ExecutadoComSucesso()
    {
        // Arrange
        var request = AtualizarRequestStatusAtendimentoFakes.AtualizarStatusAtendimentoRequestValido();
        var statusAtendimento = StatusAtendimentoFakes.CriarStatusAtendimentoValido();

        _statusAtendimentoRepositoryMock.Setup(s => s.ObterDescricaoStatusAtendimentoPorTenant(request.Descricao))
            .ReturnsAsync((Domain.AggregatesModel.StatusAtendimentoAggregate.StatusAtendimento)null);

        _statusAtendimentoRepositoryMock.Setup(s => s.GetStatusAtendimentoByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(statusAtendimento);

        unitOfWork.Setup(a => a.SaveChangesAsync(default))
            .ReturnsAsync(1);

        // Action
        await _handler.Handle(request, default);

        // Assert
    }

    [Fact]
    public async Task AtualizarStatusAtendimento_ExecutadoComErro_DescricaoExistente()
    {
        // Arrange
        var request = AtualizarRequestStatusAtendimentoFakes.AtualizarStatusAtendimentoRequestValido();
        var statusAtendimento = StatusAtendimentoFakes.CriarStatusAtendimentoValido();


        _statusAtendimentoRepositoryMock.Setup(s => s.ObterDescricaoStatusAtendimentoPorTenant(request.Descricao))
            .ReturnsAsync(statusAtendimento);

        // Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(request, default));

        // Assert
        Assert.IsType<ValidationException>(exception);
        _statusAtendimentoRepositoryMock
            .Verify(a => a.Update(It.IsAny<Domain.AggregatesModel.StatusAtendimentoAggregate.StatusAtendimento>()),
                Times.Never);
    }
}
