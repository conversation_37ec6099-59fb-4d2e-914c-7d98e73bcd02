using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Domain.Services;

public interface ILoteSeletorService : ITransientService
{
    Task<LoteSeletorService.LoteSelecaoResultado> ResolverConsumoDeLotesAsync(
        Produto produto,
        Guid formaFarmaceuticaId,
        UnidadeMedidaAbreviacao unidadeEntrada,
        decimal quantidadeNecessaria);
}