<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{2cf6a0af-3ca3-41cf-982f-b0766fecb316}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Infrastructure\Bootis.Shared.Infrastructure.csproj"/>
        <ProjectReference Include="..\Bootis.Pessoa.Application\Bootis.Pessoa.Application.csproj"/>
        <ProjectReference Include="..\Bootis.Pessoa.Resources\Bootis.Pessoa.Resources.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>

