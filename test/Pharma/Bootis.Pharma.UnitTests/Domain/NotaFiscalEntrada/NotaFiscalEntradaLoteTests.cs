using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Compra.UnitTests.Fixtures;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;

namespace Bootis.Compra.UnitTests.Domain.NotaFiscalEntrada;

public class NotaFiscalEntradaLoteTests : BaseTest
{
    private readonly NotaFiscalEntradaItem NotaFiscalEntradaItem = new();

    [Fact(Skip = "Need to be fixed")]
    public void NotaFiscalEntradaLote_DeveAtualizarNotaFiscalEntradaLote()
    {
        //Arrange
        var localEstoqueId = 1.ToGuid();
        var paisId = 1.ToGuid();
        var produto = ProdutoFake.CreateProdutoFake_Embalagem();
        var cmd = CadastrarNotaFiscalEntradaLoteCommandFake.CriarCommandValido_Atualizar();
        NotaFiscalEntradaItem.AdicionarNotaFiscalEntradaLote(cmd.Lotes.First(), localEstoqueId, produto, paisId);


        //Action
        NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().AtualizarNotaFiscalEntradaLote(cmd.Lotes.First(),
            localEstoqueId, ProdutoFake.CreateProdutoMateriaPrimaFake(), paisId);

        //Assert
        Assert.Equal(cmd.Lotes.First().NumeroLote, NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().NumeroLote);
    }

    [Fact(Skip = "Need to be fixed")]
    public void NotaFiscalEntradaLote_DeveAtualizarNotaFiscalEntradaLote_InformacaoTecnica()
    {
        //Arrange
        var localEstoqueId = 1.ToGuid();
        var paisId = 1.ToGuid();
        var produto = ProdutoFake.CreateProdutoFake_Embalagem();
        var cmd = CadastrarNotaFiscalEntradaLoteCommandFake.CriarCommandValido_Atualizar();
        var cmdInfoTecnica = CadastrarNotaFiscalEntradaLoteCommandFake
            .CriarCommandValidoInformacaoTecnicaELoteUnidadeAlternativa_Atualizar();
        NotaFiscalEntradaItem.AdicionarNotaFiscalEntradaLote(cmd.Lotes.First(), localEstoqueId, produto, paisId);

        //Action
        NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().AtualizarNotaFiscalEntradaLote(cmdInfoTecnica.Lotes.First(),
            localEstoqueId, ProdutoFake.CreateProdutoMateriaPrimaFake(), paisId);

        //Assert
        Assert.Equal(cmdInfoTecnica.Lotes.First().InformacaoTecnica.Densidade,
            NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().NotaFiscalEntradaLoteInformacaoTecnica.Densidade);
    }

    [Fact(Skip = "Need to be fixed")]
    public void NotaFiscalEntradaLote_DeveAtualizarNotaFiscalEntradaLoteRascunho()
    {
        //Arrange
        var localEstoqueId = 1.ToGuid();
        var paisId = 1.ToGuid();
        var produto = ProdutoFake.CreateProdutoFake_Embalagem();
        var cmd = CadastrarNotaFiscalEntradaLoteRascunhoCommandFake.CriarCommandValido();
        NotaFiscalEntradaItem.AdicionarNotaFiscalEntradaLoteRascunho(cmd.Lotes.First(), localEstoqueId, produto,
            paisId);


        //Action
        NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().AtualizarNotaFiscalEntradaLoteRascunho(cmd.Lotes.First(),
            localEstoqueId, ProdutoFake.CreateProdutoMateriaPrimaFake(), paisId);

        //Assert
        Assert.Equal(cmd.Lotes.First().NumeroLote, NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().NumeroLote);
    }

    [Fact(Skip = "Need to be fixed")]
    public void NotaFiscalEntradaLote_DeveAtualizarNotaFiscalEntradaLote_InformacaoTecnicaRascunho()
    {
        //Arrange
        var localEstoqueId = 1.ToGuid();
        var paisId = 1.ToGuid();
        var produto = ProdutoFake.CreateProdutoFake_Embalagem();
        var cmd = CadastrarNotaFiscalEntradaLoteRascunhoCommandFake.CriarCommandValido();
        var cmdInfoTecnica = CadastrarNotaFiscalEntradaLoteRascunhoCommandFake.CriarCommandValidoInformacaoTecnica();
        NotaFiscalEntradaItem.AdicionarNotaFiscalEntradaLoteRascunho(cmd.Lotes.First(), localEstoqueId, produto,
            paisId);

        //Action
        NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().AtualizarNotaFiscalEntradaLoteRascunho(
            cmdInfoTecnica.Lotes.First(), localEstoqueId, ProdutoFake.CreateProdutoMateriaPrimaFake(), paisId);

        //Assert
        Assert.Equal(cmdInfoTecnica.Lotes.First().InformacaoTecnica.Densidade,
            NotaFiscalEntradaItem.NotaFiscalEntradaLote.First().NotaFiscalEntradaLoteInformacaoTecnica.Densidade);
    }
}
