using Bootis.Catalogo.Domain.AggregatesModel.ProdutoMensagemAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Catalogo.Application.Extensions;

public static class ProdutoMensagemRepositoryExtension
{
    public static async Task<ProdutoMensagem> ObterProdutoMensagemAsync(this IProdutoMensagemRepository repository,
        Guid mensagemId, Guid produtoId)
    {
        var produtoMensagem = await repository.ObterPorMensagemIdEhProdutoId(mensagemId, produtoId);

        if (produtoMensagem == null)
            throw new ValidationException(
                Localizer.Instance.GetMessage_ProdutoMensagem_NaoVinculado());

        return produtoMensagem;
    }

    public static async Task ValidarProdutoMensagemAsync(this IProdutoMensagemRepository repository,
        Guid mensagemId, Guid produtoId)
    {
        var hasProdutoMensagem = await repository.ValidarPorMensagemIdEhProdutoIdAsync(mensagemId, produtoId);

        if (hasProdutoMensagem)
            throw new ValidationException(
                Localizer.Instance.GetMessage_ProdutoMensagem_JaVinculado());
    }
}