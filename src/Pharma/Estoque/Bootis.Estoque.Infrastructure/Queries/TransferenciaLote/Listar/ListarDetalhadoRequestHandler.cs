using System.Data;
using Bootis.Estoque.Application.Requests.TransferenciaLote.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.TransferenciaLote.Listar;

public class
    ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT etl.id,
                                  etl.sequencia_numero_transferencia,
                                  etl.data_transferencia,
                                  eleo.id AS local_estoque_origem_id, 
                                  eleo.descricao AS local_estoque_origem_descricao,
                                  eeo.nome_fantasia AS local_estoque_origem_empresa,
                                  eled.id AS local_estoque_destino_id,
                                  eled.descricao AS local_estoque_destino_descricao,
                                  eed.nome_fantasia AS local_estoque_destino_empresa,
                                  eu.nome AS usuario_nome,
                                  eu.sobrenome AS usuario_sobrenome
                           FROM transferencias_lote etl 
                           LEFT JOIN locais_estoque eleo ON eleo.id = local_de_estoque_origem_id
                           LEFT JOIN empresas eeo ON eleo.empresa_id = eeo.id
                           LEFT JOIN locais_estoque eled ON eled.id = local_de_estoque_destino_id
                           LEFT JOIN empresas eed ON eled.empresa_id = eed.id
                           LEFT JOIN usuarios eu ON eu.id = etl.usuario_id 
                           WHERE etl.group_tenant_id = @GroupTenantId
                                 !@SEARCH_CONDITION@!
                           """;

        var searchDescricaoOrigem = new StringSearchField
        {
            Field = "ELEO.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchDataTransferencia = new DateSearchField
        {
            Field = "ETL.data_transferencia",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchDescricaoDestino = new StringSearchField
        {
            Field = "ELED.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchNumeroTransferencia = new NumberSearchField
        {
            Field = "ETL.sequencia_numero_transferencia",
            CompareType = NumericCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricaoOrigem)
            .AddSearchField(searchDataTransferencia)
            .AddSearchField(searchDescricaoDestino)
            .AddSearchField(searchNumeroTransferencia)
            .ExecuteAsync();
    }
}