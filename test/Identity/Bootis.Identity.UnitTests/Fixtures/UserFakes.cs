using Bootis.Identity.Domain.Entities;
using Bootis.Shared.Common;
using Bootis.Shared;
using Bogus;
using UUIDNext;

namespace Bootis.Identity.UnitTests.Fixtures;

public static class UserFakes
{
    private static readonly Faker<User> UserFaker = new Faker<User>()
        .RuleFor(u => u.Id, f => Uuid.NewSequential())
        .RuleFor(u => u.TenantId, f => DefaultsValues.BootisId)
        .RuleFor(u => u.GroupTenantId, f => DefaultsValues.BootisId)
        .RuleFor(u => u.Name, f => f.Person.FullName)
        .RuleFor(u => u.Email, f => f.Person.Email)
        .RuleFor(u => u.IsActive, f => true)
        .RuleFor(u => u.EmailConfirmed, f => false)
        .RuleFor(u => u.CreatedAt, f => DateTimeOffset.UtcNow.AddDays(-30))
        .RuleFor(u => u.UpdatedAt, f => null)
        .RuleFor(u => u.LastLoginAt, f => null);

    public static User CreateValidUser()
    {
        var user = UserFaker.Generate();
        user.UpdatePassword("TempPassword123!");
        return user;
    }

    public static User CreateValidUserWithConfirmedEmail()
    {
        var user = CreateValidUser();
        var originalEmail = user.Email;
        user.UpdateEmail("<EMAIL>"); // Change to different email to generate verification code
        user.UpdateEmail(originalEmail); // Change back to original email to generate verification code
        user.ConfirmEmail(user.EmailVerificationCode!.Value);
        return user;
    }

    public static User CreateValidUserWithPasswordResetCode()
    {
        var user = CreateValidUser();
        user.ForgetPassword();
        return user;
    }

    public static User CreateInactiveUser()
    {
        var user = CreateValidUser();
        user.IsActive = false;
        return user;
    }

    public static User CreateUserWithCustomEmail(string email)
    {
        var user = UserFaker.Generate();
        user.UpdatePassword("TempPassword123!");
        user.UpdateEmail(email);
        return user;
    }

    public static User CreateUserWithLastLogin()
    {
        var user = CreateValidUser();
        user.RecordLogin();
        return user;
    }
}