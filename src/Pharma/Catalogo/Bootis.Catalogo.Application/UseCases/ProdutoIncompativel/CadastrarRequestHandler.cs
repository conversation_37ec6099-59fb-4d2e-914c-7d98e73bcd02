using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.ProdutoIncompativel.Cadastrar;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.ProdutoIncompativel;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork)
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var produtoRepository = unitOfWork.GetRepository<IProdutoRepository>();

        var produto = await produtoRepository.ObterProdutoAsync(request.ProdutoId);
        var produtoIncompatibilidade =
            await produtoRepository.ObterProdutoAsync(request.ProdutoIncompativelId);

        var produtoIncompativelRepository =
            unitOfWork.GetRepository<IProdutoIncompativelRepository>();

        await ValidarProdutoIncompativelAsync(produtoIncompativelRepository, produto, produtoIncompatibilidade);

        var produtoIncompativel = new Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel(produto,
            produtoIncompatibilidade.Id,
            request.Descricao,
            request.NivelIncompatibilidade);

        produtoIncompativelRepository.Add(produtoIncompativel);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }

    private async Task ValidarProdutoIncompativelAsync(IProdutoIncompativelRepository produtoIncompativelRepository,
        Domain.AggregatesModel.ProdutoAggregate.Produto produto,
        Domain.AggregatesModel.ProdutoAggregate.Produto produtoIncompatibilidade)
    {
        await produtoIncompativelRepository.ValidarProdutoIncompativelPorProdutoAsync(produto,
            produtoIncompatibilidade);

        if (produtoIncompatibilidade.ClasseProdutoId != TipoClasseProdutoAbreviacao.MateriaPrima ||
            produto.ClasseProdutoId != TipoClasseProdutoAbreviacao.MateriaPrima)
            throw new ValidationException(nameof(CadastrarRequest.ProdutoIncompativelId),
                Localizer.Instance.GetMessage_ProdutoIncompativel_ProdutoInvalido());
    }
}