using StatusAtendimentoModel = Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class StatusAtendimentoFakes
{
    public static StatusAtendimentoModel.StatusAtendimento CriarStatusAtendimentoValido()
    {
        var usuario = UsuarioFakes.GerarUsuarioValido();

        return new StatusAtendimentoModel.StatusAtendimento("Em separa��o", true, 7, usuario);
    }

    public static StatusAtendimentoModel.StatusAtendimento CriarStatusAtendimentoValido_EmAtendimento()
    {
        var usuario = UsuarioFakes.GerarUsuarioValido();

        return new StatusAtendimentoModel.StatusAtendimento("Em Atendimento", true, 1, usuario);
    }
}
