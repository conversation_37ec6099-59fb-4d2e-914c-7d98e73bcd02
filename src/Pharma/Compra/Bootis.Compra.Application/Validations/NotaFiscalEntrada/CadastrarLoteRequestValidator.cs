using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Cadastrar;
using Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Compra.Application.Validations.NotaFiscalEntrada;

public class CadastrarLoteRequestValidator : AbstractValidator<LancarNotaFiscalEntradaRequest>
{
    public CadastrarLoteRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.NotaFiscalEntradaId)
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.NotaFiscalEntradaId)));

        RuleForEach(c => c.Lotes)
            .SetValidator(new CadastrarLotesRequestValidator(localizer));
    }
}

public class CadastrarLotesRequestValidator : AbstractValidator<NotaFiscalEntradaLoteDto>
{
    public CadastrarLotesRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.NotaFiscalEntradaItemId)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.NotaFiscalEntradaItemId)));

        RuleFor(c => c.NumeroLote)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.NumeroLote)));

        RuleFor(c => c.LocalEstoqueId)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.LocalEstoqueId)));

        RuleFor(c => c.DataFabricacao)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.DataFabricacao)));

        RuleFor(c => c.DataValidade)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.DataValidade)));

        RuleFor(c => c.UnidadeMedidaId)
            .IsInEnum();

        RuleFor(c => c.Quantidade)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Quantidade)));

        RuleFor(c => c.InformacaoTecnica)
            .SetValidator(new CadastrarLotesInformacaoTecnicaRequestValidator(localizer));
    }
}

public class
    CadastrarLotesInformacaoTecnicaRequestValidator : AbstractValidator<NotaFiscalEntradaLoteInformacaoTecnicaDto>
{
    public CadastrarLotesInformacaoTecnicaRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Densidade)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Densidade)));

        RuleFor(c => c.DiluicaoFornecedor)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.DiluicaoFornecedor)));

        RuleFor(c => c.FatorDiluicaoFornecedor)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.FatorDiluicaoFornecedor)));

        RuleFor(c => c.FatorConcentracaoAgua)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.FatorConcentracaoAgua)));

        RuleFor(c => c.ConcentracaoAgua)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.ConcentracaoAgua)));

        RuleFor(c => c.LoteUnidadeAlternativa1)
            .SetValidator(new CadastrarLotesUnidadeAlternativaRequestValidator(localizer));

        RuleFor(c => c.LoteUnidadeAlternativa2)
            .SetValidator(new CadastrarLotesUnidadeAlternativaRequestValidator(localizer));
    }
}

public class
    CadastrarLotesUnidadeAlternativaRequestValidator : AbstractValidator<NotaFiscalEntradaLoteUnidadeAlternativaDto>
{
    public CadastrarLotesUnidadeAlternativaRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.UnidadeAlternativaConversaoId)
            .IsInEnum();

        RuleFor(c => c.UnidadeAlternativaId)
            .IsInEnum();

        RuleFor(c => c.QuantidadeUnidadeAlternativa)
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.QuantidadeUnidadeAlternativa)));
    }
}