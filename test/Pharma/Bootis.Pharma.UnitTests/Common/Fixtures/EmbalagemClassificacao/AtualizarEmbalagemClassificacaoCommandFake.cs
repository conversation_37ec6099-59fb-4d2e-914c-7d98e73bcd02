using Bootis.Catalogo.Application.Requests.EmbalagemClassificacao.Atualizar;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.EmbalagemClassificacao;

public static class AtualizarEmbalagemClassificacaoCommandFake
{
    public static AtualizarRequest CreateAtualizarEmbalagemClassificacaoCommandValido()
    {
        return new AtualizarRequest
        {
            Id = Uuid.NewSequential(),
            Descricao = "Teste"
        };
    }
}
