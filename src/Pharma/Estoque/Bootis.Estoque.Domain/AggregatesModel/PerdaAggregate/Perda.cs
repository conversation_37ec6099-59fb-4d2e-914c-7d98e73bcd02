using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate;
using Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;

public class Perda : Entity, IAggregateRoot, ITenant
{
    public Perda()
    {
    }

    public Perda(DateOnly dataPerda,
        Guid loteId,
        Guid produtoId,
        Guid localEstoqueId,
        decimal quantidade,
        UnidadeMedidaAbreviacao unidadeMedidaId,
        Guid motivoPerdaId,
        string observacao,
        Guid usuarioId,
        Guid movimentoEstoqueId)

    {
        DataPerda = dataPerda;
        DataLancamento = DateTime.UtcNow;
        ProdutoId = produtoId;
        LoteId = loteId;
        LocalEstoqueId = localEstoqueId;
        Quantidade = quantidade;
        UnidadeMedidaId = unidadeMedidaId;
        MotivoPerdaId = motivoPerdaId;
        Observacao = observacao;
        UsuarioId = usuarioId;
        MovimentoEstoqueId = movimentoEstoqueId;
    }

    public DateOnly DataPerda { get; private set; }
    public DateTime DataLancamento { get; private set; }
    public Guid ProdutoId { get; private set; }
    public virtual Produto Produto { get; private set; }
    public Guid LoteId { get; private set; }
    public virtual Lote Lote { get; private set; }
    public Guid LocalEstoqueId { get; private set; }
    public virtual LocalEstoque LocalEstoque { get; private set; }
    public Guid UsuarioId { get; private set; }
    public virtual Usuario Usuario { get; private set; }
    public Guid MotivoPerdaId { get; private set; }
    public virtual MotivoPerda MotivoPerda { get; private set; }
    public Guid MovimentoEstoqueId { get; private set; }
    public virtual MovimentoEstoque MovimentoEstoque { get; private set; }
    public decimal Quantidade { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; private set; }
    public string Observacao { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarPerda(DateOnly dataPerda, MotivoPerda motivoPerda, string observacao)
    {
        MotivoPerda = motivoPerda;
        Observacao = observacao;
        DataPerda = dataPerda;
    }
}