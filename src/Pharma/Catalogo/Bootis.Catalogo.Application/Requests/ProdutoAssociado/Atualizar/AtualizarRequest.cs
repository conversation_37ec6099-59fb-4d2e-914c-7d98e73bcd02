using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Catalogo.Application.Requests.ProdutoAssociado.Atualizar;

public class AtualizarRequest : IRequest
{
    public Guid Id { get; set; }
    public Guid ProdutoAssociadoId { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }
    public decimal DosagemMinima { get; set; }
    public decimal DosagemMaxima { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaDosagem { get; set; }
    public decimal QuantidadeAssociada { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaQuantidadeAssociada { get; set; }
    public TipoRelacaoQuantidade TipoRelacaoQuantidade { get; set; }
    public bool Acumula { get; set; }
}