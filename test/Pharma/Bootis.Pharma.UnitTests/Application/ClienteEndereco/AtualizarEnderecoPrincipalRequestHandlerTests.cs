using Bootis.Pessoa.Application.UseCases.Cliente;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.UnitTests.Fixtures.ClienteEndereco;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Pessoa.UnitTests.Application.ClienteEndereco;

public class AtualizarEnderecoPrincipalRequestHandlerTests : BaseTest
{
    private readonly Mock<IClienteRepository> _clienteRepositoryMock;
    private readonly AtualizarEnderecoPrincipalRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarEnderecoPrincipalRequestHandlerTests()
    {
        _clienteRepositoryMock = new Mock<IClienteRepository>();
        _handler = new AtualizarEnderecoPrincipalRequestHandler(unitOfWork.Object, _clienteRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarClienteEnderecoPrincipal_ExecutadoComSucesso()
    {
        // Arrange
        var cmd = AtualizarRequestClienteEnderecoPrincipalFakes.CriarCommandValido();
        var enderecos = ClienteEnderecoFake.CriarClienteEnderecoValidoLista();

        _clienteRepositoryMock.Setup(l => l.ObterEnderecoPorEnderecoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(enderecos[2]);

        _clienteRepositoryMock.Setup(l => l.ObterEnderecosPorIdEhIdAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
            .ReturnsAsync(enderecos);

        _clienteRepositoryMock.Setup(l =>
                l.Update(It.IsAny<Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        // Action
        await _handler.Handle(cmd, default);

        // Assert
        _clienteRepositoryMock
            .Verify(
                l => l.Update(It.Is<Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco>(m =>
                    m.Descricao == "Endere�o 1" && m.Principal == false)), Times.Once);
    }

    [Fact]
    public async Task AtualizarClienteEnderecoPrincipal_ExecutadoComErro()
    {
        // Arrange
        var cmd = AtualizarRequestClienteEnderecoPrincipalFakes.CriarCommandValido();

        _clienteRepositoryMock.Setup(l => l.ObterEnderecoPorEnderecoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco)null);

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        // Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        // Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
