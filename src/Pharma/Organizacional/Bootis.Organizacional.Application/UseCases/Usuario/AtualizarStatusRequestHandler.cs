using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Application.Requests.Usuario.Atualizar;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Usuario;

public class AtualizarStatusLoteRequestHandler(
    IUnitOfWork unitOfWork,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<AtualizarStatusRequest>
{
    public async Task Handle(AtualizarStatusRequest request, CancellationToken cancellationToken)
    {
        foreach (var usuarioId in request.UsuariosId)
        {
            var usuario = await usuarioRepository.ObterUsuarioAsync(usuarioId);

            usuario.AtualizarStatus(request.Ativa);

            usuarioRepository.Update(usuario);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}