using System.Data;
using Bootis.Pessoa.Application.Requests.Fornecedor.Obter;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Fornecedor.Obter;

public class ObterDocumentoRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterDocumentoRequest, ObterDocumentoResponse>
{
    public async Task<ObterDocumentoResponse> Handle(ObterDocumentoRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT fd.id AS fornecedor_documento_id,
                                  fo.id AS fornecedor_id,
                                  fd.tipo_documento_id,
                                  fd.identificacao,
                                  fd.observacao
                             FROM fornecedor_documentos fd
                                  LEFT JOIN fornecedores fo ON fo.id = fd.fornecedor_id
                            WHERE fd.id = @id
                              AND fd.group_tenant_id = @groupTenantId;
                           """;

        var fornecedorDocumento = await connection.QueryAsync<ObterDocumentoResponse>(sql, new
        {
            id = request.FornecedorDocumentoId,
            groupTenantId = userContext.GroupTenantId
        });

        if (fornecedorDocumento == null || !fornecedorDocumento.Any())
            throw new DomainException(
                Localizer.Instance.GetMessage_FornecedorDocumento_GuidNaoEncontrado(request
                    .FornecedorDocumentoId));

        return fornecedorDocumento.First();
    }
}