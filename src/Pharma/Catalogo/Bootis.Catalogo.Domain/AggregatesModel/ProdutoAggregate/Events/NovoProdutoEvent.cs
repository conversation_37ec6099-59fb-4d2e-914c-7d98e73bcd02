using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate.Events;

public record class NovoProdutoEvent : IDomainEvent
{
    public string Descricao { get; set; }
    public decimal ValorVenda { get; set; }
    public decimal ValorCusto { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedoda { get; set; }
    public Guid Id { get; set; }
    public Guid GroupTenantId { get; set; }
    public Guid TenantId { get; set; }
}