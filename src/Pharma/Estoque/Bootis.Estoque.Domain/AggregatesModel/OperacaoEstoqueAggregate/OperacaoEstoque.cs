using Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;

public class OperacaoEstoque : Entity, IAggregateRoot
{
    public OperacaoEstoque()
    {
    }

    public OperacaoEstoque(string descricao,
        TipoOperacao tipoOperacao,
        bool seAquisicao,
        bool seProjecao,
        bool sePerda,
        bool seInventario)
    {
        Descricao = descricao;
        TipoOperacao = tipoOperacao;
        SeAquisicao = seAquisicao;
        SeProjecao = seProjecao;
        SePerda = sePerda;
        SeInventario = seInventario;
    }

    public string Descricao { get; private set; }
    public TipoOperacao TipoOperacao { get; private set; }
    public bool SeAquisicao { get; private set; }
    public bool SeProjecao { get; private set; }
    public bool SePerda { get; private set; }
    public bool SeInventario { get; private set; }

    #region Navigation properties

    public virtual ICollection<MovimentoEstoque> MovimentosEstoque { get; set; } = new List<MovimentoEstoque>();

    #endregion
}