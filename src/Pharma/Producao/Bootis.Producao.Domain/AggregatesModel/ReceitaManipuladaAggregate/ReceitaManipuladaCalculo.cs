using System.ComponentModel.DataAnnotations.Schema;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public class ReceitaManipuladaCalculo
{
    protected ReceitaManipuladaCalculo()
    {
    }

    public ReceitaManipuladaCalculo(ReceitaManipuladaRastreioCalculo receitaManipuladaRastreioCalculo,
        Guid produtoId,
        Guid? loteId,
        decimal quantidadeCalculada,
        int unidadeMedidaCalculadaId,
        decimal? fatorEquivalencia,
        decimal? fatorDiluicaoFornecedor,
        decimal? fatorDiluicaoInterna,
        decimal? densidade,
        decimal? fatorConcentracaoAgua,
        decimal? fatorTotal,
        decimal? quantidadeVolume,
        int unidadeMedidaVolumeId,
        decimal valorCusto,
        decimal valorVenda,
        decimal margemLucro,
        decimal? fatorCorrecao, decimal? fatorSinonimo)
    {
        ReceitaManipuladaRastreioCalculo = receitaManipuladaRastreioCalculo;
        ProdutoId = produtoId;
        LoteId = loteId;
        QuantidadeCalculada = quantidadeCalculada;
        UnidadeMedidaCalculadaId = unidadeMedidaCalculadaId;
        FatorEquivalencia = fatorEquivalencia;
        FatorDiluicaoFornecedor = fatorDiluicaoFornecedor;
        FatorDiluicaoInterna = fatorDiluicaoInterna;
        Densidade = densidade;
        FatorConcentracaoAgua = fatorConcentracaoAgua;
        FatorTotal = fatorTotal;
        QuantidadeVolume = quantidadeVolume;
        UnidadeMedidaVolumeId = unidadeMedidaVolumeId;
        ValorCusto = valorCusto;
        ValorVenda = valorVenda;
        MargemLucro = margemLucro;
        FatorCorrecao = fatorCorrecao;
        FatorSinonimo = fatorSinonimo;
    }

    public Guid ReceitaManipuladaRastreioCalculoId { get; private set; }
    public virtual ReceitaManipuladaRastreioCalculo ReceitaManipuladaRastreioCalculo { get; private set; }
    public Guid ProdutoId { get; private set; }
    public virtual Produto Produto { get; private set; }
    public Guid? LoteId { get; private set; }
    public virtual Lote Lote { get; private set; }
    public decimal QuantidadeCalculada { get; private set; }
    public int UnidadeMedidaCalculadaId { get; private set; }
    public decimal? FatorEquivalencia { get; private set; }
    public decimal? FatorDiluicaoFornecedor { get; private set; }
    public decimal? FatorDiluicaoInterna { get; private set; }
    public decimal? Densidade { get; private set; }
    public decimal? FatorConcentracaoAgua { get; private set; }
    public decimal? FatorTotal { get; private set; }
    public decimal? QuantidadeVolume { get; private set; }
    public int UnidadeMedidaVolumeId { get; private set; }
    public decimal ValorCusto { get; private set; }
    public decimal ValorVenda { get; private set; }
    public decimal MargemLucro { get; private set; }

    [NotMapped] public decimal? FatorCorrecao { get; private set; }

    [NotMapped] public decimal? FatorSinonimo { get; private set; }

    public void Atualizar(decimal quantidadeCalculada, int unidadeMedidaId, decimal volume,
        decimal valorCustoItem, decimal valorVendaItem, ReceitaManipuladaRastreioCalculo.FatoresTotais? fatores = null)
    {
        QuantidadeCalculada = quantidadeCalculada;
        UnidadeMedidaCalculadaId = unidadeMedidaId;
        QuantidadeVolume = volume;
        ValorCusto = valorCustoItem;
        ValorVenda = valorVendaItem;
        MargemLucro = valorVendaItem - valorCustoItem;

        if (!fatores.HasValue) return;
        FatorEquivalencia = fatores.Value.FatorEquivalencia;
        FatorDiluicaoFornecedor = fatores.Value.FatorFornecedor;
        FatorDiluicaoInterna = fatores.Value.FatorDiluicaoInterna;
        Densidade = fatores.Value.Densidade;
        FatorConcentracaoAgua = fatores.Value.FatorConcentracaoAgua;
        FatorTotal = fatores.Value.FatorTotal;
    }
}