using Bootis.Compra.Domain.Enumerations;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Venda.Domain.Enumerations;

namespace Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;

public class PedidoVendaItemReceitaManipulada : PedidoVendaItem
{
    public PedidoVendaItemReceitaManipulada()
    {
    }

    public PedidoVendaItemReceitaManipulada(ReceitaManipulada receitaManipulada, decimal quantidade,
        string descricao, decimal valorUnitario, decimal valorDesconto, decimal valorTotalItem,
        TipoDesconto tipoDesconto, StatusVendaItem status, bool selecionado) : base(descricao,
        quantidade, valorUnitario, valorDesconto, valorTotalItem, tipoDesconto, status,
        selecionado, TipoItemVenda.ReceitaManipulada)
    {
        ReceitaManipulada = receitaManipulada;
    }

    public Guid ReceitaManipuladaId { get; private set; }

    #region Navigation properties

    public virtual PedidoVendaItem PedidoVendaItem { get; set; }
    public virtual ReceitaManipulada ReceitaManipulada { get; set; }

    #endregion
}