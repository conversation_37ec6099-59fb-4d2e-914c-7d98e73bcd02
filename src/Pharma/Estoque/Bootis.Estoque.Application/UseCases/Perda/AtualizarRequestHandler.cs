using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.Perda.Atualizar;
using Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate;
using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Perda;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork,
    IPerdaRepository perdaRepository,
    IMotivoPerdaRepository motivoPerdaRepository)
    : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        var perda = await perdaRepository.ObterPerdaAsync(request.PerdaId);
        var motivoPerda = await motivoPerdaRepository.ObterMotivoPerdaAsync(request.MotivoPerdaId);

        perda.AtualizarPerda(request.DataPerda, motivoPerda, request.Observacao);

        perdaRepository.Update(perda);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}