using Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;
using Bootis.Producao.Application.Services.Interfaces;
using Bootis.Shared.Common.Interfaces;
using MediatR;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class ValidarIncompatibilidadesRequestHandler(IValidarIncompatibilidadeAppService validadorIncompatibilidade)
    : IRequestHandler<ValidarIncompatibilidadesRequest, IncompatibilidadeResponse?>, IScopedService
{
    public async Task<IncompatibilidadeResponse?> <PERSON>le(ValidarIncompatibilidadesRequest request, CancellationToken cancellationToken)
    {
        return await validadorIncompatibilidade.ObterCenariosIncompatibilidadeAsync(request.ProdutoIds);
    }
}