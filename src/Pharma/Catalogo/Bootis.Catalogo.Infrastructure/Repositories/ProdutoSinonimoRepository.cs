using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Catalogo.Infrastructure.Repositories;

public class ProdutoSinonimoRepository(IUserContext userContext, IDbContext context)
    : Repository<ProdutoSinonimo>(context), IProdutoSinonimoRepository
{
    public Task<ProdutoSinonimo> ObterPorIdAsync(Guid id)
    {
        return DbSet
            .SingleOrDefaultAsync(c => c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<Guid> ObterSinonimoIdPorDescricao(string sinonimo)
    {
        const string sql = """
                           SELECT ps.id
                           FROM produtos_sinonimo ps
                           WHERE ps.sinonimo = @sinonimo
                             AND ps.group_tenant_id = @GroupTenantId;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<Guid>(sql,
            new { sinonimo, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarPorDescricaoAsync(string sinonimo)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM produtos_sinonimo ps
                                                     WHERE ps.sinonimo = @sinonimo
                                                       AND ps.group_tenant_id = @groupTenantId) 
                                      OR EXISTS (SELECT 1 
                                                 FROM produtos pro
                                                 WHERE pro.descricao = @sinonimo
                                                   AND pro.group_tenant_id = @groupTenantId) 
                                      THEN 1 
                                      ELSE 0 
                                  END AS ExisteRegistro;

                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { sinonimo, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarPorIdEDescricaoAsync(Guid id, string sinonimo)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM produtos_sinonimo ps
                                                     WHERE ps.id = @id
                                                       AND ps.sinonimo = @sinonimo
                                                       AND ps.group_tenant_id = @groupTenantId) 
                                      THEN 1 
                                      ELSE 0 
                                  END AS ExisteRegistro;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { id, sinonimo, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarProdutoVinculadoAsync(Guid produtoId, Guid produtoSinonimoId)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM produtos_sinonimo ps
                                                     JOIN produtos pro ON ps.produto_id = pro.id
                                                     WHERE ps.id = @produtoSinonimoId
                                                       AND pro.id = @produtoId
                                                       AND ps.group_tenant_id = @groupTenantId) 
                                      THEN 1 
                                      ELSE 0 
                                  END AS ExisteVinculo;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { produtoId, produtoSinonimoId, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }
}