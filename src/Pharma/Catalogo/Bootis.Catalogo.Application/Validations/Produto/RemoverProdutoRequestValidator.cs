using Bootis.Catalogo.Application.Requests.Produto.Remover;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.Produto;

public class RemoverProdutoRequestValidator : AbstractValidator<RemoverRequest>
{
    public RemoverProdutoRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(f => f.ProdutosId)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ProdutosId)));
    }
}