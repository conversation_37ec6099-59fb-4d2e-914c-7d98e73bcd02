using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Producao.Infrastructure.EntityConfigurations;

public class ReceitaManipuladaValoresEntityTypeConfiguration : IEntityTypeConfiguration<ReceitaManipuladaValores>
{
    public void Configure(EntityTypeBuilder<ReceitaManipuladaValores> builder)
    {
        builder.ToTable("receitas_manipuladas_valores");

        builder.<PERSON><PERSON><PERSON>(c => c.ReceitaManipuladaId);

        builder.HasOne(c => c.ReceitaManipulada)
            .WithOne(c => c.Valores)
            .HasForeignKey<ReceitaManipuladaValores>(c => c.ReceitaManipuladaId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}