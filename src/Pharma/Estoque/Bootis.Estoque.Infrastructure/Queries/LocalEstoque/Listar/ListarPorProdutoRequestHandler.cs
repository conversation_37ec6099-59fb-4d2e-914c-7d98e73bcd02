using System.Data;
using Bootis.Estoque.Application.Requests.LocalEstoque.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.LocalEstoque.Listar;

public class
    ListarPorProdutoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarPorProdutoRequest, PaginatedResult<ListarPorProdutoResponse>>
{
    public Task<PaginatedResult<ListarPorProdutoResponse>> Handle(ListarPorProdutoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT l.descricao,
                                  l.id,
                                  emp.nome_fantasia AS nome_empresa,
                                  CASE 
                                      WHEN EXISTS (
                                          SELECT 1
                                          FROM saldos_estoque se
                                          INNER JOIN produtos prod ON prod.id = se.produto_id 
                                          WHERE l.id = se.local_estoque_id 
                                          AND prod.id = @ProdutoId
                                      ) THEN 1
                                      ELSE 0
                                  END AS vinculo_produto
                           FROM locais_estoque l
                           LEFT JOIN empresas emp ON emp.id = l.empresa_id
                           WHERE l.ativo = true
                             AND l.group_tenant_id = @GroupTenantId 
                             !@SEARCH_CONDITION@!
                           """;

        var searchLocal = new StringSearchField
        {
            Field = "L.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarPorProdutoRequest, ListarPorProdutoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchLocal)
            .ExecuteAsync();
    }
}