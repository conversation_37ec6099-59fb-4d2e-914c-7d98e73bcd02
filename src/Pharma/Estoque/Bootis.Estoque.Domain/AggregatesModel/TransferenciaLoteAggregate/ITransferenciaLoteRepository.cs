using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;

public interface ITransferenciaLoteRepository : IRepository<TransferenciaLote>, IScopedService
{
    Task<TransferenciaLote> ObterPorIdAsync(Guid id);
    Task<TransferenciaLote> ObterPorIdEGroupTenantAsync(Guid id);
    Task<TransferenciaLote> ObterPorIdAsync(Guid id, Guid groupTenantId);
    Task<TransferenciaLoteItens> ObterItensPorIdAsync(Guid id);
    void Add(TransferenciaLoteItens transferenciaLoteItem);
    void Update(TransferenciaLoteItens transferenciaLoteItem);
    void Remove(TransferenciaLoteItens transferenciaLoteItem);
}