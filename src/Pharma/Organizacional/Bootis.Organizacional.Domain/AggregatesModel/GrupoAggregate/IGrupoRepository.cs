using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate;

public interface IGrupoRepository : IRepository<Grupo>, IScopedService
{
    IEnumerable<Usuario> ListarUsuarios(ICollection<Guid> usuariosId);
    Task<Grupo> ObterPorIdAsync(Guid id);
    Task<List<Grupo>> ObterPorIdsAsync(IEnumerable<Guid> ids);
    Task<bool> ExistByNome(string nome);
    Task<IEnumerable<(Guid Id, string Nome)>> VerificarDependenciaAsync(IEnumerable<Guid> ids);
    Task<IEnumerable<Grupo>> ObterTodosGruposAsync();
}