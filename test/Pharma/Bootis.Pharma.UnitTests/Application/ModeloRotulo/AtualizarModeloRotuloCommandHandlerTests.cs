using Bootis.Catalogo.Application.UseCases.ModeloRotulo;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Catalogo.UnitTests.Fixtures.ModeloRotulo;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Catalogo.UnitTests.Application.ModeloRotulo;

public class AtualizarModeloRotuloCommandHandlerTests : BaseTest
{
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<IModeloRotuloRepository> _modeloRotuloRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarModeloRotuloCommandHandlerTests()
    {
        _modeloRotuloRepository = new Mock<IModeloRotuloRepository>();
        _handler = new AtualizarRequestHandler(unitOfWork.Object, _modeloRotuloRepository.Object);
    }

    [Fact]
    public async Task AtualizarModeloRotulo_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarModeloRotuloCommandFake.CriarCommandValido();
        var modeloRotulo = new Mock<Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>();

        _modeloRotuloRepository.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(modeloRotulo.Object));

        _modeloRotuloRepository.Setup(l => l.ValidarDescricaoAsync(cmd.Descricao, cmd.TipoRotulo)).Returns(
            Task.FromResult(false));

        _modeloRotuloRepository.Setup(l => l.Update(modeloRotulo.Object))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _modeloRotuloRepository.Verify(
            l => l.Update(
                It.Is<Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>(m =>
                    m.Descricao == modeloRotulo.Object.Descricao)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarModeloRotuloGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarModeloRotuloCommandFake.CriarCommandValido();

        _modeloRotuloRepository.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult<Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>(default));


        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _modeloRotuloRepository.Verify(
            l => l.Update(
                It.Is<Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>(m =>
                    m.Descricao == cmd.Descricao)),
            Times.Never);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AtualizarModeloRotulo_DescricaoExistente_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarModeloRotuloCommandFake.CriarCommandValido();
        var modeloRotulo = new Mock<Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>();
        modeloRotulo.SetupGet(c => c.Id).Returns(1.ToGuid());

        _modeloRotuloRepository.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(modeloRotulo.Object));

        _modeloRotuloRepository.Setup(l => l.ValidarDescricaoAsync(cmd.Descricao, cmd.TipoRotulo)).Returns(
            Task.FromResult(true));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _modeloRotuloRepository.Verify(
            l => l.Update(
                It.Is<Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>(m =>
                    m.Descricao == cmd.Descricao)),
            Times.Never);
    }
}
