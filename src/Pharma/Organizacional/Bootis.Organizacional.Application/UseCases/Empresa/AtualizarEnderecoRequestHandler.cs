using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Application.Mappers;
using Bootis.Organizacional.Application.Requests.Empresa.Atualizar;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Empresa;

public class AtualizarEnderecoRequestHandler(
    IUnitOfWork unitOfWork,
    IEmpresaRepository empresaRepository)
    : IRequestHandler<AtualizarEnderecoRequest>
{
    public async Task Handle(AtualizarEnderecoRequest request, CancellationToken cancellationToken)
    {
        var empresa = await empresaRepository.ObterEmpresaAsync(request.Id);

        empresa.AtualizarEndereco(EnderecoMapper.EnderecoFrom(request.Endereco)!);

        empresaRepository.Update(empresa);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}