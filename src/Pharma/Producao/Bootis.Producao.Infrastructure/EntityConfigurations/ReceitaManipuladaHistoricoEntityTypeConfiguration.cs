using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Producao.Infrastructure.EntityConfigurations;

public class
    ReceitaManipuladaaHistoricoEntityTypeConfiguration : BaseEntityTypeConfiguration<ReceitaManipuladaHistorico>
{
    public override void Configure(EntityTypeBuilder<ReceitaManipuladaHistorico> builder)
    {
        builder.ToTable("receitas_manipuladas_historico");

        builder
            .Property(x => x.DataHora)
            .IsRequired();

        builder
            .Property(x => x.Status)
            .HasSentinel(0)
            .IsRequired();

        builder
            .Property(x => x.MotivoCancelamento)
            .HasMaxLength(500)
            .IsRequired(false);

        builder
            .HasOne(c => c.ReceitaManipulada)
            .WithMany(c => c.Historico)
            .HasForeignKey(x => x.ReceitaManipuladaId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        builder
            .HasOne(c => c.Usuario)
            .WithMany()
            .HasForeignKey(c => c.UsuarioId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();
    }
}