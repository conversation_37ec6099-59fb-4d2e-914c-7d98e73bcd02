using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.SubGrupo.Cadastrar;
using Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.SubGrupo;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork)
    : IRequestHandler<CadastrarRequest, CadastrarResponseSubGrupo>
{
    public async Task<CadastrarResponseSubGrupo> Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var subGrupoRepository = unitOfWork.GetRepository<ISubGrupoRepository>();
        var grupoRepository = unitOfWork.GetRepository<IGrupoRepository>();

        var grupo = await grupoRepository.ObterGrupoAsync(request.GrupoPaiId);

        await subGrupoRepository.ValidarSubGrupoPorDescricaoAsync(grupo.Id, request.Descricao);

        var subGrupo = new Domain.AggregatesModel.SubGrupoAggregate.SubGrupo(request.Descricao, grupo.Id);

        subGrupoRepository.Add(subGrupo);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

        return new CadastrarResponseSubGrupo
        {
            Id = subGrupo.Id,
            Descricao = subGrupo.Descricao
        };
    }
}