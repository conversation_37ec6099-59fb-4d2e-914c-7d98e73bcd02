namespace Bootis.Setup.Api.RequestHandlers.Pessoa;

public record PrescritorContato(
    Guid id,
    string PrescritorNomeCompleto,
    Guid TipoContatoId,
    string Identificacao,
    bool Principal,
    string Observacao);

public record MigratePrescritoresContato(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigratePrescritoresContatoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigratePrescritoresContato>(configuration)
{
    public override async Task Handle(MigratePrescritoresContato request, CancellationToken cancellationToken)
    {
        var dataTable =
            await GetDataFromSourceDatabase(request, "SELECT * FROM prescritores_contato", cancellationToken);

        await InsertOrUpdateData(dataTable, "prescritores_contato", cancellationToken);
    }
}