using Bootis.Setup.Api.Models.Producao;

namespace Bootis.Setup.Api.RequestHandlers.Producao;

public record MigrateCapsulasCor(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateCapsulasCorHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateCapsulasCor, CapsulaCor>(configuration)
{
    private static readonly CapsulaCor[] CapsulaCor =
    [
        new() { CorCapsula = "Amarelo/Preto", Transparente = false },
        new() { CorCapsula = "Azul Claro/Azul Escarlate", Transparente = false },
        new() { CorCapsula = "Azul Claro/Azul Escuro", Transparente = false },
        new() { CorCapsula = "Azul Claro/Branco", Transparente = false },
        new() { CorCapsula = "Azul/Azul", Transparente = false },
        new() { CorCapsula = "Azul/Branco", Transparente = false },
        new() { CorCapsula = "Azul/Cinza", Transparente = false },
        new() { CorCapsula = "Branco/Branco", Transparente = false },
        new() { CorCapsula = "Escarlate/Branco", Transparente = false },
        new() { CorCapsula = "Escarlate/Cinza", Transparente = false },
        new() { CorCapsula = "Escarlate/Escarlate", Transparente = false },
        new() { CorCapsula = "Incolor/Incolor", Transparente = true },
        new() { CorCapsula = "Laranja/Branco", Transparente = false },
        new() { CorCapsula = "Laranja/Laranja", Transparente = false },
        new() { CorCapsula = "Marrom", Transparente = false },
        new() { CorCapsula = "Preto/Preto", Transparente = false },
        new() { CorCapsula = "Rosa/Branco", Transparente = false },
        new() { CorCapsula = "Rosa/Rosa", Transparente = false },
        new() { CorCapsula = "Roxo/Branco", Transparente = false },
        new() { CorCapsula = "Verde Água/Branco", Transparente = false },
        new() { CorCapsula = "Verde Claro/Transparente", Transparente = true },
        new() { CorCapsula = "Verde Claro/Verde Escuro", Transparente = false },
        new() { CorCapsula = "Verde Escuro/Verde Escuro", Transparente = false },
        new() { CorCapsula = "Verde/Branco", Transparente = false },
        new() { CorCapsula = "Vermelho/Branco", Transparente = false },
        new() { CorCapsula = "Vermelho/Vermelho", Transparente = false }
    ];

    public override async Task Handle(MigrateCapsulasCor request, CancellationToken cancellationToken)
    {
        var data = GetData(request, cancellationToken);

        await InsertOrUpdateData(data, cancellationToken);
    }

    private IAsyncEnumerable<CapsulaCor> GetData(MigrateCapsulasCor request, CancellationToken cancellationToken)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM capsulas_cor",
                cancellationToken);
    }

    private static async IAsyncEnumerable<CapsulaCor> GetDataFromDefault(MigrateCapsulasCor request)
    {
        foreach (var capsulaCor in CapsulaCor)
            yield return capsulaCor with
            {
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId
            };

        await Task.CompletedTask;
    }
}