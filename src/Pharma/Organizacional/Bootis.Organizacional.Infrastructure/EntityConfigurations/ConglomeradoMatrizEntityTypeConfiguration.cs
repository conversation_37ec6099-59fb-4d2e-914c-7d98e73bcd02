using Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Organizacional.Infrastructure.EntityConfigurations;

public class ConglomeradoMatrizEntityTypeConfiguration : IEntityTypeConfiguration<ConglomeradoMatriz>
{
    public void Configure(EntityTypeBuilder<ConglomeradoMatriz> builder)
    {
        builder.ToTable("conglomerado_matriz");

        builder.<PERSON><PERSON><PERSON>("Id");

        builder
            .HasOne(c => c.Conglomerado)
            .WithOne(c => c.Matriz)
            .HasForeignKey<ConglomeradoMatriz>("Id")
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(m => m.Empresa)
            .WithOne()
            .HasForeignKey<ConglomeradoMatriz>(m => m.EmpresaId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}