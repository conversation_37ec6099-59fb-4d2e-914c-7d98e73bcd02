using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;

public abstract class UnidadeMedida
{
    public abstract UnidadeMedidaAbreviacao Abreviacao { get; }
    public abstract string Descricao { get; }
    public abstract bool Ativo { get; }
    public abstract bool UnidadeAlternativa { get; }
    public abstract bool UnidadeEstoque { get; }
    public abstract bool UnidadePrescricao { get; }
    public abstract bool UnidadePosologia { get; }
    public abstract TipoUnidade TipoUnidade { get; }
}