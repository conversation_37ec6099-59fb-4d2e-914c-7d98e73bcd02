using Bootis.Setup.Api.RequestHandlers.Estoque;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Setup.Api.Infrastructure.EntityConfigurations.Estoque;

public class ProdutoSinonimoConfiguration : IEntityTypeConfiguration<ProdutoSinonimo>
{
    public void Configure(EntityTypeBuilder<ProdutoSinonimo> builder)
    {
        builder.ToTable("produtos_sinonimo");

        builder.HasKey(x => x.Id);
    }
}