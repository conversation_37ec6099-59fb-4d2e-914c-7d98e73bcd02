using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate;

public interface IGrupoRepository : IRepository<Grupo>, IScopedService
{
    Task<Grupo> ObterPorIdAsync(Guid id);
    Task<List<Grupo>> ObterPorIdsAsync(IEnumerable<Guid> ids);
    Task<bool> ValidarPorDescricaoAsync(string descricao);
    Task<bool> ValidarPorIdEhDescricaoAsync(Guid id, string descricao);

    Task<IEnumerable<(Guid GrupoId, string GrupoDescricao, int TotalSubGrupos, int TotalProdutos)>>
        VerificarDependeciasGruposAsync(IEnumerable<Guid> ids);
}