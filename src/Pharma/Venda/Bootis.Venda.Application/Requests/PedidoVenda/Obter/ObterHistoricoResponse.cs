using System.ComponentModel;
using Bootis.Venda.Domain.Enumerations;

namespace Bootis.Venda.Application.Requests.PedidoVenda.Obter;

public class ObterHistoricoResponse
{
    public List<Historico> Historico { get; set; } = new();
}

public class Historico
{
    public Guid Id { get; set; }
    public DateTime Data { get; set; }
    public StatusVenda Status { get; set; }
    public bool Estorno { get; set; }

    public string StatusDescricao
    {
        get
        {
            var descriptionAttribute = typeof(StatusVenda)
                    .GetField(Status.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];
            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : Status.ToString();
        }
    }

    public string Vendedor { get; set; }

    public bool EhCriacao { get; set; }
}