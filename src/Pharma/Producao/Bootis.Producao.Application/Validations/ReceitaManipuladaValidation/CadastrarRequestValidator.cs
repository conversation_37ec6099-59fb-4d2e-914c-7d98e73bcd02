using Bootis.Producao.Application.Requests.ReceitaManipulada.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Producao.Application.Validations.ReceitaManipuladaValidation;

public class CadastrarRequestValidator : AbstractValidator<CadastrarReceitaManipuladaRequest>
{
    public CadastrarRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.PedidoVendaId)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.PedidoVendaId)));
        
        RuleFor(c => c.FormaFarmaceuticaId)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.FormaFarmaceuticaId)));

        RuleFor(c => c.QuantidadeReceita)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.QuantidadeReceita)));

        RuleFor(c => c.PacienteId)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.PacienteId)));

        RuleForEach(c => c.Componentes)
            .SetValidator(new CadastrarReceitaItemRequestValidator(localizer));
    }
}

public class CadastrarReceitaItemRequestValidator : AbstractValidator<CadastrarReceitaItemRequest>
{
    public CadastrarReceitaItemRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.ProdutoId)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.ProdutoId)));

        RuleFor(c => c.UnidadeMedidaId)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.UnidadeMedidaId)));

        RuleFor(c => c.Quantidade)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Quantidade)));

        RuleFor(c => c.TipoQuantificacao)
            .IsInEnum()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.TipoQuantificacao)));
    }
}