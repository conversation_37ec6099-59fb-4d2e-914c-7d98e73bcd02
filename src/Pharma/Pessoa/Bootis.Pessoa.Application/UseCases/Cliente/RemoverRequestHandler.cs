using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Cliente.Remover;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Cliente;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork,
    IClienteRepository clienteRepository) : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var clientes = await clienteRepository.ObterClientesAsync(request.ClientesId);

        await clienteRepository.VerificarDependenciasAsync(clientes);

        foreach (var id in request.ClientesId)
        {
            var cliente = clientes.Single(c => c.Id == id);

            clienteRepository.Remove(cliente);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}