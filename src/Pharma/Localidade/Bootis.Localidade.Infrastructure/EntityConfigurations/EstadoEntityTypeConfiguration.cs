using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Localidade.Infrastructure.EntityConfigurations;

public class EstadoEntityTypeConfiguration : BaseEntityTypeConfiguration<Estado>
{
    public override void Configure(EntityTypeBuilder<Estado> builder)
    {
        builder.ToTable("estados");

        builder
            .Property(c => c.Descricao)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired();

        builder
            .Property(c => c.Abreviacao)
            .Documento(TamanhoTexto.Dez)
            .IsRequired();

        builder.HasOne(c => c.Pais)
            .WithMany(c => c.<PERSON>)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}