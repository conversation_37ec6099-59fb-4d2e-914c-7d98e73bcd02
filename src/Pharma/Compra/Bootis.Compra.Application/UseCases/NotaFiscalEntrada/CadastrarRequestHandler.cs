using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.NcmAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Compra.Application.Extensions;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Cadastrar;
using Bootis.Compra.Domain.AggregatesModel.NaturezaOperacaoAggregate;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate;
using Bootis.Compra.Domain.AggregatesModel.TipoFreteAggregate;
using Bootis.Compra.Domain.Enumerations;
using Bootis.Compra.Resources;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.ValueObjects.Responses;
using MediatR;

namespace Bootis.Compra.Application.UseCases.NotaFiscalEntrada;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext)
    : IRequestHandler<CadastrarRequest, CadastrarGlobalResponse>
{
    private readonly IFornecedorRepository _fornecedorRepository =
        unitOfWork.GetRepository<IFornecedorRepository>();

    private readonly INcmRepository _ncmRepository =
        unitOfWork.GetRepository<INcmRepository>();

    private readonly INotaFiscalEntradaRepository _notaFiscalEntradaRepository =
        unitOfWork.GetRepository<INotaFiscalEntradaRepository>();

    private readonly IPedidoCompraRepository _pedidoCompraRepository =
        unitOfWork.GetRepository<IPedidoCompraRepository>();

    private readonly IProdutoRepository _produtoRepository = unitOfWork.GetRepository<IProdutoRepository>();
    private readonly IUsuarioRepository _usuarioRepository = unitOfWork.GetRepository<IUsuarioRepository>();

    public async Task<CadastrarGlobalResponse> Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var fornecedor = await _fornecedorRepository.ObterFornecedorAsync(request.FornecedorId);

        await _notaFiscalEntradaRepository.ValidarNotaFiscalPorNumeroEhSerieEhFornecedorIdAsync(
            request.Numero, request.Serie, fornecedor.Id);

        var usuario = await _usuarioRepository.ObterUsuarioAsync(userContext.UserId);
        var naturezaOperacao =
            await _notaFiscalEntradaRepository.ObterNaturezaOperacaoAsync(request.NaturezaOperacaoId);
        var tipoFrete = await _notaFiscalEntradaRepository.ObterTipoFreteAsync(request.TipoFreteId);
        var transportadora = await _fornecedorRepository.ObterFornecedorAsync(request.TransportadoraId);
        var produtos = await _produtoRepository.ObterProdutosAsync(request.Itens
            .Select(c => c.ProdutoId));
        var cfops = await _notaFiscalEntradaRepository.ObterCfopsAsync(request.Itens
            .Select(c => c.CfopId));
        var ncms = await _ncmRepository.ObterNcmsAsync(request.Itens
            .Select(c => c.NcmId));
        var csts = await _notaFiscalEntradaRepository.ObterCstsAsync(request.Itens
            .Select(c => c.CstCsosnId));

        Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada notaFiscalEntrada;

        if (request.NotaFiscalEntradaId.HasValue)
        {
            notaFiscalEntrada = await _notaFiscalEntradaRepository.ObterNotaFiscalEntradaAsync(
                request.NotaFiscalEntradaId.Value);

            if (notaFiscalEntrada.Status != StatusNotaFiscal.Rascunho)
                throw new ValidationException(nameof(notaFiscalEntrada.Id),
                    Localizer.Instance.GetMessage_NotaFiscalEntrada_JaLancada());

            notaFiscalEntrada.AtualizarNotaFiscalEntrada(request, fornecedor, tipoFrete, naturezaOperacao,
                transportadora);
            notaFiscalEntrada.AtualizarStatus(StatusNotaFiscal.Pendente, usuario);
            notaFiscalEntrada.AtualizarNotaFiscalEntradaItem(request.Itens, produtos, ncms, cfops, csts);

            _notaFiscalEntradaRepository.Update(notaFiscalEntrada);
        }
        else
        {
            notaFiscalEntrada = await AdicionarNotaFiscalEntradaAsync(request, fornecedor, tipoFrete, naturezaOperacao,
                transportadora, usuario);

            foreach (var itemRequest in request.Itens)
            {
                var produto = produtos.Single(c => c.Id == itemRequest.ProdutoId);
                var ncm = ncms.Single(c => c.Id == itemRequest.NcmId);
                var cfop = cfops.Single(c => c.Id == itemRequest.CfopId);
                var cst = csts.Single(c => c.Id == itemRequest.CstCsosnId);

                notaFiscalEntrada.CadastrarNotaFiscalEntradaItem(itemRequest, produto, ncm, cfop, cst);
            }
        }

        notaFiscalEntrada.TotalizarNotaFiscal();

        if (request.PedidosId is not null && request.PedidosId.Any())
            await VincularPedidosCompra(request, notaFiscalEntrada);

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return new CadastrarGlobalResponse
        {
            Id = notaFiscalEntrada.Id
        };
    }

    private Task<Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada> AdicionarNotaFiscalEntradaAsync(
        CadastrarRequest request,
        Fornecedor fornecedor, TipoFrete tipoFrete, NaturezaOperacao naturezaOperacao, Fornecedor transportadora,
        Usuario usuario)
    {
        var notaFiscalEntrada = new Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada(request,
            naturezaOperacao,
            fornecedor,
            transportadora,
            tipoFrete);

        notaFiscalEntrada.AtualizarStatus(StatusNotaFiscal.Pendente, usuario);

        _notaFiscalEntradaRepository.Add(notaFiscalEntrada);

        return Task.FromResult(notaFiscalEntrada);
    }

    private async Task VincularPedidosCompra(CadastrarRequest request,
        Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada notaFiscalEntrada)
    {
        foreach (var pedidoCompraId in request.PedidosId)
        {
            var pedidoCompra = await _pedidoCompraRepository.ObterPedidoCompraAsync(pedidoCompraId);

            var notaFiscalEntradaPedidoCompra = notaFiscalEntrada.VincularPedidoCompra(pedidoCompra);

            _notaFiscalEntradaRepository.Add(notaFiscalEntradaPedidoCompra);
        }
    }
}