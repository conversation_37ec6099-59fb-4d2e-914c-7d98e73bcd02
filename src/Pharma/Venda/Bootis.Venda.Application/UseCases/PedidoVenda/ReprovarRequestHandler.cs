using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using Bootis.Venda.Application.Extensions;
using Bootis.Venda.Application.Requests.PedidoVenda.Atualizar;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using MediatR;

namespace Bootis.Venda.Application.UseCases.PedidoVenda;

public class ReprovarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IPedidoVendaRepository pedidoVendaRepository,
    IReceitaManipuladaRepository receitaManipuladaRepository)
    : IRequestHandler<ReprovarRequest>
{
    public async Task Handle(ReprovarRequest request, CancellationToken cancellationToken)
    {
        var pedidoVenda = await pedidoVendaRepository.ObterPedidoVendaAsync(request.PedidoVendaId);

        pedidoVenda.Reprovar(userContext.UserId);

        await ReprovarReceitasPorPedidoVendaAsync(pedidoVenda, userContext.UserId);

        pedidoVendaRepository.Update(pedidoVenda);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
    
    
    private async Task ReprovarReceitasPorPedidoVendaAsync(Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda pedidoVenda, Guid userId)
    {
        var itensReceitaManipulada = pedidoVenda.Itens
            .OfType<PedidoVendaItemReceitaManipulada>()
            .ToList();

        if (itensReceitaManipulada.Count is 0)
            return;
    
        var receitaIds = itensReceitaManipulada
            .Select(i => i.ReceitaManipuladaId)
            .Distinct()
            .ToList();

        var receitasManipuladas = await receitaManipuladaRepository.ObterReceitasManipuladasAsync(receitaIds);

        foreach (var receita in receitasManipuladas)
        {
            receita.AtualizarStatus(StatusReceita.Rejeitada, userId);
            receitaManipuladaRepository.Update(receita);
        }
    }
}