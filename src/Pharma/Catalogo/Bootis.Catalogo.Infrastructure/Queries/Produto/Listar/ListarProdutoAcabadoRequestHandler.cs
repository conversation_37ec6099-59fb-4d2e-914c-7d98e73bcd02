using System.Data;
using Bootis.Catalogo.Application.Requests.Produto.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Produto.Listar;

public class ListarProdutoAcabadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarProdutoAcabadoRequest,
        PaginatedResult<ListarProdutoAcabadoResponse>>
{
    public Task<PaginatedResult<ListarProdutoAcabadoResponse>> Handle(ListarProdutoAcabadoRequest request,
        CancellationToken cancellationToken)
    {
        var sql = """
                   SELECT 
                         pro.descricao,
                         pro.id,
                         pro.sequencia_group_tenant AS codigo,
                         SUM(se.saldo) AS saldo_estoque,
                         un.abreviacao AS unidade_estoque
                    FROM produtos pro
                         LEFT JOIN saldos_estoque se ON
                                   se.produto_id = pro.id
                         LEFT JOIN unidades_medida un ON
                                   un.id = pro.unidade_estoque_id
                   WHERE pro.classe_produto_id = 3
                     AND pro.group_tenant_id = @GroupTenantId
                     AND un.unidade_estoque = true
                     AND pro.ativo = true
                     !@SEARCH_CONDITION@!
                   GROUP BY pro.id,
                            pro.descricao,
                            pro.sequencia_group_tenant,
                            un.abreviacao
                  """;

        var searchProduto = new StringSearchField
        {
            Field = "PRO.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchCodigo = new NumberSearchField
        {
            Field = "PRO.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarProdutoAcabadoRequest, ListarProdutoAcabadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchProduto)
            .AddSearchField(searchCodigo)
            .ExecuteAsync();
    }
}