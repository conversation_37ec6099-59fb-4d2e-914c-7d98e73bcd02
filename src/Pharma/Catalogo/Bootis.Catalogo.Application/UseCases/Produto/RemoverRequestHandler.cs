using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.Produto.Remover;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.Produto;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork) : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var produtoRepository = unitOfWork.GetRepository<IProdutoRepository>();

        var produtos = await produtoRepository.ObterProdutosIncludeAsync(request.ProdutosId);

        await produtoRepository.VerificarDependenciasAsync(produtos);

        foreach (var produtoId in request.ProdutosId)
        {
            var produto = produtos.Single(p => p.Id == produtoId);

            produto.Remove();
            produtoRepository.Remove(produto);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}