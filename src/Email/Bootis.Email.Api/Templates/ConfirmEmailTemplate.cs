using Bootis.Email.Api.Models;
using Bootis.Shared;
using Bootis.Shared.Common;
using Newtonsoft.Json;
using SendGrid.Helpers.Mail;

namespace Bootis.Email.Api.Templates;

public class ConfirmEmailTemplate(IConfiguration configuration) : ITemplate
{
    public string TemplateName => "ConfirmEmail";

    public void SetTemplate(SendGridMessage sendGridMessage, SendEmail email)
    {
        sendGridMessage.SetTemplateId(configuration[$"Templates:{TemplateName}"]);

        TemplateNewPassword template = new();
        foreach (var to in email.Tos)
        {
            template.Name = email.Properties["UserName"];
            template.Email = to.Email;
            template.Link = email.Properties["UrlConfirmEmail"];

            template.TemplateEmail = new TemplateEmail
            {
                Subject = Localizer.Instance.GetSubjectConfirmEmail(email.Properties["UserName"]),
                Welcome = Localizer.Instance.GetWelcomeConfirmEmailMessage(),
                RecoverDescriptionInitial = Localizer.Instance.GetRecoverDescriptionInitialConfirmEmailMessage(),
                RecoverDescriptionEnd = Localizer.Instance.GetRecoverDescriptionEndConfirmEmailMessage(),
                BtnTitle = Localizer.Instance.GetBtnTitleConfirmEmailMessage(),
                LinkDescription = Localizer.Instance.GetLinkDescriptionConfirmEmailMessage(),
                Help = Localizer.Instance.GetHelpConfirmEmailMessage()
            };

            sendGridMessage.SetTemplateData(template);
        }
    }

    public class TemplateNewPassword
    {
        [JsonProperty("name")] public string Name { get; set; }

        [JsonProperty("email")] public string Email { get; set; }

        [JsonProperty("link")] public string Link { get; set; }

        [JsonProperty("t")] public TemplateEmail TemplateEmail { get; set; }
    }

    public class TemplateEmail
    {
        [JsonProperty("subject")] public string Subject { get; set; }

        [JsonProperty("welcome")] public string Welcome { get; set; }

        [JsonProperty("recoverDescriptionInitial")]
        public string RecoverDescriptionInitial { get; set; }

        [JsonProperty("recoverDescriptionEnd")]
        public string RecoverDescriptionEnd { get; set; }

        [JsonProperty("btnTitle")] public string BtnTitle { get; set; }

        [JsonProperty("linkDescription")] public string LinkDescription { get; set; }

        [JsonProperty("help")] public string Help { get; set; }
    }
}