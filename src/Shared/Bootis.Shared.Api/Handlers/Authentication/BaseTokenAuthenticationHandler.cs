using System.Security.Claims;
using System.Text.Encodings.Web;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Security;
using Bootis.Shared.Common.ValueObjects.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Bootis.Shared.Api.Handlers.Authentication;

public abstract class BaseTokenAuthenticationHandler(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory loggerFactory,
    UrlEncoder encoder,
    IIdentityClient identityClient)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, loggerFactory, encoder)
{
    protected async Task<AuthenticateResult> IntrospectAsync(string token, IntrospectType introspectType)
    {
        Logger.LogDebug("Validating token with Identity service");
        var validationResult = await identityClient.IntrospectAsync(token, introspectType);

        if (validationResult.IsT1)
        {
            var failure = validationResult.AsT1;
            return CreateFailureResult(failure.ErrorCode, failure.ErrorMessage);
        }

        var successData = validationResult.AsT0;

        var userIdentity = CreateClaimsIdentity(successData);
        var userPrincipal = new ClaimsPrincipal(userIdentity);

        SetUserContext(successData);

        Logger.LogDebug("Authentication successful for user {UserId}",
            successData.UserSession.UserIdentity.UserId);
        return AuthenticateResult.Success(new AuthenticationTicket(userPrincipal, Scheme.Name));
    }

    protected ClaimsIdentity CreateClaimsIdentity(TokenValidationSuccess successData)
    {
        var userIdentity = new ClaimsIdentity(Scheme.Name);

        userIdentity.AddClaim(new Claim(ClaimTypes.NameIdentifier,
            successData.UserSession.UserIdentity.UserId.ToString()));
        userIdentity.AddClaim(new Claim(ClaimTypes.Name, successData.UserSession.UserIdentity.Name));
        userIdentity.AddClaim(new Claim(ClaimTypes.Email, successData.UserSession.UserIdentity.Email));
        userIdentity.AddClaim(new Claim(ClaimTypes.Expiration, successData.ExpirationTimestamp.ToString()));
        userIdentity.AddClaim(
            new Claim(CustomClaims.TenantId, successData.UserSession.UserIdentity.TenantId.ToString()));
        userIdentity.AddClaim(new Claim(CustomClaims.GroupTenantId,
            successData.UserSession.UserIdentity.GroupTenantId.ToString()));
        userIdentity.AddClaim(new Claim(CustomClaims.SessionId, successData.UserSession.SessionId.ToString()));
        userIdentity.AddClaim(new Claim(CustomClaims.Language, successData.UserSession.UserPreferences.Language));
        userIdentity.AddClaim(new Claim(CustomClaims.TimeZone, successData.UserSession.UserPreferences.TimeZone));
        userIdentity.AddClaim(new Claim(CustomClaims.CurrencyId,
            successData.UserSession.UserPreferences.CurrencyTypeId.ToString()));

        foreach (var role in successData.UserSession.UserIdentity.Roles)
            userIdentity.AddClaim(new Claim(ClaimTypes.Role, role.ToString()));

        foreach (var (key, value) in successData.UserSession.UserIdentity.CustomClaims)
            userIdentity.AddClaim(new Claim(key, value));

        return userIdentity;
    }


    protected AuthenticateResult CreateFailureResult(AuthenticationError errorCode, string details = null)
    {
        Context.Items["AuthFailureReason"] = errorCode.ToString();
        Logger.LogDebug("Authentication failed: {ErrorCode} - {Details}", errorCode, details);
        return AuthenticateResult.Fail($"{errorCode}: {details ?? errorCode.ToString()}");
    }


    protected void SetUserContext(TokenValidationSuccess successData)
    {
        try
        {
            var userContext = Context.RequestServices.GetRequiredService<IUserContext>();
            userContext.UserSession = successData.UserSession;
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to set user context");
        }
    }
}