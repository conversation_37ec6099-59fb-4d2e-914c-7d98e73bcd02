using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.TipoClasseProdutoAggregate.Specializations;

public class TipoClasseProdutoUsoConsumo : TipoClasseProduto
{
    public override UnidadeMedidaAbreviacao UnidadeCalculo => UnidadeMedidaAbreviacao.un;
    public override UnidadeMedidaAbreviacao UnidadePadraoVisualizacao => UnidadeMedidaAbreviacao.un;
    public override bool ControlaEstoque => false;

    public override List<UnidadeMedidaAbreviacao> UnidadesVisualizacao =>
        new() { UnidadeMedidaAbreviacao.un, UnidadeMedidaAbreviacao.Mil };

    public override TipoClasseProdutoAbreviacao Abreviacao => TipoClasseProdutoAbreviacao.UsoConsumo;
}