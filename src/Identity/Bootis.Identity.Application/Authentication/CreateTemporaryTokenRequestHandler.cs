using Bootis.Identity.Application.Authentication.Models;
using Bootis.Identity.Domain.Entities;
using Bootis.Shared.Application.Interfaces;
using MediatR;
using UUIDNext;

namespace Bootis.Identity.Application.Authentication;

public class
    CreateTemporaryTokenRequestHandler(IUnitOfWork unitOfWork, IUserContext userContext)
    : IRequestHandler<CreateTemporaryTokenRequest, CreateTemporaryTokenResponse>
{
    public async Task<CreateTemporaryTokenResponse> Handle(CreateTemporaryTokenRequest request,
        CancellationToken cancellationToken)
    {
        var temporaryToken = new TemporaryLogin
        {
            Id = Uuid.NewSequential(),
            SessionId = userContext.SessionId,
            UserId = userContext.UserId,
            TenantId = userContext.TenantId,
            GroupTenantId = userContext.GroupTenantId,
            CreatedAt = DateTimeOffset.UtcNow,
            ExpiresAt = DateTimeOffset.UtcNow.AddMinutes(15)
        };

        unitOfWork.Add(temporaryToken);

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return temporaryToken;
    }
}