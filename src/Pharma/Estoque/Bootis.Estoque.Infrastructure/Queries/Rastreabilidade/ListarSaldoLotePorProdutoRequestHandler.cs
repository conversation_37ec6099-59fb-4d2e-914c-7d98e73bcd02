using System.Data;
using Bootis.Estoque.Application.Requests.Rastreabilidade.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Rastreabilidade;

public class
    ListarSaldoLotePorProdutoRequestHandler(
        IUserContext userContext,
        IDbConnection connection)
    : IRequestHandler<ListarSaldoLotePorProdutoRequest,
        ListarSaldoLotePorProdutoResponse>
{
    public async Task<ListarSaldoLotePorProdutoResponse> Handle(ListarSaldoLotePorProdutoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT l.id as lote_id,
                                  l.numero as lote_numero,
                                  f.nome as fornecedor_descricao,
                                  l.data_lancamento as data_entrada,
                                  l.data_validade,
                                  l.situacao,
                                  SUM(COALESCE(s.saldo, 0)) as saldo,
                                  COALESCE(s.unidade_medida_id, p.unidade_estoque_id) as unidade_medida_id,
                                  um.abreviacao as unidade_medida
                             FROM lotes l
                                  LEFT JOIN saldos_estoque s ON s.lote_id = l.id
                                  LEFT JOIN fornecedores f ON f.id = l.fornecedor_id
                                  LEFT JOIN produtos p ON p.id = l.produto_id
                                  LEFT JOIN unidades_medida um ON um.id = p.unidade_estoque_id 
                            WHERE l.group_tenant_id = @GroupTenantId
                              AND p.id = @ProdutoId
                              !@SEARCH_CONDITION@!
                           GROUP BY l.id, l.numero, f.nome, l.data_lancamento, l.data_validade, l.situacao, COALESCE(s.unidade_medida_id, p.unidade_estoque_id), um.abreviacao
                           """;

        var searchUnidade = new StringSearchField
        {
            Field = "um.abreviacao",
            CompareType = StringCompareType.Contains
        };

        var searchLoteNumero = new StringSearchField
        {
            Field = "l.numero",
            CompareType = StringCompareType.Contains
        };

        var searchFornecedor = new StringSearchField
        {
            Field = "f.nome",
            CompareType = StringCompareType.Contains
        };

        var searchDataLancamento = new DateSearchField
        {
            Field = "l.data_lancamento",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchDataValidade = new DateSearchField
        {
            Field = "l.data_validade",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var response = await PaginatedQueryBuilder<ListarSaldoLotePorProdutoRequest, ListarSaldos>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchLoteNumero)
            .AddSearchField(searchFornecedor)
            .AddSearchField(searchDataValidade)
            .AddSearchField(searchDataLancamento)
            .AddSearchField(searchUnidade)
            .ExecuteAsync();

        if (!response.Data.Any())
            return new ListarSaldoLotePorProdutoResponse();

        return new ListarSaldoLotePorProdutoResponse
        {
            TotalSaldo = response.Data.Sum(x => x.Saldo),
            UnidadeMedida = response.Data.First().UnidadeMedida,
            Saldos = response
        };
    }
}