using Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Compra.Domain.ValuesObject;

public class NotaFiscalEntradaLoteUnidadeAlternativa(
    UnidadeMedidaAbreviacao? unidadeAlternativaId,
    UnidadeMedidaAbreviacao? unidadeAlternativaConversaoId,
    decimal? quantidadeUnidadeAlternativa)
    : ValueObject
{
    public NotaFiscalEntradaLoteUnidadeAlternativa(NotaFiscalEntradaLoteUnidadeAlternativaDto loteUnidadeAlternativaDto)
        : this(loteUnidadeAlternativaDto.UnidadeAlternativaId, loteUnidadeAlternativaDto.UnidadeAlternativaConversaoId,
            loteUnidadeAlternativaDto.QuantidadeUnidadeAlternativa)
    {
    }

    public UnidadeMedidaAbreviacao? UnidadeAlternativaId { get; init; } = unidadeAlternativaId;
    public UnidadeMedidaAbreviacao? UnidadeAlternativaConversaoId { get; init; } = unidadeAlternativaConversaoId;
    public decimal? QuantidadeUnidadeAlternativa { get; init; } = quantidadeUnidadeAlternativa;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return UnidadeAlternativaId;
        yield return UnidadeAlternativaConversaoId;
        yield return QuantidadeUnidadeAlternativa;
    }
}