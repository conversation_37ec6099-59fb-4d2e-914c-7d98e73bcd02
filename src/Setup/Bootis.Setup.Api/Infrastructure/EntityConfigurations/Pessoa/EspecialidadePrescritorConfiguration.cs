using Bootis.Setup.Api.Extensions;
using Bootis.Setup.Api.Models.Pessoa;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Setup.Api.Infrastructure.EntityConfigurations.Pessoa;

public class EspecialidadePrescritorConfiguration : IEntityTypeConfiguration<EspecialidadePrescritor>
{
    public void Configure(EntityTypeBuilder<EspecialidadePrescritor> builder)
    {
        builder.ToTable(typeof(EspecialidadePrescritor).GetTableName());

        builder.<PERSON><PERSON>ey(x => x.Id);
    }
}