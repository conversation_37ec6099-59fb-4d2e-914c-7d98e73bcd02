using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Serilog.Core;
using Serilog.Events;

namespace Bootis.Shared.Api;

public class UniversalCircularReferencePolicy : IDestructuringPolicy
{
    private readonly ThreadLocal<HashSet<object>> _visiting = new(() => new HashSet<object>());

    public bool TryDestructure(object value, ILogEventPropertyValueFactory propertyValueFactory,
        out LogEventPropertyValue result)
    {
        result = null;

        if (value == null)
            return false;

        var type = value.GetType();

        // BLOQUEAR COMPLETAMENTE qualquer coisa relacionada ao Entity Framework
        if (IsEntityFrameworkType(type))
        {
            result = new ScalarValue($"[EF-{type.Name}: Blocked for safety]");
            return true;
        }

        // Verificar se já estamos visitando este objeto
        if (_visiting.Value.Contains(value))
        {
            result = new ScalarValue($"[Circular: {type.Name}]");
            return true;
        }

        // Tipos simples - deixar o Serilog processar
        if (IsSimpleType(type))
            return false;

        // Para outros objetos complexos
        try
        {
            _visiting.Value.Add(value);
            result = CreateSafeStructure(value, propertyValueFactory);
            return true;
        }
        catch (ObjectDisposedException)
        {
            result = new ScalarValue($"[{type.Name}: Disposed object]");
            return true;
        }
        catch (Exception ex)
        {
            result = new ScalarValue($"[{type.Name}: Error - {ex.GetType().Name}]");
            return true;
        }
        finally
        {
            _visiting.Value.Remove(value);
        }
    }

    private static bool IsEntityFrameworkType(Type type)
    {
        var typeName = type.Name;
        var namespaceName = type.Namespace ?? "";

        return namespaceName.StartsWith("Microsoft.EntityFrameworkCore") ||
               namespaceName.StartsWith("System.Data") ||
               type.IsSubclassOf(typeof(DbContext)) ||
               typeName.Contains("DbContext") ||
               typeName.Contains("ChangeTracker") ||
               typeName.Contains("EntityEntry") ||
               typeName.Contains("DbSet") ||
               typeName.Contains("Database") ||
               typeName.Contains("Model") ||
               typeName.Contains("Migration") ||
               // Adicionar seu contexto específico
               typeName == "BootisContext" ||
               // Bloquear propriedades que podem acessar contexto disposed
               HasEntityFrameworkProperties(type);
    }

    private static bool HasEntityFrameworkProperties(Type type)
    {
        try
        {
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            return properties.Any(p =>
                p.PropertyType.IsSubclassOf(typeof(DbContext)) ||
                p.PropertyType.Name.Contains("DbSet") ||
                p.Name.Contains("Context") ||
                p.Name == "OutboxMessages"); // Propriedade específica que está causando o erro
        }
        catch
        {
            return true; // Se não conseguir verificar, bloquear por segurança
        }
    }

    private LogEventPropertyValue CreateSafeStructure(object value, ILogEventPropertyValueFactory factory)
    {
        var type = value.GetType();
        var properties = new List<LogEventProperty>();

        try
        {
            // Pegar apenas propriedades "super seguras"
            var safeProps = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead &&
                            IsSimpleType(p.PropertyType) &&
                            !IsProblematicProperty(p))
                .Take(5); // Reduzir ainda mais

            foreach (var prop in safeProps)
                try
                {
                    var propValue = prop.GetValue(value);
                    if (propValue != null)
                        properties.Add(new LogEventProperty(prop.Name, factory.CreatePropertyValue(propValue)));
                }
                catch (ObjectDisposedException)
                {
                    properties.Add(new LogEventProperty(prop.Name, new ScalarValue("[Disposed]")));
                }
                catch
                {
                    // Ignorar qualquer propriedade problemática
                }
        }
        catch (ObjectDisposedException)
        {
            properties.Add(new LogEventProperty("Status", new ScalarValue("Disposed")));
        }

        return new StructureValue(properties, type.Name);
    }

    private static bool IsProblematicProperty(PropertyInfo property)
    {
        var propName = property.Name;
        var propType = property.PropertyType;

        return propName.Contains("Context") ||
               propName.Contains("Database") ||
               propName.Contains("Model") ||
               propName.Contains("Entry") ||
               propName.Contains("Tracker") ||
               propName.Contains("OutboxMessages") ||
               propName.Contains("DbSet") ||
               propType.IsSubclassOf(typeof(DbContext)) ||
               propType.Name.Contains("DbSet");
    }

    private static bool IsSimpleType(Type type)
    {
        return type.IsPrimitive ||
               type.IsEnum ||
               type == typeof(string) ||
               type == typeof(DateTime) ||
               type == typeof(DateTimeOffset) ||
               type == typeof(TimeSpan) ||
               type == typeof(Guid) ||
               type == typeof(decimal) ||
               (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>));
    }
}