using Bootis.Shared.Common.Extensions;
using Bootis.Venda.Application.Requests.Atendimento.Cadastrar;
using UUIDNext;

namespace Bootis.Venda.UnitTests.Fixtures.Atendimento;

public static class CadastrarRequestAtendimentoFakes
{
    public static CadastrarRequest CriarCommandValido()
    {
        return new CadastrarRequest
        {
            ClienteId = Uuid.NewSequential(),
            CanalAtendimentoId = 1.ToGuid()
        };
    }

    public static CadastrarRequest CriarCommandValido_ComPedidoVenda()
    {
        return new CadastrarRequest
        {
            ClienteId = Uuid.NewSequential(),
            CanalAtendimentoId = 1.ToGuid(),
            CriarPedidoVenda = true
        };
    }
}
