using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Cliente.Remover;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Cliente;

public class RemoverEnderecoRequestHandler(
    IUnitOfWork unitOfWork,
    IClienteRepository clienteRepository)
    : IRequestHandler<RemoverEnderecoRequest>
{
    public async Task Handle(RemoverEnderecoRequest request, CancellationToken cancellationToken)
    {
        var clienteEndereco = await clienteRepository.ObterClienteEnderecoAsync(request.ClienteEnderecoId);

        clienteRepository.Remove(clienteEndereco);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}