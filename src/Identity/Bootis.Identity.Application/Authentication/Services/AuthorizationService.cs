using System.Collections.Immutable;
using Bootis.Identity.Application.Authentication.Validators;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.ValueObjects.Auth;
using Microsoft.Extensions.Logging;
using OneOf;

namespace Bootis.Identity.Application.Authentication.Services;

public interface IAuthorizationService : IScopedService
{
    Task<OneOf<AuthorizeSuccess, AuthorizeFailure>> AuthorizeAsync(
        Guid userId,
        Guid sessionId,
        ImmutableHashSet<int> requiredRoles,
        CancellationToken cancellationToken);
}

public class AuthorizationService(
    IIdentitySessionValidator sessionValidator,
    IPermissionProvider permissionProvider,
    ILogger<AuthorizationService> logger)
    : IAuthorizationService
{
    public async Task<OneOf<AuthorizeSuccess, AuthorizeFailure>> AuthorizeAsync(
        Guid userId,
        Guid sessionId,
        ImmutableHashSet<int> requiredRoles,
        CancellationToken cancellationToken)
    {
        var isValidSession = await sessionValidator.IsSessionValid(userId, sessionId, cancellationToken);
        if (!isValidSession)
        {
            logger.LogWarning("Authorization failed: Session revoked for user {UserId}", userId);
            return CreateFailure(AuthenticationError.SessionRevoked, false);
        }

        if (requiredRoles is null || requiredRoles.Count == 0)
        {
            logger.LogDebug("No roles required for authorization, skipping permission check for user {UserId}", userId);
            return new AuthorizeSuccess
            {
                UserId = userId,
                SessionId = sessionId
            };
        }

        var permissions = await permissionProvider.GetEffectivePermissionsAsync(userId);
        var hasRole = requiredRoles!.Intersect(permissions).Any();

        if (!hasRole)
        {
            logger.LogWarning("Authorization failed: Insufficient permissions for user {UserId}", userId);
            return CreateFailure(AuthenticationError.InsufficientPermissions, true);
        }

        logger.LogDebug("Authorization successful for user {UserId}", userId);
        return new AuthorizeSuccess
        {
            UserId = userId,
            SessionId = sessionId,
            GrantedRoles = requiredRoles
        };
    }

    private static AuthorizeFailure CreateFailure(AuthenticationError error, bool isActive)
    {
        return new AuthorizeFailure(error) { IsActive = isActive };
    }
}