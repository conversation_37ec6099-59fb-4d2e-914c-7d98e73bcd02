using System.Data;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Shared.Application.Interfaces;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Calcular;

public class ObterEmbalagemCapsulaTamanhoAssociacoesHandler(IDbConnection dbConnection, IUserContext userContext)
    : IRequestHandler<ObterEmbalagemCapsulaTamanhoAssociacoesDto, Guid?>
{
    public async Task<Guid?> Handle(ObterEmbalagemCapsulaTamanhoAssociacoesDto request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                               SELECT ecta.produto_embalagem_id
                                     FROM embalagem_capsula_tamanho_associacoes ecta
                                          JOIN produtos_embalagem pe ON
                                              pe.produto_id = ecta.produto_embalagem_id
                                          JOIN produtos p ON
                                              p.id = pe.produto_id
                                 WHERE ecta.capsula_tamanho_id = @capsulaTamanhoId
                                   AND ecta.quantidade_capsula >= @quantidadeCapsulas
                                   AND p.group_tenant_id = @groupTenantId
                               ORDER BY ecta.quantidade_capsula
                                 LIMIT 1;
                           """;

        var parameters = new
        {
            request.CapsulaTamanhoId,
            request.QuantidadeCapsulas,
            userContext.GroupTenantId
        };

        var embalagemId = await dbConnection.QueryFirstOrDefaultAsync<Guid?>(sql, parameters);

        if (embalagemId != null)
            return embalagemId;
        
        const string sqlMaiorEmbalagem = """
                                             SELECT ea.produto_embalagem_id
                                               FROM embalagem_capsula_tamanho_associacoes ea
                                                    JOIN produtos_embalagem pe ON
                                                        pe.produto_id = ea.produto_embalagem_id
                                                    JOIN produtos p ON
                                                        p.id = pe.produto_id
                                              WHERE ea.capsula_tamanho_id = @capsulaTamanhoId
                                                AND p.group_tenant_id = @groupTenantId
                                             ORDER BY ea.quantidade_capsula DESC
                                                LIMIT 1;
                                         """;

        var parametersSegunda = new
        {
            request.CapsulaTamanhoId,
            userContext.GroupTenantId
        };

        return await dbConnection.QueryFirstOrDefaultAsync<Guid?>(sqlMaiorEmbalagem, parametersSegunda);
    }
}