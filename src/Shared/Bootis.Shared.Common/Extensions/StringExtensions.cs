using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Serilog;

namespace Bootis.Shared.Common.Extensions;

public static partial class StringExtensions
{
    public static string CorrectDateFormat(this string formatFromSource)
    {
        if (string.IsNullOrWhiteSpace(formatFromSource))
        {
            Log.Logger.Warning("The format string from the database is null or empty. Returning the original value.");
            return formatFromSource;
        }

        var correctedFormat = formatFromSource;
        var wasCorrected = false;

        if (!correctedFormat.Contains('M') && LowercaseMonthRegex().IsMatch(correctedFormat))
            correctedFormat = LowercaseMonthRegex().Replace(correctedFormat, match =>
            {
                wasCorrected = true;
                return match.Value.Length == 1 ? "M" : "MM";
            });

        if (correctedFormat.Contains('Y'))
        {
            correctedFormat = correctedFormat.Replace('Y', 'y');
            wasCorrected = true;
        }

        if (correctedFormat.Contains("DD"))
        {
            correctedFormat = correctedFormat.Replace("DD", "dd");
            wasCorrected = true;
        }
        else if (correctedFormat.Contains('D') &&
                 (correctedFormat.Contains('y') || correctedFormat.Contains('M')) &&
                 !correctedFormat.Any(c => "FfGgOoRrUu".Contains(c)))
        {
            correctedFormat = correctedFormat.Replace("D", "d");
            wasCorrected = true;
        }

        correctedFormat = SingleHourRegex().Replace(correctedFormat, match =>
        {
            wasCorrected = true;
            return match.Value + match.Value;
        });

        var hasHour12 = correctedFormat.Contains("hh");
        var hasHour24 = correctedFormat.Contains("HH");
        var hasAmPm = correctedFormat.Contains("tt") || correctedFormat.Contains('t');

        if (!correctedFormat.Contains("tt") && correctedFormat.Contains('t'))
        {
            correctedFormat = SingleAmPmRegex().Replace(correctedFormat, _ =>
            {
                wasCorrected = true;
                return "tt";
            });
            hasAmPm = true;
        }

        if (hasHour12 && !hasAmPm)
        {
            if (correctedFormat.Contains(':'))
            {
                var lastTimeComponentIndex = correctedFormat.LastIndexOfAny(['s', 'm']);
                if (lastTimeComponentIndex >= 0 &&
                    lastTimeComponentIndex < correctedFormat.Length - 1 &&
                    correctedFormat.Length > lastTimeComponentIndex + (correctedFormat[lastTimeComponentIndex] ==
                                                                       correctedFormat[lastTimeComponentIndex - 1]
                        ? 2
                        : 1))
                {
                    var insertIndex = lastTimeComponentIndex +
                                      (correctedFormat[lastTimeComponentIndex] ==
                                       correctedFormat[lastTimeComponentIndex - 1]
                                          ? 2
                                          : 1);
                    correctedFormat = correctedFormat.Insert(insertIndex, " tt");
                }
                else
                {
                    correctedFormat += " tt";
                }
            }
            else
            {
                correctedFormat += " tt";
            }

            wasCorrected = true;
        }
        else if (hasHour24 && hasAmPm)
        {
            correctedFormat = correctedFormat.Replace(" tt", "").Replace(" t", "");
            wasCorrected = true;
        }

        if (SingleMinuteRegex().IsMatch(correctedFormat))
            correctedFormat = SingleMinuteRegex().Replace(correctedFormat, _ =>
            {
                wasCorrected = true;
                return "mm";
            });

        if (SingleSecondRegex().IsMatch(correctedFormat))
            correctedFormat = SingleSecondRegex().Replace(correctedFormat, _ =>
            {
                wasCorrected = true;
                return "ss";
            });

        try
        {
            _ = DateTime.MinValue.ToString(correctedFormat, CultureInfo.InvariantCulture);
        }
        catch (FormatException ex)
        {
            Log.Logger.Error(ex,
                "INVALID FORMAT AFTER CORRECTION: Format '{CorrectedFormat}' (original: '{OriginalForLog}') is still invalid. Returning original format. Error: {Message}",
                correctedFormat, formatFromSource, ex.Message);
            return formatFromSource;
        }

        if (wasCorrected)
            Log.Logger.Information(
                "AUTOMATIC FORMAT CORRECTION: Format '{OriginalForLog}' was corrected to '{CorrectedFormat}'.",
                formatFromSource, correctedFormat);

        return correctedFormat;
    }

    /// <summary>
    ///     Converte uma string para o formato snake_case.
    ///     Exemplo: "UserName" se torna "user_name".
    /// </summary>
    public static string ToSnakeCase(this string input)
    {
        return ToCase(input, CaseType.Snake);
    }

    /// <summary>
    ///     Converte uma string para o formato PascalCase.
    ///     Exemplo: "user_name" se torna "UserName".
    /// </summary>
    public static string ToPascalCase(this string input)
    {
        return ToCase(input, CaseType.Pascal);
    }

    /// <summary>
    ///     Converte uma string para o formato camelCase.
    ///     Exemplo: "UserName" se torna "userName".
    /// </summary>
    public static string ToCamelCase(this string input)
    {
        return ToCase(input, CaseType.Camel);
    }

    private static string ToCase(this string input, CaseType targetCase)
    {
        if (string.IsNullOrWhiteSpace(input))
            return input;

        var words = SplitIntoWords(input);

        if (words.Count == 0)
            return string.Empty;

        var sb = new StringBuilder(input.Length + words.Count);

        for (var i = 0; i < words.Count; i++)
        {
            var word = words[i];
            var isFirstWord = i == 0;

            switch (targetCase)
            {
                case CaseType.Pascal:
                    AppendPascalWord(sb, word);
                    break;

                case CaseType.Camel:
                    if (isFirstWord)
                        AppendLowerWord(sb, word);
                    else
                        AppendPascalWord(sb, word);
                    break;

                case CaseType.Snake:
                    if (!isFirstWord)
                        sb.Append('_');
                    AppendLowerWord(sb, word);
                    break;

                default:
                    throw new ArgumentOutOfRangeException(nameof(targetCase), targetCase, null);
            }
        }

        return sb.ToString();
    }

    private static List<string> SplitIntoWords(string input)
    {
        var words = new List<string>();
        var currentWord = new StringBuilder();

        for (var i = 0; i < input.Length; i++)
        {
            var currentChar = input[i];

            if (currentChar == '_')
            {
                if (currentWord.Length > 0)
                {
                    words.Add(currentWord.ToString());
                    currentWord.Clear();
                }
            }
            else if (i > 0 && char.IsUpper(currentChar) &&
                     (char.IsLower(input[i - 1]) ||
                      (i < input.Length - 1 && char.IsLower(input[i + 1]))))
            {
                if (currentWord.Length > 0)
                {
                    words.Add(currentWord.ToString());
                    currentWord.Clear();
                }

                currentWord.Append(currentChar);
            }
            else
            {
                currentWord.Append(currentChar);
            }
        }

        if (currentWord.Length > 0) words.Add(currentWord.ToString());

        return words;
    }

    private static void AppendPascalWord(StringBuilder sb, string word)
    {
        if (string.IsNullOrEmpty(word)) return;

        sb.Append(char.ToUpperInvariant(word[0]));
        for (var i = 1; i < word.Length; i++) sb.Append(char.ToLowerInvariant(word[i]));
    }

    private static void AppendLowerWord(StringBuilder sb, string word)
    {
        foreach (var c in word) sb.Append(char.ToLowerInvariant(c));
    }

    public static string GetNumbers(this string input)
    {
        return NumberRegex().Replace(input, "");
    }

    [GeneratedRegex(@"\D")]
    private static partial Regex NumberRegex();

    [GeneratedRegex(@"(?<![Hh]:)(?<![Hh])(?<=[^A-Za-z]|^)(m|mm)(?![A-Za-z])(?=.*(y|d|Y|D))")]
    private static partial Regex LowercaseMonthRegex();

    [GeneratedRegex(@"(?<!t)t(?!t)")]
    private static partial Regex SingleAmPmRegex();

    [GeneratedRegex(@"(?<!h|H)(h|H)(?!h|H)")]
    private static partial Regex SingleHourRegex();

    [GeneratedRegex(@"(?<=[Hh]:|T| )(m)(?![m])")]
    private static partial Regex SingleMinuteRegex();

    [GeneratedRegex(@"(?<=[m]:|T| )(s)(?![s])")]
    private static partial Regex SingleSecondRegex();

    private enum CaseType
    {
        Pascal,
        Camel,
        Snake
    }
}