using System.Data;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using Bootis.Venda.Application.Requests.PedidoVenda.Listar;
using MediatR;

namespace Bootis.Venda.Infrastructure.Queries.PedidoVenda.Listar;

public class
    ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                            
                           SELECT c.nome AS nome_cliente,
                                  pv.id,
                                  pv.sequencia_group_tenant AS codigo_pedido_venda,
                                  ca.icon AS canal_icon,
                                  a.sequencia_group_tenant AS codigo_atendimento,
                                  a.id AS atendimento_id,
                                  sa.id AS status_atendimento_id,
                                  sa.descricao AS status_atendimento,
                                  sa.cor_fundo AS status_atendimento_cor_fundo,
                                  sa.cor_fonte AS status_atendimento_cor_fonte,
                                  pv.data_lancamento,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS atendente,
                                  pt.total_pedido AS valor_pedido,
                                  pv.status AS status_pedido_venda
                             FROM pedidos_venda pv
                                  LEFT JOIN atendimentos a ON
                                        a.id = pv.atendimento_id
                                  LEFT JOIN clientes c ON
                                        c.id = pv.cliente_id
                                  LEFT JOIN usuarios u ON
                                        u.id = pv.vendedor_id
                                  LEFT JOIN canais_atendimento ca ON
                                        ca.id = a.canal_atendimento_id
                                  LEFT JOIN pedidos_venda_totalizadores pt ON
                                        pt.id = pv.id
                                  LEFT JOIN status_atendimento sa ON
                                        sa.id = a.status_atendimento_id
                            WHERE pv.group_tenant_id = @GroupTenantId
                           !@SEARCH_CONDITION@! 
                           """;

        var condtionaloClienteId = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "C.id = @ClienteId",
            Predicate = filter => filter.ClienteId.HasValue
        };

        var searchCliente = new StringSearchField
        {
            Field = "C.nome",
            CompareType = StringCompareType.Contains
        };

        var searchCodigo = new NumberSearchField
        {
            Field = "PV.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        var searchAtendimento = new NumberSearchField
        {
            Field = "A.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        var searchDataLancamento = new DateSearchField
        {
            Field = "PV.data_lancamento",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchAtendente = new StringSearchField
        {
            Field = "CONCAT(U.nome, ' ', U.sobrenome)",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchCliente)
            .AddSearchField(searchCodigo)
            .AddSearchField(searchAtendimento)
            .AddSearchField(searchDataLancamento)
            .AddSearchField(searchAtendente)
            .AddFilter(condtionaloClienteId)
            .ExecuteAsync();
    }
}