<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <AssemblyName>Bootis.Estoque.Api</AssemblyName>
        <RootNamespace>Bootis.Estoque.Api</RootNamespace>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{00ba4826-263f-456c-9a95-5bca5eeea7ff}</ProjectGuid>
        <UserSecretsId>cbcbfb12-ce85-4c75-a8a9-fd5a9a206aa7</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
        <ProjectReference Include="..\Bootis.Estoque.Infrastructure\Bootis.Estoque.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <Content Remove="appsettings*.json"/>
    </ItemGroup>

    <ItemGroup Condition="'$(IsPublishable)' == 'true' AND '$(MSBuildProjectName)' == 'Bootis.Pharma.Api'">
        <None Include="appsettings*.json">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </None>
    </ItemGroup>

</Project>

