using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;
using UnidadeMedida = Bootis.Catalogo.Application.Requests.UnidadeMedida.UnidadeMedida;

namespace Bootis.Estoque.Application.Requests.Perda.Obter;

public class ObterResponse
{
    public Guid Id { get; set; }
    public DateTime DataLancamento { get; set; }
    public string Responsavel { get; set; }
    public Guid UsuarioId { get; set; }
    public string DescricaoProduto { get; set; }
    public Guid ProdutoId { get; set; }
    public string LocalEstoqueDescricao { get; set; }
    public string NomeFantasia { get; set; }
    public Guid LocalEstoqueId { get; set; }
    public string NumeroLote { get; set; }
    public Guid LoteId { get; set; }
    public string Fornecedor { get; set; }
    public Guid FornecedorId { get; set; }
    public int NumeroNf { get; set; }
    public int SerieNf { get; set; }
    public DateTime DataPerda { get; set; }
    public string MotivoPerdaDescricao { get; set; }
    public int CodigoSngpc { get; set; }
    public Guid MotivoPerdaId { get; set; }
    public decimal QuantidadeAntesPerda { get; set; }
    public decimal QuantidadePerdida { get; set; }
    public decimal QuantidadeAposPerda { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }

    public virtual UnidadeMedida UnidadeMedida
    {
        get
        {
            var unidadeMedidaModel = UnidadeMedidaCreator.Criar(UnidadeMedidaId);

            return new UnidadeMedida
            {
                Abreviacao = unidadeMedidaModel.Abreviacao.GetDescription(),
                Ativo = unidadeMedidaModel.Ativo,
                Descricao = unidadeMedidaModel.Descricao,
                UnidadeAlternativa = unidadeMedidaModel.UnidadeAlternativa
            };
        }
    }

    public string Observacao { get; set; }
}