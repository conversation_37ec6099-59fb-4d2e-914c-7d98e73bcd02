using System.Data;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoAssociado.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               proa.descricao AS produto_associado_descricao,
                               pa.id,
                               proa.id AS produto_associado_id
                           FROM produtos_associado pa
                                    LEFT JOIN produtos prod ON prod.id = pa.produto_id
                                    LEFT JOIN produtos proa ON proa.id = pa.produto_associado_id
                           WHERE prod.id = @ProdutoId
                             AND pa.group_tenant_id = @GroupTenantId
                             !@SEARCH_CONDITION@!
                           """;

        var searchProdutoAssociado = new StringSearchField
        {
            Field = "PROA.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchProdutoAssociado)
            .ExecuteAsync();
    }
}