using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.TipoDocumentoAggregate;
using Bootis.Pessoa.Domain.Enumerations;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Pessoa.Application.Extensions;

public static class TipoDocumentoRepositoryExtension
{
    public static async Task ValidarPrescritorDocumentoAsync(this ITipoDocumentoRepository repository, Guid id)
    {
        var tipoDocumento = await repository.FindWithoutIncludesAsync(id);

        if (tipoDocumento == null)
            throw new ValidationException(nameof(id), Localizer.Instance.GetMessage_TipoDocumento_IdNaoEncontrado(id));

        if (tipoDocumento.Pessoa != TipoPessoa.Fisica)
            throw new ValidationException(nameof(id), Localizer.Instance.GetMessage_TipoDocumento_Invalido());
    }

    public static async Task ValidarClienteDocumentoAsync(this ITipoDocumentoRepository tipoDocumentoRepository,
        IClienteRepository clienteRepository, Cliente cliente, Guid tipoDocumentoId)
    {
        var tipoDocumento = await tipoDocumentoRepository.FindWithoutIncludesAsync(tipoDocumentoId);

        if (tipoDocumento == null)
            throw new ValidationException(nameof(tipoDocumentoId),
                Localizer.Instance.GetMessage_TipoDocumento_IdNaoEncontrado(tipoDocumentoId));

        int?[] ordemIn = { 0, 1, 2 };

        if (!ordemIn.Contains(tipoDocumento.Ordem) || tipoDocumento.Pessoa != cliente.Pessoa)
            throw new ValidationException(nameof(tipoDocumentoId),
                Localizer.Instance.GetMessage_TipoDocumento_Invalido());

        var documentosCliente = await clienteRepository.ObterDocumentosPorClienteIdAsync(cliente.Id);

        if (documentosCliente.Any(dc => dc.TipoDocumentoId == tipoDocumentoId))
            throw new ValidationException(nameof(tipoDocumentoId),
                Localizer.Instance.GetMessage_TipoDocumento_JaCadastrado(tipoDocumentoId));
    }

    public static async Task ValidarAtualizacaoClienteDocumentoAsync(
        this ITipoDocumentoRepository tipoDocumentoRepository,
        IClienteRepository clienteRepository, Cliente cliente, Guid tipoDocumentoId)
    {
        var tipoDocumento = await tipoDocumentoRepository.FindWithoutIncludesAsync(tipoDocumentoId);

        if (tipoDocumento == null)
            throw new ValidationException(nameof(tipoDocumentoId),
                Localizer.Instance.GetMessage_TipoDocumento_IdNaoEncontrado(tipoDocumentoId));

        int?[] ordemIn = { 0, 1, 2 };

        if (!ordemIn.Contains(tipoDocumento.Ordem) || tipoDocumento.Pessoa != cliente.Pessoa)
            throw new ValidationException(nameof(tipoDocumentoId),
                Localizer.Instance.GetMessage_TipoDocumento_Invalido());
    }

    public static async Task ValidarFornecedorDocumentoAsync(this ITipoDocumentoRepository tipoDocumentoRepository,
        IFornecedorRepository fornecedorRepository, Fornecedor fornecedor, Guid id)
    {
        var tipoDocumento = await tipoDocumentoRepository.FindWithoutIncludesAsync(id);

        if (tipoDocumento == null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_TipoDocumento_IdNaoEncontrado(id));

        int?[] ordemIn = { 0, 1, 2 };

        if (!ordemIn.Contains(tipoDocumento.Ordem) || tipoDocumento.Pessoa != fornecedor.TipoPessoa)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_TipoDocumento_Invalido());

        var documentosFornecedor = await fornecedorRepository.ObterDocumentosPorFornecedorIdAsync(fornecedor.Id);

        if (documentosFornecedor.Any(dc => dc.TipoDocumentoId == id))
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_TipoDocumento_JaCadastrado(id));
    }

    public static async Task ValidarAtualizacaoFornecedorDocumentoAsync(
        this ITipoDocumentoRepository tipoDocumentoRepository, Fornecedor fornecedor, Guid id)
    {
        var tipoDocumento = await tipoDocumentoRepository.FindWithoutIncludesAsync(id);

        if (tipoDocumento == null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_TipoDocumento_IdNaoEncontrado(id));

        int?[] ordemIn = { 0, 1, 2 };

        if (!ordemIn.Contains(tipoDocumento.Ordem) || tipoDocumento.Pessoa != fornecedor.TipoPessoa)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_TipoDocumento_Invalido());
    }
}