using System.Data;
using Bootis.Pessoa.Application.Requests.Cliente.Validar;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Cliente.Validar;

public class ValidarCnpjRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ValidarCnpjRequest>
{
    public async Task Handle(ValidarCnpjRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM clientes
                                                     WHERE cnpj = @cnpj
                                                       AND group_tenant_id = @groupTenantId)
                                       THEN 1 ELSE 0 END AS existe_registro;
                           """;

        var result = await connection.QuerySingleOrDefaultAsync<bool>(sql,
            new { cnpj = request.Cnpj, groupTenantId = userContext.GroupTenantId });

        if (result) throw new DomainException(Localizer.Instance.GetMessage_Cliente_CnpjExistente(request.Cnpj));
    }
}