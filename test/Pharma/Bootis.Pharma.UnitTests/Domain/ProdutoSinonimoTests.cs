using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.ProdutoSinonimo;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.UnitTests;

namespace Bootis.Estoque.UnitTests.Domain;

public class ProdutoSinonimoTests : BaseTest
{
    private readonly ProdutoSinonimo _produtoSinonimo =
        ProdutoSinonimoFake.CreateProdutoSinonimoValido();

    [Fact]
    public void ProdutoSinonimo_DeveAtualizarProdutoSinonimo()
    {
        //Arrange 
        var cmd = AtualizarProdutoSinonimoCommandFake.CreateAtualizarProdutoSinonimoCommandValido();

        //Action
        _produtoSinonimo.Atualizar(cmd.FatorEquivalencia.Value, cmd.PercentualCorrecao.Value,
            cmd.Sinonimo, cmd.DescricaoRotulo);

        //Assert
        Assert.Equal(cmd.Sinonimo, _produtoSinonimo.Sinonimo);
        Assert.Equal(cmd.FatorEquivalencia, _produtoSinonimo.FatorEquivalencia);
    }
}
