using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.Inventario.Atualizar;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Inventario;

public class FinalizarLancamentoRascunhoRequestHandler(
    IUnitOfWork unitOfWork,
    IInventarioRepository inventarioRepository,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<FinalizarLancamentoRascunhoRequest>
{
    public async Task Handle(FinalizarLancamentoRascunhoRequest request, CancellationToken cancellationToken)
    {
        var inventario = await inventarioRepository.ObterInventarioAsync(request.InventarioId);
        var inventarioLancamento =
            inventario.InventarioLancamento.SingleOrDefault(c =>
                c.CodigoSequencia == inventario.CodigoUltimoLancamento);

        inventario.FinalizarLancamentoRascunho(inventarioLancamento, request.Itens);

        inventarioRepository.Update(inventario);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}