using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Venda.Application.Extensions;
using Bootis.Venda.Application.Requests.PedidoVenda.Cadastrar;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using Bootis.Venda.Domain.Enumerations;
using MediatR;

namespace Bootis.Venda.Application.UseCases.PedidoVenda;

public class CadastrarItemProdutoAcabadoRequestHandler(
    IUnitOfWork unitOfWork,
    IPedidoVendaRepository pedidoVendaRepository,
    IProdutoRepository produtoRepository,
    IPrescritorRepository prescritorRepository)
    : IRequestHandler<CadastrarItemProdutoAcabadoRequest>
{
    public async Task Handle(CadastrarItemProdutoAcabadoRequest request, CancellationToken cancellationToken)
    {
        var pedidoVenda = await pedidoVendaRepository.ObterPedidoVendaAsync(request.PedidoVendaId);
        var produto = await produtoRepository.ObterProdutoAcabadoAsync(request.ProdutoId);
        var prescritorId = await prescritorRepository.ObterIdPrescritor(request.PrescritorId);

        CadastrarProdutoAcabado(request, pedidoVenda, produto, prescritorId);

        pedidoVendaRepository.Update(pedidoVenda);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private static void CadastrarProdutoAcabado(CadastrarItemProdutoAcabadoRequest request,
        Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda pedidoVenda,
        Produto produto, Guid? prescritorId)
    {
        pedidoVenda.AdicionarItemProdutoAcabado(produto.Descricao,
            produto.Id,
            prescritorId,
            request.DataPrescricao,
            request.Quantidade,
            request.ValorUnitario,
            request.Desconto.GetValueOrDefault(),
            request.TipoDesconto,
            StatusVendaItem.Orcado,
            true,
            pedidoVenda.Atendimento.AtendenteId);
    }
}