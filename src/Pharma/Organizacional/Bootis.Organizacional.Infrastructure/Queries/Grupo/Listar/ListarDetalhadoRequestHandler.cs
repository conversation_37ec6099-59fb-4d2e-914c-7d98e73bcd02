using System.Data;
using Bootis.Organizacional.Application.Requests.Grupo.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Grupo.Listar;

public class
    ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT g.nome,
                                  g.id,
                                  g.se_ativo,
                                  (SELECT COUNT(usrg.usuarios_id)
                                     FROM usuarios_grupos usrg
                                    WHERE usrg.grupos_id = g.id) AS usuarios
                             FROM grupos g
                            WHERE g.group_tenant_id = @GroupTenantId
                              !@SEARCH_CONDITION@!
                           """;

        var searchGrupo = new StringSearchField
        {
            Field = "G.nome",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchGrupo)
            .ExecuteAsync();
    }
}