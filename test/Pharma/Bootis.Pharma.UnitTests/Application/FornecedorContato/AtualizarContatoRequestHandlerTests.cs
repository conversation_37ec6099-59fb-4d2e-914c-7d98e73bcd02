using Bootis.Pessoa.Application.UseCases.Fornecedor;
using Bootis.Pessoa.UnitTests.Fixtures.FornecedorContato;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using FornecedorModel = Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;

namespace Bootis.Pessoa.UnitTests.Application.FornecedorContato;

public class AtualizarContatoRequestHandlerTests : BaseTest
{
    private readonly Mock<FornecedorModel.IFornecedorRepository> _fornecedorRepositoryMock;
    private readonly AtualizarContatoRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();


    public AtualizarContatoRequestHandlerTests()
    {
        _fornecedorRepositoryMock = new Mock<FornecedorModel.IFornecedorRepository>();

        _handler = new AtualizarContatoRequestHandler(unitOfWork.Object, _fornecedorRepositoryMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AlterarFornecedorContato_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorContatoFake.CriarCommandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var model = new FornecedorModel.FornecedorContato(fornecedor, cmd.TipoContatoId, cmd.Identificacao,
            cmd.Principal, cmd.Observacao);

        _fornecedorRepositoryMock.Setup(l => l.ObterContatoPorContatoIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(model));

        _fornecedorRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>()))
            .ReturnsAsync(fornecedor);

        _fornecedorRepositoryMock.Setup(l => l.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _fornecedorRepositoryMock
            .Verify(
                l => l.Update(It.Is<FornecedorModel.Fornecedor>(m =>
                    m.Contatos.First().Identificacao == model.Identificacao)), Times.Once);
    }

    [Fact]
    public async Task AlterarFornecedorContato_ExecutadoComErro_FornecedorNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorContatoFake.CriarCommandValido();

        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((FornecedorModel.Fornecedor)null);

        _fornecedorRepositoryMock.Setup(l => l.Update(It.IsAny<FornecedorModel.Fornecedor>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task AlterarFornecedorContato_ExecutadoComErro_ContatoNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorContatoFake.CriarCommandValido();

        _fornecedorRepositoryMock.Setup(l => l.ObterContatoPorContatoIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult<FornecedorModel.FornecedorContato>(default));

        _fornecedorRepositoryMock.Setup(l => l.Update(It.IsAny<FornecedorModel.Fornecedor>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
