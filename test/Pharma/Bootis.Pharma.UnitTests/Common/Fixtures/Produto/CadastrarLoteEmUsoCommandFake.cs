using Bootis.Catalogo.Application.Requests.Produto.Cadastrar;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Produto;

public class CadastrarLoteEmUsoCommandFake
{
    public static CadastrarLoteEmUsoRequest CreateCommandValido()
    {
        return new CadastrarLoteEmUsoRequest
        {
            ProdutoId = Uuid.NewSequential(),
            LoteId = Uuid.NewSequential(),
            LocalEstoqueId = Uuid.NewSequential()
        };
    }
}
