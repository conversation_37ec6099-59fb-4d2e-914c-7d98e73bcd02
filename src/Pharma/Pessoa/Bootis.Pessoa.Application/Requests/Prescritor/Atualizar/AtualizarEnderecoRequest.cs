using MediatR;

namespace Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;

public class AtualizarEnderecoRequest : IRequest
{
    public Guid PrescritorEnderecoId { get; set; }
    public Guid? PaisId { get; set; }
    public Guid? EstadoId { get; set; }
    public Guid? CidadeId { get; set; }
    public string Bairro { get; set; }
    public string Numero { get; set; }
    public string Complemento { get; set; }
    public bool Principal { get; set; }
    public string Cep { get; set; }
    public string Descricao { get; set; }
    public string Logradouro { get; set; }
}