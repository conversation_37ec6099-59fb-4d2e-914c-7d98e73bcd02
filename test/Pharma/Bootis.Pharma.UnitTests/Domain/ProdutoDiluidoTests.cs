using Bootis.Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.ProdutoDiluido;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;
using Bootis.Shared.UnitTests;

namespace Bootis.Estoque.UnitTests.Domain;

public class ProdutoDiluidoTests : BaseTest
{
    private readonly ProdutoDiluido _produtoDiluido;

    public ProdutoDiluidoTests()
    {
        _produtoDiluido =
            ProdutoDiluidoFake.CreateProdutoDiluidoValido();
    }

    [Fact]
    public void ProdutoDiluido_AtualizarProdutoDiluido()
    {
        //Arrange
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmd = AtualizarProdutoDiluidoCommandFake.CreateAtualizarProdutoDiluidoCommandValido();

        //Action
        _produtoDiluido.Atualizar(formaFarmaceutica, cmd);

        //Assert
        Assert.Equal(formaFarmaceutica, _produtoDiluido.FormaFarmaceutica);
        Assert.Equal(cmd.DosagemMaxima, _produtoDiluido.DosagemMaxima);
    }

    [Fact]
    public void ProdutoDiluido_DeveValidarDosagem()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_DosagemDiferente();

        var tipoUnidade = UnidadeMedidaCreator.Criar(cmd.UnidadeMedidaId).TipoUnidade;

        //Action
        var executionResult = _produtoDiluido.ValidarProdutoDiluido(cmd, formaFarmaceutica, tipoUnidade);

        //Assert
        Assert.Null(executionResult);
    }

    [Fact]
    public void ProdutoDiluido_DeveValidarDosagem_DiluicaoUnica_ExecutadoComErro()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmdInterno = CadastrarProdutoDiluidoCommandFake
            .CreateCadastrarProdutoDiluidoCommandValido_TodasFFEhQualquerDosagem();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido();

        _produtoDiluido.Atualizar(formaFarmaceutica, cmdInterno);

        var tipoUnidade = UnidadeMedidaCreator.Criar(cmd.UnidadeMedidaId).TipoUnidade;

        //Action
        var exception =
            Record.Exception(() => _produtoDiluido.ValidarProdutoDiluido(cmd, formaFarmaceutica, tipoUnidade));

        //Assert
        Assert.IsType<DomainException>(exception);
    }

    [Fact]
    public void ProdutoDiluido_DeveValidarDosagem_TipoUnidadeDiferente()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido();

        var tipoUnidade = UnidadeMedidaCreator.Criar(UnidadeMedidaAbreviacao.L).TipoUnidade;

        //Action
        var executionResult = _produtoDiluido.ValidarProdutoDiluido(cmd, formaFarmaceutica, tipoUnidade);

        //Assert
        Assert.Null(executionResult);
    }

    [Fact]
    public void ProdutoDiluido_DeveValidarDosagem_FormaFarmaceuticaComDiluicaoParaQualquerDosagem_ExecutadoComErro()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmdInterno =
            CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_QualquerDosagem();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido();

        _produtoDiluido.Atualizar(formaFarmaceutica, cmdInterno);

        var tipoUnidade = UnidadeMedidaCreator.Criar(cmd.UnidadeMedidaId).TipoUnidade;

        //Action
        var exception =
            Record.Exception(() => _produtoDiluido.ValidarProdutoDiluido(cmd, formaFarmaceutica, tipoUnidade));

        //Assert
        Assert.IsType<DomainException>(exception);
    }

    [Fact(Skip = "Need to be fixed")]
    public void
        ProdutoDiluido_DeveValidarDosagem_FormaFarmaceuticaComDiluicaoNaoSendoPossivelParaQualquerDosagem_ExecutadoComErro()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_QualquerDosagem();

        var tipoUnidade = UnidadeMedidaCreator.Criar(cmd.UnidadeMedidaId).TipoUnidade;

        //Action
        var exception =
            Record.Exception(() => _produtoDiluido.ValidarProdutoDiluido(cmd, formaFarmaceutica, tipoUnidade));

        //Assert
        Assert.IsType<DomainException>(exception);
    }

    [Fact]
    public void ProdutoDiluido_DeveValidarDosagem_DosagemInvalida_ExecutadoComErro()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido();

        var tipoUnidade = UnidadeMedidaCreator.Criar(cmd.UnidadeMedidaId).TipoUnidade;

        //Action
        var executionResult = _produtoDiluido.ValidarProdutoDiluido(cmd, formaFarmaceutica, tipoUnidade);

        //Assert
    }
}
