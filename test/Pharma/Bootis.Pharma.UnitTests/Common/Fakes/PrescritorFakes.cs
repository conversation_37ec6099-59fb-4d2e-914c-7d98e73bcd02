using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Common.Extensions;
using UUIDNext;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class PrescritorFakes
{
    public static Prescritor CriarPrescritorValido()
    {
        var tipoRegistro = new TipoRegistro("Conselho Regional de Medicina", "CRM");
        var pais = new Pais("Brasil", "BR", 55, Uuid.NewSequential());
        var estado = new Estado("São Paulo", "SP", pais);

        return new Prescritor("Nome Teste", tipoRegistro, estado, "123456", DateTime.UtcNow.ToDateOnly(),
            "Observação teste", 10, 10, "link");
    }
}