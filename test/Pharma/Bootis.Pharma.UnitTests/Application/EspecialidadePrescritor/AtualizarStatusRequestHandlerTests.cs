using Bootis.Pessoa.Application.Requests.EspecialidadePrescritor.Atualizar;
using Bootis.Pessoa.Application.UseCases.EspecialidadePrescritor;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;
using EspecialidadePrescritorModel = Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate;

namespace Bootis.Pessoa.UnitTests.Application.EspecialidadePrescritor;

public class AtualizarStatusRequestHandlerTests : BaseTest
{
    private readonly Mock<EspecialidadePrescritorModel.IEspecialidadePrescritorRepository>
        _especialidadePrescritorRepositoryMock;

    private readonly AtualizarStatusRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarStatusRequestHandlerTests()
    {
        _especialidadePrescritorRepositoryMock =
            new Mock<EspecialidadePrescritorModel.IEspecialidadePrescritorRepository>();
        _handler = new AtualizarStatusRequestHandler(unitOfWork.Object, _especialidadePrescritorRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarStatusEspecialidadePrescritor_ComSucesso()
    {
        // Arrange
        var request = new AtualizarStatusRequest
        {
            EspecialidadePrescritorId = new List<Guid> { Uuid.NewSequential() },
            Ativo = true
        };
        var especialidadePrescritor = EspecialidadePrescritorFakes.CriarEspecialidadePrescritorValido();

        _especialidadePrescritorRepositoryMock.Setup(r => r.ObterPorIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(especialidadePrescritor);

        _especialidadePrescritorRepositoryMock.Setup(r => r.Update(especialidadePrescritor))
            .Verifiable();

        unitOfWork.Setup(r => r.SaveChangesAsync(default))
            .ReturnsAsync(1);

        // Act
        await _handler.Handle(request, default);

        // Assert
        unitOfWork.Verify(r => r.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task AtualizarStatusEspecialidadePrescritor_ComErro()
    {
        // Arrange
        var request = new AtualizarStatusRequest
        {
            EspecialidadePrescritorId = new List<Guid> { Uuid.NewSequential() },
            Ativo = true
        };

        _especialidadePrescritorRepositoryMock.Setup(r => r.ObterPorIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((EspecialidadePrescritorModel.EspecialidadePrescritor)null);

        // Act
        var exception = await Assert.ThrowsAsync<ValidationException>(() => _handler.Handle(request, default));

        // Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork.Verify(r => r.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
