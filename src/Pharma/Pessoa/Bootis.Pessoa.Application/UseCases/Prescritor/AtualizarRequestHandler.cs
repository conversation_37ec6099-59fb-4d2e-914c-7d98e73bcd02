using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;
using Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Prescritor;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork,
    IPrescritorRepository prescritorRepository,
    IEspecialidadePrescritorRepository especialidadePrescritorRepository)
    : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        var prescritor = await prescritorRepository.ObterPrescritorAsync(request.Id);

        var (tipoRegistro, ufRegistro) = await prescritorRepository.ValidarAtualizacaoRegistroPrescritorAsync(
            request.TipoRegistroId,
            request.CodigoRegistro, request.UfRegistroId, prescritor.Id);

        prescritor.Atualizar(request.NomeCompleto,
            tipoRegistro,
            ufRegistro,
            request.CodigoRegistro,
            request.DataNascimento,
            request.Observacao,
            request.DescontoProdutosAcabados,
            request.DescontoFormulas);

        AtualizarEspecialidades(request.EspecialidadesId, prescritor);

        prescritorRepository.Update(prescritor);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public List<Guid> AtualizarEspecialidades(ICollection<Guid> especialidadesId,
        Domain.AggregatesModel.PrescritorAggregate.Prescritor prescritor)
    {
        foreach (var especialidade in prescritor.Especialidades.ToArray())
            if (!especialidadesId.Contains(especialidade.Id))
                prescritor.RemoverEspecialidade(especialidade);

        var especialidadesNoPrescritor = prescritor.Especialidades.Select(e => e.Id).ToList();
        var especialidadesParaAdicionar =
            especialidadesId.Where(id => !especialidadesNoPrescritor.Contains(id)).ToList();

        foreach (var especialidadeAdicionada in
                 especialidadePrescritorRepository.ListarEspecialidades(especialidadesParaAdicionar))
            prescritor.AdicionarEspecialidade(especialidadeAdicionada);

        return especialidadesNoPrescritor;
    }
}