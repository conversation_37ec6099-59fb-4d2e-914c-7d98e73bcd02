using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Domain.Factories;

public interface ICalculoReceitaManipuladaFactory
{
    CalculoReceitaManipulada Criar(
        ICollection<ReceitaManipuladaItem> itensEntrada,
        decimal quantidadeAlvoCalculo,
        FormaFarmaceutica formaFarmaceuticaEntrada,
        Cliente pacienteEntrada,
        Guid? prescritorIdEntrada,
        TipoDesconto? tipoDescontoManualEntrada,
        decimal? descontoManualEntrada,
        decimal? percentualDescontoManualEntrada);
}