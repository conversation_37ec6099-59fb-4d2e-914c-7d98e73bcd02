using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.Produto.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Produto.Listar;

public class ListarPrescricaoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarPrescricaoRequest, PaginatedResult<ListarPrescricaoResponse>>
{
    public async Task<PaginatedResult<ListarPrescricaoResponse>> Handle(
        ListarPrescricaoRequest request, CancellationToken cancellationToken)
    {
        var classes = request.ClasseProdutoIds.Distinct().ToList();
        
        if (!classes.Contains((int)TipoClasseProdutoAbreviacao.MateriaPrima))
            classes.Add((int)TipoClasseProdutoAbreviacao.MateriaPrima);

        var filtroClasses = string.Join(",", classes);

        var sql = new StringBuilder($"""
                                         SELECT 
                                             p.descricao,
                                             p.id AS produto_id,
                                             ps.id AS sinonimo_id,
                                             p.id AS produto_prescricao_id,
                                             fp.id AS formula_padrao_id,
                                             p.sequencia_group_tenant AS codigo,
                                             COALESCE(ps.sinonimo, p.descricao) AS sinonimo,
                                             p.descricao AS produto_descricao,
                                             p.classe_produto_id AS classe_produto,
                                             mp.unidade_prescricao_id,
                                             um.abreviacao AS unidade_prescricao_abreviacao,
                                             COUNT(DISTINCT fi.id) AS quantidade_componentes,
                                             CASE 
                                                 WHEN pi.id IS NOT NULL THEN true 
                                                 ELSE false 
                                             END AS tem_incompatibilidade
                                         FROM produtos p
                                         LEFT JOIN produtos_sinonimo ps ON ps.produto_id = p.id
                                         LEFT JOIN formulas_padrao fp 
                                             ON fp.produto_id = p.id
                                            AND fp.forma_farmaceutica_id = @FormaFarmaceuticaId
                                         LEFT JOIN formulas_padrao_item fi ON fi.formula_padrao_id = fp.id
                                         LEFT JOIN produtos_materia_prima mp ON mp.produto_id = p.id
                                         LEFT JOIN unidades_medida um ON um.id = mp.unidade_prescricao_id
                                         LEFT JOIN produtos_incompativel pi 
                                             ON (pi.produto_id = p.id OR pi.produto_incompativel_id = p.id)
                                            AND pi.group_tenant_id = p.group_tenant_id
                                         WHERE p.classe_produto_id IN ({filtroClasses})
                                           AND p.group_tenant_id = @GroupTenantId
                                         !@SEARCH_CONDITION@!
                                         GROUP BY 
                                             p.id, ps.id, ps.sinonimo, p.sequencia_group_tenant, p.descricao,
                                             p.classe_produto_id, fp.id, mp.unidade_prescricao_id, um.abreviacao,
                                             CASE WHEN pi.id IS NOT NULL THEN true ELSE false END
                                     """);

        var searchDescricao = new StringSearchField
        {
            Field = "p.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchSinonimo = new StringSearchField
        {
            Field = "ps.sinonimo",
            CompareType = StringCompareType.Contains
        };

        var searchCodigo = new NumberSearchField
        {
            Field = "p.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        var result = await PaginatedQueryBuilder<ListarPrescricaoRequest, ListarPrescricaoResponse>
            .Create(connection, sql.ToString(), request, userContext)
            .AddSearchField(searchDescricao)
            .AddSearchField(searchSinonimo)
            .AddSearchField(searchCodigo)
            .ExecuteAsync();

        var queryResult = new List<ListarPrescricaoResponse>();

        foreach (var item in result.Data)
        {
            var responseItem = new ListarPrescricaoResponse
            {
                FormulaPadraoId = item.FormulaPadraoId,
                Codigo = item.Codigo,
                Descricao = item.Sinonimo,
                ProdutoDescricao = item.SinonimoId != null ? item.Descricao : null,
                ProdutoId = item.ProdutoId,
                SinonimoId = item.SinonimoId,
                Id = item.SinonimoId ?? item.ProdutoId,
                ClasseProduto = item.ClasseProduto,
                QuantidadeComponentes = item.QuantidadeComponentes,
                UnidadePrescricaoId = item.UnidadePrescricaoId,
                UnidadePrescricaoAbreviacao = item.UnidadePrescricaoAbreviacao,
                TemIncompatibilidade = item.TemIncompatibilidade
            };

            queryResult.Add(responseItem);
        }

        return new PaginatedResult<ListarPrescricaoResponse>(
            result.PageIndex, result.PageSize, result.Count, queryResult);
    }
}