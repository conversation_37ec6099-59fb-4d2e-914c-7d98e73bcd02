using Bootis.Organizacional.Domain.AggregatesModel.PermissaoAggregate;
using Bootis.Organizacional.Domain.Statics;

namespace Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;

public class PermissaoUsuario
{
    private Permissao _permissao;

    protected PermissaoUsuario()
    {
    }

    public PermissaoUsuario(Usuario usuario, int permissaoId, bool ativo)
    {
        Usuario = usuario;
        PermissaoId = permissaoId;
        Ativo = ativo;
    }

    public PermissaoUsuario(Guid usuarioId, int permissaoId, bool ativo)
    {
        UsuarioId = usuarioId;
        PermissaoId = permissaoId;
        Ativo = ativo;
    }

    public Guid UsuarioId { get; private set; }
    public int PermissaoId { get; init; }
    public bool Ativo { get; private set; }

    public Permissao Permissao
    {
        get
        {
            if (_permissao != null)
                return _permissao;

            return _permissao = PermissoesStatic.GetPermissao(PermissaoId)!;
        }
    }

    public virtual Usuario Usuario { get; init; }

    public void AtualisarSituacao(bool situacao)
    {
        Ativo = situacao;
    }
}