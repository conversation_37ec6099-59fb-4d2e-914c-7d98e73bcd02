using Bootis.Catalogo.Application.Requests.Mensagem.Cadastrar;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Mensagem;

public static class CadastrarVinculadoProdutoCommandFake
{
    public static CadastrarVinculadoProdutoRequest CreateCadastrarVinculadoProdutoCommandValido()
    {
        return new CadastrarVinculadoProdutoRequest
        {
            Descricao = "Teste",
            ExibeFichaPesagem = true,
            ExibeImpressaoRotulo = true,
            ExibeRotulagem = true,
            ExibeVenda = true,
            ProdutoId = Uuid.NewSequential()
        };
    }
}
