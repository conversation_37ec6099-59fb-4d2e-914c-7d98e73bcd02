using Bootis.Producao.Application.Requests.Laboratorio.Cadastrar;
using UUIDNext;

namespace Bootis.Producao.UnitTests.Fixtures.Laboratorio;

public class CadastrarLaboratorioCommandFake
{
    public static CadastrarRequest CriarCadastrarLaboratorioCommandValido()
    {
        return new CadastrarRequest
        {
            NomeLaboratorio = "Teste",
            EmpresaId = Uuid.NewSequential(),
            LocalEstoqueId = Uuid.NewSequential(),
            ModeloOrdemManipulacaoId = Uuid.NewSequential()
        };
    }
}
