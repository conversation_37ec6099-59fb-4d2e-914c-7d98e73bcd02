using Bootis.Pessoa.Application.Requests.Prescritor.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.Prescritor;

public class CadastrarRapidoPrescritorRequestValidator : AbstractValidator<CadastrarRapidoRequest>
{
    public CadastrarRapidoPrescritorRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.NomeCompleto)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.NomeCompleto)));

        RuleFor(c => c.TipoRegistroId)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.TipoRegistroId)));

        RuleFor(c => c.CodigoRegistro)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.CodigoRegistro)));

        RuleFor(c => c.UfRegistroId)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.UfRegistroId)));
    }
}