using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Listar;

public class ListarResellResponse
{
    public Guid ReceitaManipuladaId { get; set; }
    public string ClienteDescricao { get; set; } = string.Empty;
    public string ReceitaDescricao { get; set; } = string.Empty;
    public DateTime DataEmissao { get; set; }
    public int DuracaoPrevistaDias { get; set; }
    public DateTime PrevisaoTermino { get; set; }
    public int DuracaoPrevistaDiasExibicao => (PrevisaoTermino.Date - DateTime.UtcNow).Days;
    
    public SituacaoUsoReceita Situacao => PrevisaoTermino.Date < DateTime.UtcNow ? 
        SituacaoUsoReceita.Encerrado : SituacaoUsoReceita.EmUso;
    
    public List<string> MateriasPrimas { get; set; } = new();
}