using System.Data;
using Bootis.Organizacional.Application.Requests.Empresa.Validar;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Empresa.Validar;

public class ValidarCnpjRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ValidarCnpjRequest>
{
    public async Task Handle(ValidarCnpjRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT emp.cnpj 
                                                     FROM empresas emp
                                                     WHERE emp.cnpj = @cnpj) THEN 1 ELSE 0 END AS existe_registro;
                           """;

        var result = await connection.QuerySingleAsync<bool>(sql,
            new { cnpj = request.Cnpj, groupTenantId = userContext.GroupTenantId });

        if (result) throw new DomainException(Localizer.Instance.GetMessage_Empresa_CnpjExistente(request.Cnpj));
    }
}