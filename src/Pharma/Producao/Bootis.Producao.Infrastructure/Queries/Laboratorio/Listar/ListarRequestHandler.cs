using System.Data;
using Bootis.Producao.Application.Requests.Laboratorio.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.Laboratorio.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               la.nome_laboratorio AS laboratorio,
                               la.id,
                               emp.nome_fantasia AS empresa,
                               lo.descricao AS local_estoque
                           FROM laboratorios la
                           LEFT JOIN empresas emp ON emp.id = la.empresa_id
                           LEFT JOIN locais_estoque lo ON lo.id = la.local_estoque_id
                           WHERE la.group_tenant_id = @GroupTenantId
                                 !@SEARCH_CONDITION@!
                           """;

        var searchLaboratorio = new StringSearchField
        {
            Field = "LA.nome_laboratorio",
            CompareType = StringCompareType.Contains
        };

        var searchEmpresa = new StringSearchField
        {
            Field = "EMP.nome_fantasia",
            CompareType = StringCompareType.Contains
        };

        var searchLocalEstoque = new StringSearchField
        {
            Field = "LO.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchLaboratorio)
            .AddSearchField(searchEmpresa)
            .AddSearchField(searchLocalEstoque)
            .ExecuteAsync();
    }
}