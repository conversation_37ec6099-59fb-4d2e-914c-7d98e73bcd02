namespace Bootis.Organizacional.Application.Dtos;

public class PermissaoGrupoDto
{
    public PermissaoGrupoDto()
    {
    }

    public PermissaoGrupoDto(PermissaoDto permissaoDtoResponse)
    {
        PermissaoId = permissaoDtoResponse.Id;
        Nome = permissaoDtoResponse.Nome;
    }

    public int PermissaoId { get; set; }
    public string Nome { get; set; }
    public bool Ativo { get; set; }
}