using System.Data;
using Bootis.Catalogo.Application.Requests.ProdutoMensagem.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoMensagem.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               mm.descricao AS mensagem_descricao,
                               mm.id AS mensagem_id
                           FROM mensagem_produtos mp
                                    LEFT JOIN mensagens mm ON mm.id = mp.mensagem_id
                                    LEFT JOIN produtos prod ON prod.id = mp.produto_id
                           WHERE prod.id = @ProdutoId
                             AND prod.group_tenant_id = @GroupTenantId
                             !@SEARCH_CONDITION@!
                           """;

        var searchMensagem = new StringSearchField
        {
            Field = "MM.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchMensagem)
            .ExecuteAsync();
    }
}