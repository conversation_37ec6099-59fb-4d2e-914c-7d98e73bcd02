using Asp.Versioning;
using Bootis.Catalogo.Application.Requests.CalculoConversaoUnidadeMedida.Calcular;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Estoque.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Estoque")]
[Route("estoque/v{version:apiVersion}/[controller]")]
public class ConversaoUnidadeMedidaController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [Route("Calcular")]
    [ProducesResponseType(typeof(CalcularConversaoUnidadeMedidaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Calcular(CalcularConversaoUnidadeMedidaRequest request)
    {
        var resultado = await mediator.Send(request);

        return Ok(resultado);
    }

    [HttpPost]
    [Route("CalcularValor")]
    [ProducesResponseType(typeof(CalcularConversaoValorUnidadeMedidaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CalcularValor(CalcularConversaoValorUnidadeMedidaRequest request)
    {
        var resultado = await mediator.Send(request);

        return Ok(resultado);
    }

    [HttpPost]
    [Route("CalcularComLote")]
    [ProducesResponseType(typeof(CalcularConversaoUnidadeMedidaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CalcularLote(CalcularConversaoUnidadeMedidaLoteRequest request)
    {
        var resultado = await mediator.Send(request);

        return Ok(resultado);
    }
}