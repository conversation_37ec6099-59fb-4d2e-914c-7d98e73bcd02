using System.Data;
using System.Text;
using Bootis.Estoque.Application.Requests.Inventario.Listar;
using Bootis.Shared.Application.Interfaces;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Inventario.Listar;

public class ListarResumoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarResumoRequest, IEnumerable<ListarResumoResponse>>
{
    public async Task<IEnumerable<ListarResumoResponse>> Handle(ListarResumoRequest request,
        CancellationToken cancellationToken)
    {
        var parametros = new DynamicParameters();

        var sql = new StringBuilder($"""
                                     SELECT le.id,
                                            le.descricao AS local_estoque_descricao,
                                            emp.nome_fantasia AS empresa_descricao,
                                            count(distinct prod.id) AS contagem_produto,
                                            count(distinct lot.id) AS contagem_lote
                                       FROM saldos_estoque se
                                            LEFT JOIN locais_estoque le ON le.id = se.local_estoque_id
                                            LEFT JOIN empresas emp ON emp.id = le.empresa_id
                                            LEFT JOIN lotes lot ON lot.id = se.lote_id
                                            LEFT JOIN produtos prod ON prod.id = se.produto_id
                                            LEFT JOIN sub_grupos sg ON sg.id = prod.sub_grupo_id
                                            LEFT JOIN produto_grupos gru ON gru.id = sg.grupo_id
                                      WHERE se.group_tenant_id = '{userContext.GroupTenantId}'
                                     """);

        if (!request.Filtros.Any(c =>
                c.LocalEstoqueId is null && c.GrupoId is null && c.SubGrupoId is null))
        {
            var whereEspecificacao = GerarFiltroEspecificacoesPorLinha(request.Filtros, parametros);

            sql.Append(whereEspecificacao);
        }

        sql.Append("""
                   GROUP BY le.id,
                            le.descricao,
                            emp.nome_fantasia
                                           
                   """);

        return await connection.QueryAsync<ListarResumoResponse>(sql.ToString(), parametros);
    }

    public static string GerarFiltroEspecificacoesPorLinha(
        List<Filtro> filtros,
        DynamicParameters parametros)
    {
        if (filtros == null || filtros.Count == 0)
            return string.Empty;

        var filtrosOr = new List<string>();

        for (var i = 0; i < filtros.Count; i++)
        {
            var partes = new List<string>();
            var filtro = filtros[i];

            if (filtro.LocalEstoqueId.HasValue)
            {
                partes.Add($"le.id = @Local_{i}");
                parametros.Add($"Local_{i}", filtro.LocalEstoqueId.Value);
            }

            if (filtro.GrupoId.HasValue)
            {
                partes.Add($"gru.id = @Grupo_{i}");
                parametros.Add($"Grupo_{i}", filtro.GrupoId.Value);
            }

            if (filtro.SubGrupoId.HasValue)
            {
                partes.Add($"sg.id = @SubGrupo_{i}");
                parametros.Add($"SubGrupo_{i}", filtro.SubGrupoId.Value);
            }

            if (partes.Count > 0) filtrosOr.Add("(" + string.Join(" AND ", partes) + ")");
        }

        if (filtrosOr.Count == 0)
            return string.Empty;

        return " AND (" + string.Join(" OR ", filtrosOr) + ")";
    }
}