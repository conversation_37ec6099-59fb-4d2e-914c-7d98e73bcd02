using Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate;
using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Pessoa.Application.UseCases.Cliente;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.UnitTests.Fixtures.ClienteEndereco;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Pessoa.UnitTests.Application.ClienteEndereco;

public class CadastrarEnderecoRequestHandlerTests : BaseTest
{
    private readonly Mock<ICidadeRepository> _cidadeRepositoryMock;
    private readonly Mock<IClienteRepository> _clienteRepositoryMock;
    private readonly Mock<IEstadoRepository> _estadoRepositoryMock;
    private readonly CadastrarEnderecoRequestHandler _handler;
    private readonly Mock<IPaisRepository> _paisRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarEnderecoRequestHandlerTests()
    {
        _clienteRepositoryMock = new Mock<IClienteRepository>();
        _paisRepositoryMock = new Mock<IPaisRepository>();
        _estadoRepositoryMock = new Mock<IEstadoRepository>();
        _cidadeRepositoryMock = new Mock<ICidadeRepository>();
        _handler = new CadastrarEnderecoRequestHandler(unitOfWork.Object, _clienteRepositoryMock.Object,
            _paisRepositoryMock.Object, _estadoRepositoryMock.Object, _cidadeRepositoryMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AdicionarClienteEndereco_ExecutadoComSucesso_Completo()
    {
        //Arrange
        var cmd = CadastrarRequestClienteEnderecoFakes.CreateClienteEnderecoValido();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();
        var cliente = ClienteFakes.CriarClienteValidoFisico();

        var model = new Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco(cliente.Id, pais,
            estado, cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal,
            cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _clienteRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(cliente);

        _paisRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(pais));

        _estadoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(estado));

        _cidadeRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(cidade));

        _clienteRepositoryMock.Setup(l => l.Update(cliente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _clienteRepositoryMock
            .Verify(l => l.Update(It.Is<Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente>(m => m.Id == model.Id)),
                Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AdicionarClienteEndereco_ExecutadoComSucesso_Requerido()
    {
        //Arrange
        var cmd = CadastrarRequestClienteEnderecoFakes.CreateClienteEnderecoRequerido();
        var cliente = ClienteFakes.CriarClienteValidoFisico();

        var model = new Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco(cliente.Id, null,
            null, null, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal,
            cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _clienteRepositoryMock
            .Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(cliente);

        _clienteRepositoryMock.Setup(l => l.Update(cliente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _clienteRepositoryMock
            .Verify(l => l.Update(It.Is<Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente>(m => m.Id == model.Id)),
                Times.Once);
    }

    [Fact]
    public async Task AdicionarClienteEndereco_ExecutadoComErro_ClienteNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarRequestClienteEnderecoFakes.CreateClienteEnderecoValido();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();
        var cliente = ClienteFakes.CriarClienteValidoFisico();

        var model = new Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco(cliente.Id, pais, estado,
            cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep,
            cmd.Descricao, cmd.Logradouro);

        _clienteRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult<Pessoa.Domain.AggregatesModel.ClienteAggregate.Cliente>(default));

        _clienteRepositoryMock.Setup(l => l.Update(cliente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        var exception = await Record.ExceptionAsync(() => _handler
            .Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork.Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task AdicionarClienteEndereco_ExecutadoComErro_PaisNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarRequestClienteEnderecoFakes.CreateClienteEnderecoValido();
        var cliente = ClienteFakes.CriarClienteValidoFisico();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var model = new Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco(cliente.Id, pais, estado,
            cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _clienteRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(cliente));

        _paisRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult<Pais>(default));

        _clienteRepositoryMock.Setup(l => l.Update(cliente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        _paisRepositoryMock.Verify(l => l.Add(It.IsAny<Pais>()), Times.Never);
    }

    [Fact]
    public async Task AdicionarClienteEndereco_ExecutadoComErro_EstadoNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarRequestClienteEnderecoFakes.CreateClienteEnderecoValido();
        var cliente = ClienteFakes.CriarClienteValidoFisico();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var model = new Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco(cliente.Id, pais, estado,
            cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _clienteRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(cliente));

        _paisRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(pais));

        _estadoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult<Estado>(default));

        _clienteRepositoryMock.Setup(l => l.Update(cliente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        _estadoRepositoryMock.Verify(l => l.Add(It.IsAny<Estado>()), Times.Never);
    }

    [Fact]
    public async Task AdicionarClienteEndereco_ExecutadoComErro_CidadeNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarRequestClienteEnderecoFakes.CreateClienteEnderecoValido();
        var cliente = ClienteFakes.CriarClienteValidoFisico();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var model = new Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteEndereco(cliente.Id, pais, estado,
            cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _clienteRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(cliente));

        _paisRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(pais));

        _estadoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(estado));

        _clienteRepositoryMock.Setup(l => l.Update(cliente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        _estadoRepositoryMock.Verify(l => l.Add(It.IsAny<Estado>()), Times.Never);
    }
}
