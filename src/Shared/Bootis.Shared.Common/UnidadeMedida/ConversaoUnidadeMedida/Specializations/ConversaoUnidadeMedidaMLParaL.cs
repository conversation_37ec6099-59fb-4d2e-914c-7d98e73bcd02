using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida.Specializations;

public class ConversaoUnidadeMedidaMLParaL : ConversaoUnidadeMedida
{
    public override UnidadeMedidaAbreviacao UnidadeMedidaOrigem => UnidadeMedidaAbreviacao.mL;
    public override UnidadeMedidaAbreviacao UnidadeMedidaConversao => UnidadeMedidaAbreviacao.L;
    public override decimal TaxaConversao => 0.001m;
    public override TipoCalculoDensidade TipoCalculoDensidade => TipoCalculoDensidade.Nenhum;
}