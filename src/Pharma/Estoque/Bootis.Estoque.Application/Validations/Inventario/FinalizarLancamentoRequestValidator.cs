using Bootis.Estoque.Application.Requests.Inventario.Atualizar;
using Bootis.Estoque.Domain.Dtos.Inventario;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Estoque.Application.Validations.Inventario;

public class FinalizarLancamentoRequestValidator : AbstractValidator<FinalizarLancamentoRequest>
{
    public FinalizarLancamentoRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.InventarioId)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.InventarioId)));

        RuleForEach(c => c.Itens)
            .SetValidator(new FinalizarLancamentoItemRequestValidator(localizer));
    }
}

public class FinalizarLancamentoItemRequestValidator : AbstractValidator<InventarioItemDto>
{
    public FinalizarLancamentoItemRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.InventarioItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.InventarioItemId)));

        RuleFor(c => c.QuantidadeInventariada)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.QuantidadeInventariada)));

        RuleFor(c => c.UnidadeMedidaId)
            .IsInEnum();

        RuleFor(c => c.UsuarioId)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.UsuarioId)));
    }
}