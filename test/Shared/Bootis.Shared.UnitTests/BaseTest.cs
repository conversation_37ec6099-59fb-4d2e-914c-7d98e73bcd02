using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.Security;
using Microsoft.Extensions.Localization;
using Moq;

namespace Bootis.Shared.UnitTests;

public abstract class BaseTest
{
    private static readonly Mock<IStringLocalizer> Mock = new();
    public static readonly Mock<IUserContext> UserContext = new();

    public static readonly Mock<IDomainEventDispatcher> mockDomainEventDispatcher = new();
    public static readonly Mock<IServiceProvider> mockServiceProvider = new();


    static BaseTest()
    {
        Mock.Setup(l => l[It.IsAny<string>()])
            .Returns((string key) => new LocalizedString(key, key));

        Localizer.Initialize(Mock.Object);

        mockServiceProvider.Setup(l => l.GetService(typeof(IDomainEventDispatcher)))
            .Returns(mockDomainEventDispatcher.Object);
        mockServiceProvider.Setup(l => l.GetService(typeof(IUserContext))).Returns(UserContext.Object);

        DomainEvent.Configure(mockServiceProvider.Object);
        DomainEvent.SetDispatcher(mockDomainEventDispatcher.Object);
    }

    protected BaseTest()
    {
        var userSession = new UserSession
        {
            UserIdentity = new UserIdentity
            {
                GroupTenantId = DefaultsValues.BootisId,
                TenantId = DefaultsValues.BootisId,
                UserId = DefaultsValues.BootisId
            },
            UserPreferences = new UserPreferences()
        };
        UserContext.Setup(l => l.UserSession).Returns(userSession);
    }
}