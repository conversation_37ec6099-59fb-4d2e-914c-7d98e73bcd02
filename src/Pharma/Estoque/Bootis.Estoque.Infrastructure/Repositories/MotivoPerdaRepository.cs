using Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class MotivoPerdaRepository(IDbContext context) : Repository<MotivoPerda>(context), IMotivoPerdaRepository
{
    public Task<MotivoPerda> ObterPorIdAsync(Guid id)
    {
        return DbSet
            .FirstOrDefaultAsync(c => c.Id == id);
    }
}