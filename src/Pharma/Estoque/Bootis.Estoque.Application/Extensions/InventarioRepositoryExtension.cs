using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Estoque.Application.Extensions;

public static class InventarioRepositoryExtension
{
    public static async Task<Inventario> ObterInventarioAsync(this IInventarioRepository repository,
        Guid inventarioId)
    {
        var inventario = await repository.ObterInventarioPorIdAsync(inventarioId).ConfigureAwait(false);

        if (inventario is null)
            throw new ValidationException(nameof(inventarioId),
                Localizer.Instance.GetMessage_Inventario_GuidNaoEncontrado(inventarioId));

        return inventario;
    }
}