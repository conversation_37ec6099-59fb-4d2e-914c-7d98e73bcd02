using Asp.Versioning;
using Bootis.Estoque.Application.Requests.SituacaoLote.Listar;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Estoque.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Estoque")]
[Route("estoque/v{version:apiVersion}/[controller]")]
public class SituacaoLoteController(IMediator mediator) : ControllerBase
{
    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(ListarResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Listar()
    {
        var result = await mediator.Send(new ListarRequest());

        return Ok(result);
    }
}