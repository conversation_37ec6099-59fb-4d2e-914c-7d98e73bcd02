using Bootis.Catalogo.Application.Requests.EmbalagemClassificacao.Remover;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Catalogo.Application.Extensions;

public static class EmbalagemClassificacaoRepositoryExtension
{
    public static async Task<EmbalagemClassificacao> ObterEmbalagemClassificacaoAsync(
        this IEmbalagemClassificacaoRepository repository, Guid? id)
    {
        if (id.HasValue)
        {
            var embalagemClassificacao = await repository.ObterPorIdAsync(id.Value);

            if (embalagemClassificacao is null)
                throw new ValidationException(nameof(id),
                    Localizer.Instance.GetMessage_EmbalagemClassificacao_GuidNaoEncontrado(id.Value));

            return embalagemClassificacao;
        }

        return null;
    }

    public static async Task<List<EmbalagemClassificacao>> ObterEmbalagensClassificacaoAsync(
        this IEmbalagemClassificacaoRepository repository, IEnumerable<Guid> ids)
    {
        var embalagensClassificacao = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !embalagensClassificacao.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_EmbalagemClassificacao_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return embalagensClassificacao;
    }

    public static async Task ValidarEmbalagemClassificacaoPorDescricaoAsync(
        this IEmbalagemClassificacaoRepository repository, string descricao)
    {
        var hasDescricao = await repository.ValidarPorDescricaoAsync(descricao);

        if (hasDescricao)
            throw new ValidationException(nameof(descricao),
                Localizer.Instance.GetMessage_EmbalagemClassificacao_DescricaoExistente(descricao));
    }

    public static async Task ValidarEmbalagemClassificacaoPorIdEDescricaoAsync(
        this IEmbalagemClassificacaoRepository repository, Guid id, string descricao)
    {
        var hasDescricao = await repository.ValidarPorIdEDescricaoAsync(id, descricao);

        if (!hasDescricao) await ValidarEmbalagemClassificacaoPorDescricaoAsync(repository, descricao);
    }

    public static async Task ValidarVinculoEmbalagemClassificacaoProdutoEmbalgemAsync(
        this IEmbalagemClassificacaoRepository repository,
        EmbalagemClassificacao embalagemClassificacao, Produto produto)
    {
        var hasVinculo =
            await repository.ValidarVinculoComProdutoEmbalagemAsync(embalagemClassificacao.Id,
                produto.Id);

        if (!hasVinculo)
            throw new ValidationException(nameof(embalagemClassificacao.Id),
                Localizer.Instance.GetMessage_EmbalagemClassificacao_VinculoInexistente(
                    embalagemClassificacao.Id, produto.Id));
    }

    public static async Task VerificarDependenciasAsync(this IEmbalagemClassificacaoRepository repository,
        IEnumerable<EmbalagemClassificacao> embalagensClassificacao)
    {
        var removeResponses = new List<RemoverResponse>();
        var ids = embalagensClassificacao.Select(c => c.Id).ToList();
        var embalagensDependentes = await repository.VerificarDependenciaAsync(ids);

        removeResponses.AddRange(embalagensDependentes.Select(e => new RemoverResponse
        {
            Id = e.Id,
            Descricao = e.Descricao
        }));

        if (removeResponses.Count > 0)
            throw new DomainException(nameof(GlobalErrorCode), (int)GlobalErrorCode.Global_Remover,
                removeResponses.ToArray());
    }
}