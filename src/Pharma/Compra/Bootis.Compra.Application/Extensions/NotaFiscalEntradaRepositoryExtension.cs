using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Remover;
using Bootis.Compra.Common;
using Bootis.Compra.Domain.AggregatesModel.CfopAggregate;
using Bootis.Compra.Domain.AggregatesModel.CstCsosnAggregate;
using Bootis.Compra.Domain.AggregatesModel.NaturezaOperacaoAggregate;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Compra.Domain.AggregatesModel.TipoFreteAggregate;
using Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;
using Bootis.Compra.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Compra.Application.Extensions;

public static class NotaFiscalEntradaRepositoryExtension
{
    public static async Task<NotaFiscalEntrada> ObterNotaFiscalEntradaAsync(
        this INotaFiscalEntradaRepository repository, Guid id)
    {
        var notaFiscalEntrada = await repository.ObterPorIdAsync(id);

        if (notaFiscalEntrada is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_NotaFiscalEntrada_GuidNaoEncontrado(id));

        return notaFiscalEntrada;
    }

    public static async Task<List<NotaFiscalEntrada>> ObterNotasFiscaisAsync(
        this INotaFiscalEntradaRepository repository, IEnumerable<Guid> ids)
    {
        var notasFiscais = await repository.ObterNotasFiscaisEntradaPorIds(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !notasFiscais.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_NotaFiscalEntrada_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return notasFiscais;
    }

    public static async Task<NotaFiscalEntradaItem> ObterItemAsync(this INotaFiscalEntradaRepository repository,
        Guid notaFiscalEntradaItemId)
    {
        var notaFiscalEntradaItem = await repository
            .ObterNotaFiscalEntradaItemAsync(notaFiscalEntradaItemId);

        if (notaFiscalEntradaItem is null)
            throw new ValidationException(nameof(notaFiscalEntradaItemId),
                Localizer.Instance.GetMessage_NotaFiscalEntradaItem_NaoEncontrado(notaFiscalEntradaItemId));

        return notaFiscalEntradaItem;
    }

    public static async Task VerificarDependenciasAsync(this INotaFiscalEntradaRepository repository,
        IEnumerable<NotaFiscalEntrada> notasFiscaisEntrada)
    {
        var removeResponses = new List<RemoverResponse>();

        foreach (var notaFiscalEntrada in notasFiscaisEntrada)
            if (await repository.VerificarDependenciaAsync(notaFiscalEntrada.Id) > 0)
                removeResponses.Add(new RemoverResponse
                {
                    NomeFornecedor = notaFiscalEntrada.Fornecedor.Nome,
                    Numero = notaFiscalEntrada.Numero,
                    Serie = notaFiscalEntrada.Serie
                });

        if (removeResponses.Count > 0)
            throw new DomainException(nameof(CompraErrorCode), (int)CompraErrorCode.NotaFiscal_Remover,
                removeResponses.ToArray());
    }

    public static async Task<List<Cfop>> ObterCfopsAsync(this INotaFiscalEntradaRepository repository,
        IEnumerable<Guid> cfopIds)
    {
        var cfops = await repository.ObterCfopsPorIdsAsync(cfopIds);

        var idsInvalidos = cfopIds
            .Distinct()
            .Where(id => !cfops.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_Cfop_IdNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(NotaFiscalEntradaItemDto.CfopId),
                idsInvalidos);

        return cfops;
    }

    public static async Task<List<CstCsosn>> ObterCstsAsync(this INotaFiscalEntradaRepository repository,
        IEnumerable<Guid> cstIds)
    {
        var csts = await repository.ObterCstsPorIdsAsync(cstIds);

        var idsInvalidos = cstIds
            .Distinct()
            .Where(id => !csts.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_CstCsosn_NaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(NotaFiscalEntradaItemDto.CstCsosnId),
                idsInvalidos);

        return csts;
    }

    public static async Task<NaturezaOperacao> ObterNaturezaOperacaoAsync(this INotaFiscalEntradaRepository repository,
        Guid naturezaOperacaoId)
    {
        var naturezaOperacao = await repository.ObterNaturezaOperacaoPorIdAsync(naturezaOperacaoId);

        if (naturezaOperacao is null)
            throw new ValidationException(nameof(naturezaOperacao),
                Localizer.Instance.GetMessage_NaturezaOperacao_IdNaoEncontrado(naturezaOperacaoId));

        return naturezaOperacao;
    }

    public static async Task<TipoFrete> ObterTipoFreteAsync(this INotaFiscalEntradaRepository repository,
        Guid tipoFreteId)
    {
        var tipoFrete = await repository.ObterTipoFretePorIdAsync(tipoFreteId);

        if (tipoFrete is null)
            throw new ValidationException(nameof(tipoFrete),
                Localizer.Instance.GetMessage_TipoFrete_IdNaoEncontrado(tipoFreteId));

        return tipoFrete;
    }

    public static async Task ValidarNotaFiscalPorNumeroEhSerieEhFornecedorIdAsync(
        this INotaFiscalEntradaRepository repository, int numero, int serie, Guid fornecedorId)
    {
        var hasRegistro = await repository.ValidarPorNumeroEhSerieEhFornecedorIdAsync(numero, serie, fornecedorId);

        if (hasRegistro)
            throw new ValidationException(nameof(numero),
                Localizer.Instance.GetMessage_NotaFiscalEntrada_JaLancada());
    }
}