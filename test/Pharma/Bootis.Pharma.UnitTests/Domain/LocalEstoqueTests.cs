using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.ValuesObject;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;

namespace Bootis.Estoque.UnitTests.Domain;

public class LocalEstoqueTests : BaseTest
{
    private readonly LocalEstoque _localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

    [Theory]
    [InlineData("Local Estoque Teste1", TipoEstoque.Venda)]
    [InlineData("Local Estoque Teste2", TipoEstoque.Armazenagem)]
    public void LocalEstoque_DeveAtualizarLocalEstoque(string descricao, TipoEstoque tipoEstoque)
    {
        //Arrange 
        var empresa = EmpresaFakes.GerarEmpresaValido();

        //Action
        _localEstoque.AtualizarLocalEstoque(descricao, tipoEstoque, empresa);

        //Assert
        Assert.Equal(descricao, _localEstoque.Descricao);
        Assert.Equal(tipoEstoque, _localEstoque.TipoEstoque);
        Assert.Equal(empresa, _localEstoque.Empresa);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void LocalEstoque_DeveAtualizarStatus(bool status)
    {
        //Action
        _localEstoque.AtualizarStatus(status);

        //Assert
        Assert.Equal(status, _localEstoque.Ativo);
    }

    [Fact]
    public void LocalEstoque_DeveValidarEmpresaSePertenceAoLocalEstoque_Valido()
    {
        //Arrange
        var empresa = EmpresaFakes.GerarEmpresaValido();

        _localEstoque.AtualizarLocalEstoque("Teste", TipoEstoque.Venda, empresa);

        //Action
        _localEstoque.ValidarEmpresaSePertenceAoLocalEstoque(empresa);

        //Assert
        Assert.Equal(empresa, _localEstoque.Empresa);
    }

    [Fact]
    public void LocalEstoque_DeveValidarEmpresaSePertenceAoLocalEstoque_Invalido()
    {
        //Arrange
        var endereco = new Endereco("49000000", "rua a", 21, "torre a", "atalaia",
            "aracaju", "sergipe");

        var empresa = new Empresa("Teste", "Teste", "033234567000123",
            2, endereco, null, null, "1234567",
            "12345678912345", "12345678912", null, null, true, "11999999999");

        //Action
        var exception = Record.Exception(() => _localEstoque.ValidarEmpresaSePertenceAoLocalEstoque(empresa));

        //Assert
        Assert.IsType<ValidationException>(exception);
    }
}
