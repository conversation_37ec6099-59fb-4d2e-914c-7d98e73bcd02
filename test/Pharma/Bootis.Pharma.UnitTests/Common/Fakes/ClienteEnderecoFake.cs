using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class ClienteEnderecoFake
{
    public static ClienteEndereco CriarClienteEnderecoValido()
    {
        var cliente = ClienteFakes.CriarClienteValidoFisico();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        return new ClienteEndereco(cliente.Id,
            pais,
            estado,
            cidade,
            "Bairro Teste",
            "1",
            "teste",
            true,
            "05017-020",
            "Teste",
            "Rua Teste");
    }

    public static List<ClienteEndereco> CriarClienteEnderecoValidoLista()
    {
        var cliente = ClienteFakes.CriarClienteValidoFisico();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var endereco1 = new ClienteEndereco(cliente.Id,
            pais,
            estado,
            cidade,
            "Bairro Teste",
            "1",
            "teste",
            true,
            "05017-020",
            "Endere�o 1",
            "Rua Teste");

        var endereco2 = new ClienteEndereco(cliente.Id,
            pais,
            estado,
            cidade,
            "Bairro Teste",
            "1",
            "teste",
            false,
            "05017-020",
            "Endere�o 2",
            "Rua Teste");

        var endereco3 = new ClienteEndereco(cliente.Id,
            pais,
            estado,
            cidade,
            "Bairro Teste",
            "1",
            "teste",
            false,
            "05017-020",
            "Endere�o 3",
            "Rua Teste");

        endereco2.AtualizarPrincipal(true);

        return new List<ClienteEndereco> { endereco1, endereco2, endereco3 };
    }
}
