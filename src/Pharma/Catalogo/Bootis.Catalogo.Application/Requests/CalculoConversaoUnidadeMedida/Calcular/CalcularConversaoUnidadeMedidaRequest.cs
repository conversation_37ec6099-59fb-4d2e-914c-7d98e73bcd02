using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Catalogo.Application.Requests.CalculoConversaoUnidadeMedida.Calcular;

public class CalcularConversaoUnidadeMedidaRequest(ICollection<ConversaoUnidadeMedidaACalcular> conversoes)
    : IRequest<CalcularConversaoUnidadeMedidaResponse>
{
    public ICollection<ConversaoUnidadeMedidaACalcular> Conversoes { get; set; } = conversoes;
}

public class ConversaoUnidadeMedidaACalcular
{
    public UnidadeMedidaAbreviacao UnidadeMedidaOrigemId { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaConversaoId { get; set; }
    public decimal Quantidade { get; set; }
    public decimal? Densidade { get; set; }
}