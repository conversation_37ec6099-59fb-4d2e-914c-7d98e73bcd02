using Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate;
using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Pessoa.Application.UseCases.Fornecedor;
using Bootis.Pessoa.UnitTests.Fixtures.FornecedorEndereco;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using FornecedorModel = Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;

namespace Bootis.Pessoa.UnitTests.Application.FornecedorEndereco;

public class AtualizarEnderecoRequestHandlerTests : BaseTest
{
    private readonly Mock<ICidadeRepository> _cidadeRepositoryMock;
    private readonly Mock<IEstadoRepository> _estadoRepositoryMock;
    private readonly Mock<FornecedorModel.IFornecedorRepository> _fornecedorRepositoryMock;
    private readonly AtualizarEnderecoRequestHandler _handler;
    private readonly Mock<IPaisRepository> _paisRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarEnderecoRequestHandlerTests()
    {
        _fornecedorRepositoryMock = new Mock<FornecedorModel.IFornecedorRepository>();
        _paisRepositoryMock = new Mock<IPaisRepository>();
        _estadoRepositoryMock = new Mock<IEstadoRepository>();
        _cidadeRepositoryMock = new Mock<ICidadeRepository>();
        _handler = new AtualizarEnderecoRequestHandler(unitOfWork.Object, _fornecedorRepositoryMock.Object,
            _paisRepositoryMock.Object, _estadoRepositoryMock.Object, _cidadeRepositoryMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AtualizarFornecedorEndereco_ExecutadoComSucesso_Completo()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorEnderecoFakes.CriarCommandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var model = new FornecedorModel.FornecedorEndereco(fornecedor.Id, pais, estado, cidade,
            cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _fornecedorRepositoryMock
            .Setup(l => l.ObterEnderecoPorEnderecoIdAsync(cmd.FornecedorEnderecoId))
            .Returns(Task.FromResult(model));

        _fornecedorRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>()))
            .ReturnsAsync(fornecedor);

        _paisRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(pais));

        _estadoRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(estado));

        _cidadeRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult(cidade));

        _fornecedorRepositoryMock.Setup(l => l.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _fornecedorRepositoryMock
            .Verify(l => l.Update(It.Is<FornecedorModel.Fornecedor>(m => m.Nome == fornecedor.Nome)), Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AtualizarFornecedorEndereco_ExecutadoComSucesso_Requerido()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorEnderecoFakes.CriarCommandRequerido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var model = new FornecedorModel.FornecedorEndereco(fornecedor.Id, null, null, null,
            cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _fornecedorRepositoryMock
            .Setup(l => l.ObterEnderecoPorEnderecoIdAsync(cmd.FornecedorEnderecoId))
            .Returns(Task.FromResult(model));

        _fornecedorRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<int>()))
            .Returns(Task.FromResult(fornecedor));

        _fornecedorRepositoryMock.Setup(l => l.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _fornecedorRepositoryMock
            .Verify(l => l.Update(It.Is<FornecedorModel.Fornecedor>(m => m.Nome == fornecedor.Nome)), Times.Once);
    }

    [Fact]
    public async Task AtualizarFornecedorEndereco_ExecutadoComErro_EnderecoNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorEnderecoFakes.CriarCommandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        _fornecedorRepositoryMock
            .Setup(l => l.ObterEnderecoPorEnderecoIdAsync(cmd.FornecedorEnderecoId))
            .Returns(Task.FromResult<FornecedorModel.FornecedorEndereco>(default));


        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(fornecedor);

        _fornecedorRepositoryMock.Setup(l => l.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task AtualizarFornecedorEndereco_ExecutadoComErro_PaisNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorEnderecoFakes.CriarCommandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var model = new FornecedorModel.FornecedorEndereco(fornecedor.Id, pais, estado,
            cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _fornecedorRepositoryMock
            .Setup(l => l.ObterEnderecoPorEnderecoIdAsync(cmd.FornecedorEnderecoId))
            .Returns(Task.FromResult(model));

        _fornecedorRepositoryMock.Setup(l => l.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        _paisRepositoryMock.Verify(l => l.Update(It.IsAny<Pais>()), Times.Never);
    }


    [Fact]
    public async Task AtualizarFornecedorEndereco_ExecutadoComErro_EstadoNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorEnderecoFakes.CriarCommandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var model = new FornecedorModel.FornecedorEndereco(fornecedor.Id, pais, estado,
            cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _fornecedorRepositoryMock.Setup(l => l.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        _estadoRepositoryMock.Verify(l => l.Update(It.IsAny<Estado>()), Times.Never);
    }

    [Fact]
    public async Task AtualizarFornecedorEndereco_ExecutadoComErro_CidadeNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarRequestFornecedorEnderecoFakes.CriarCommandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var pais = PaisFakes.CriarPaisValidoBrasil();
        var estado = EstadoFakes.CriarEstadoValidoSP();
        var cidade = CidadeFakes.CriarCidadeValidaSaoPaulo();

        var model = new FornecedorModel.FornecedorEndereco(fornecedor.Id, pais, estado,
            cidade, cmd.Bairro, cmd.Numero, cmd.Complemento, cmd.Principal, cmd.Cep, cmd.Descricao, cmd.Logradouro);

        _fornecedorRepositoryMock.Setup(l => l.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        _cidadeRepositoryMock
            .Verify(l => l.Update(It.IsAny<Cidade>()), Times.Never);
    }
}
