using DevExpress.XtraReports.UI;
using DevExpress.XtraReports.Web.Extensions;

namespace Bootis.Report.Web.Services;

public class ReportLoader : IReportLoader
{
    public async Task<XtraReport> LoadReportAsync(ReportStorageWebExtension reportStorage, string reportUrl)
    {
        var data = await reportStorage.GetDataAsync(reportUrl);
        if (data is null) return null;

        var report = new XtraReport();
        using var reportStream = new MemoryStream(data);
        report.LoadLayout(reportStream);
        return report;
    }

    public async Task<byte[]> ExportToPdfAsync(XtraReport report)
    {
        await report.CreateDocumentAsync();
        
        using var stream = new MemoryStream();
        await report.ExportToPdfAsync(stream);
        return stream.ToArray();
    }
}