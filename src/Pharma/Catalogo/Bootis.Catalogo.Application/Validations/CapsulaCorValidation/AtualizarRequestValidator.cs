using Bootis.Catalogo.Application.Requests.CapsulaCor.Atualizar;
using FluentValidation;

namespace Bootis.Catalogo.Application.Validations.CapsulaCorValidation;

public class AtualizarRequestValidator : AbstractValidator<AtualizarRequest>
{
    public AtualizarRequestValidator()
    {
        RuleFor(c => c.Cor<PERSON>ap<PERSON>)
            .MaximumLength(50)
            .NotEmpty();

        RuleFor(c => c.Transparente)
            .NotNull();
    }
}