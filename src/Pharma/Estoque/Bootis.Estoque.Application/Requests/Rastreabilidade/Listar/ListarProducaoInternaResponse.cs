using Bootis.Shared.Common.Query;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Application.Requests.Rastreabilidade.Listar;

public class ListarProducaoInternaResponse
{
    public int? TotalItens { get; set; }
    public PaginatedResult<ListarProducao> Receitas { get; set; }
}

public class ListarProducao
{
    public Guid ProducaoInternaId { get; set; }
    public DateTime DataMovimento { get; set; }
    public decimal QuantidadeMovimento { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao => UnidadeMedidaId.ToString();
    public int NumeroProducao { get; set; }
    public string FormaFarmaceutica { get; set; }
    public decimal CustoMovimentacao { get; set; }
    public decimal ValorTotalReceita { get; set; }
    public string NomeOperador { get; set; }
    public string NomeFormula { get; set; }
    public string NumeroLote { get; set; }
    public DateTime DataValidade { get; set; }
    public string TipoMovimento { get; set; }
    public string Operacao { get; set; }
}