using Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Shared.Application.Events;
using MediatR;

namespace Bootis.Estoque.Application.DomainEvents;

public class
    SaldoAtualizadoNotificationHandler(ISaldoEstoqueRepository repository)
    : INotificationHandler<DomainEventNotification<SaldoAtualizadoEvent>>
{
    public async Task Handle(DomainEventNotification<SaldoAtualizadoEvent> notification,
        CancellationToken cancellationToken)
    {
        var saldoAtualizadoNotification = notification.DomainEvent;

        var saldoEstoque = await repository.GetByIdAsync(saldoAtualizadoNotification.SaldoEstoqueId);

        if (saldoEstoque != null)
        {
            saldoEstoque.AtualizarSaldo(saldoAtualizadoNotification.Saldo);

            repository.Update(saldoEstoque);
        }
    }
}