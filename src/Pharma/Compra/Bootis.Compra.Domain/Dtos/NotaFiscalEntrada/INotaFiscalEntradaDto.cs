namespace Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;

public interface INotaFiscalEntradaDto
{
    public int Numero { get; set; }
    public int Serie { get; set; }
    public DateOnly DataEmissao { get; set; }
    public DateOnly DataEntrega { get; set; }
    public decimal? ValorBaseIcms { get; set; }
    public decimal? ValorIcms { get; set; }
    public decimal? ValorBaseIcmsSubstituicao { get; set; }
    public decimal? ValorIcmsSubstituicao { get; set; }
    public decimal? ValorFrete { get; set; }
    public decimal? ValorSeguro { get; set; }
    public decimal? ValorDesconto { get; set; }
    public decimal? ValorOutrasDespesas { get; set; }
    public decimal? ValorIpi { get; set; }
    public string InformacoesComplementares { get; set; }
}