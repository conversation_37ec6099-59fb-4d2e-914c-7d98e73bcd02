using Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Catalogo.Infrastructure.EntityConfigurations;

public class ProdutoFichaTecnicaEspecificacaoEntityTypeConfiguration : IEntityTypeConfiguration<ProdutoFichaTecnicaEspecificacao>
{
    public void Configure(EntityTypeBuilder<ProdutoFichaTecnicaEspecificacao> builder)
    {
        builder.ToTable("produtos_ficha_tecnica_especificacao")
            .HasKey(c => new { c.ProdutoFichaTecnicaId, c.EnsaioControleQualidadeId, c.BibliografiaId });

        builder
            .HasOne(c => c.ProdutoFichaTecnica)
            .WithMany(c => c.Especificacoes)
            .HasForeignKey(c => c.ProdutoFichaTecnicaId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        builder
            .HasOne(c => c.EnsaioControleQualidade)
            .WithMany()
            .HasForeignKey(c => c.EnsaioControleQualidadeId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder
            .HasOne(c => c.Bibliografia)
            .WithMany()
            .HasForeignKey(c => c.BibliografiaId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder
            .Property(c => c.Especificacao)
            .Observacao(Shared.Infrastructure.Enums.TamanhoTexto.CentoECinquenta)
            .IsRequired(false);

        builder
            .Property(c => c.MostrarCertificadoAnalise)
            .HasDefaultValue(false)
            .IsRequired();
    }
}