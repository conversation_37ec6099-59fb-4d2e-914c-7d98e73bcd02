using System.Data;
using Bootis.Catalogo.Application.Requests.ProdutoDiluido.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoDiluido.Obter;

public class
    ObterProdutoDiluidoRequestHandler(
        IUserContext userContext,
        IDbConnection connection)
    : IRequestHandler<ObterProdutoDiluidoRequest, ObterProdutoDiluidoResponse>
{
    public async Task<ObterProdutoDiluidoResponse> Handle(ObterProdutoDiluidoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT prod.id AS produto_id,
                                  prod.descricao AS produto_descricao,
                                  ff.id AS forma_farmaceutica_id,
                                  ff.descricao AS forma_farmaceutica_descricao,
                                  pd.dosagem_minima,
                                  pd.dosagem_maxima,
                                  pd.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  pd.diluicao,
                                  pd.se_todas_formas_farmaceuticas,
                                  pd.se_qualquer_dosagem
                           FROM produtos_diluido pd
                                    LEFT JOIN produtos prod ON prod.id = pd.produto_id
                                    LEFT JOIN formas_farmaceutica ff ON ff.id = pd.forma_farmaceutica_id
                                    LEFT JOIN unidades_medida un ON un.id = pd.unidade_medida_id
                           WHERE pd.id = @id
                             AND pd.group_tenant_id = @groupTenantId;
                           """;

        var result = await connection.QueryFirstOrDefaultAsync<ObterProdutoDiluidoResponse>(sql,
                new { request.Id, groupTenantId = userContext.GroupTenantId })
            .ConfigureAwait(false);

        if (result is not null) return result;
        var message = Localizer.Instance.GetMessage_ProdutoDiluido_GuidNaoEncontrado(request.Id);
        throw new DomainException(message);
    }
}