using System.Data;
using Bootis.Organizacional.Application.Requests.Conglomerado.Obter;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Conglomerado.Obter;

public class ObterRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT con.id,
                                  con.nome,
                                  con.data_inclusao,
                                  con.ativo,
                                  emp.id AS empresa_matriz_id,
                                  emp.nome_fantasia AS empresa_matriz_nome_fantasia
                           FROM conglomerados con
                                LEFT JOIN conglomerado_matriz cmat ON
                                          cmat.id = con.id
                                LEFT JOIN empresas emp ON
                                          emp.id = cmat.empresa_id
                           WHERE con.id = @id;

                           SELECT emp.id AS empresa_id,
                                  emp.nome_fantasia AS empresa_nome_fantasia,
                                  emp.cnpj AS empresa_cnpj,
                                  emp.razao_social AS empresa_razao_social,
                                  emp.tipo_id AS tipo_empresa_id
                           FROM conglomerados con
                                LEFT JOIN empresas emp ON
                                          emp.conglomerado_id = con.id
                           WHERE con.id = @id;
                           """;

        await using var query = await connection.QueryMultipleAsync(sql, new { id = request.Id })
            .ConfigureAwait(false);
        var response = await query.ReadSingleOrDefaultAsync<ObterResponse>();

        if (response is null)
            throw new DomainException(
                Localizer.Instance.GetMessage_Conglomerado_GuidNaoEncontrado(request.Id));

        var empresasVinculadas = await query.ReadAsync<EmpresasResponse>();

        response.Empresas = empresasVinculadas;

        return response;
    }
}