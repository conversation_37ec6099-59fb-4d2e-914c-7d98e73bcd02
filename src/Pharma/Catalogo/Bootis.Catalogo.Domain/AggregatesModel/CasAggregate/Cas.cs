using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.CasAggregate;

public class Cas : Entity, IAggregateRoot
{
    public Cas()
    {
    }

    public Cas(string numeroCas, string nomeProduto)
    {
        NumeroCas = numeroCas;
        NomeProduto = nomeProduto;
    }

    public string NumeroCas { get; private set; }
    public string NomeProduto { get; private set; }
}