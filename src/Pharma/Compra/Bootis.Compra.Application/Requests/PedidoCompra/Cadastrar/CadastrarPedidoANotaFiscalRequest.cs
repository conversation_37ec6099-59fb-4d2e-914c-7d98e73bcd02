using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using MediatR;

namespace Bootis.Compra.Application.Requests.PedidoCompra.Cadastrar;

public class CadastrarPedidoANotaFiscalRequest : IRequest<bool>
{
    public Domain.AggregatesModel.NotaFiscalEntradaAggregate.NotaFiscalEntrada NotaFiscalEntrada { get; set; }
    public IEnumerable<NotaFiscalEntradaPedido> NotaFiscalEntradaPedido { get; set; }
}