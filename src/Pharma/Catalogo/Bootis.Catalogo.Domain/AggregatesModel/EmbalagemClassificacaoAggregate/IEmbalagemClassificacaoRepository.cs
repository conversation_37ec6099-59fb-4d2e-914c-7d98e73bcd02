using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;

public interface IEmbalagemClassificacaoRepository : IRepository<EmbalagemClassificacao>, IScopedService
{
    Task<EmbalagemClassificacao> ObterPorIdAsync(Guid id);
    Task<List<EmbalagemClassificacao>> ObterPorIdsAsync(IEnumerable<Guid> ids);
    Task<bool> ValidarPorDescricaoAsync(string descricao);
    Task<bool> ValidarPorIdEDescricaoAsync(Guid id, string descricao);
    Task<bool> ValidarVinculoComProdutoEmbalagemAsync(Guid embalagemClassificacaoId, Guid produtoId);
    Task<IEnumerable<(Guid Id, string Descricao)>> VerificarDependenciaAsync(IEnumerable<Guid> ids);
}