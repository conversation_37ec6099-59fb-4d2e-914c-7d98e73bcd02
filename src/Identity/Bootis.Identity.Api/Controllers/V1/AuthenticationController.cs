using Asp.Versioning;
using Bootis.Identity.Application.Authentication.Models;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Identity.Api.Controllers.V1;

[ApiVersion(1)]
[Route("v{version:apiVersion}/auth")]
[ApiController]
public class AuthenticationController(IMediator mediator, ILogger<AuthenticationController> logger)
    : ControllerBase
{
    [HttpPost("login")]
    [ProducesResponseType(typeof(LoginResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        logger.LogInformation("Processing login request for email: {Email}", request.Email);

        var response = await mediator.Send(request);

        logger.LogInformation("Login successful for email: {Email}", request.Email);
        return Ok(response);
    }

    [HttpPost("refresh")]
    [ProducesResponseType(typeof(LoginResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        logger.LogDebug("Processing refresh token request");

        var response = await mediator.Send(request);

        return response.Match<IActionResult>(
            success =>
            {
                logger.LogDebug("Refresh token successful");
                return Ok(success);
            },
            error =>
            {
                logger.LogWarning("Refresh token failed: {Error}", error);
                return Unauthorized(error);
            }
        );
    }

    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Logout([FromBody] LogoutRequest request)
    {
        logger.LogInformation("Processing logout request");

        await mediator.Send(request);

        logger.LogInformation("Logout completed successfully");
        return Ok(new { message = "Logout realizado com sucesso" });
    }
}