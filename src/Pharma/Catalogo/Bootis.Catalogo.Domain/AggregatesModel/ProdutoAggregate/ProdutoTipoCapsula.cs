using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.TipoCapsulaAggregate;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;

public class ProdutoTipoCapsula
{
    public ProdutoTipoCapsula()
    {
    }

    public ProdutoTipoCapsula(Produto produto,
        CapsulaCor capsulaCor,
        TipoCapsula tipoCapsula,
        CapsulaTamanho numeroCapsula)
    {
        Produto = produto;
        CapsulaCor = capsulaCor;
        CapsulaTamanho = numeroCapsula;
        TipoCapsula = tipoCapsula;
    }

    public Guid ProdutoId { get; private set; }
    public Guid? TipoCapsulaId { get; private set; }
    public Guid? NumeroCapsulaId { get; private set; }
    public Guid? CapsulaCorId { get; private set; }

    public void AtualizarProdutoTipoCapsula(CapsulaCor capsulaCor, TipoCapsula tipoCapsula,
        CapsulaTamanho capsulaTamanho)
    {
        CapsulaCor = capsulaCor;
        TipoCapsula = tipoCapsula;
        CapsulaTamanho = capsulaTamanho;
    }

    #region Navigation properties

    public virtual Produto Produto { get; set; }
    public virtual TipoCapsula TipoCapsula { get; set; }
    public virtual CapsulaTamanho CapsulaTamanho { get; set; }
    public virtual CapsulaCor CapsulaCor { get; set; }

    #endregion
}