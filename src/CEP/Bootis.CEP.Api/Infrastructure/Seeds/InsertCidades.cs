using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace Bootis.CEP.Api.Infrastructure.Seeds;

public class InsertCidades : ISeed
{
    public int Order => 3;

    public void Seed(DbContext dbContext)
    {
        var fullFileName =
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Infrastructure", "Seeds", "Data", "Cidades.csv");

        var npgsqlConnection = dbContext.Database.GetDbConnection() as NpgsqlConnection;

        using var writer =
            npgsqlConnection!.BeginTextImport(
                "COPY cidades (estado_id, id, nome, cep) FROM STDIN (FORMAT csv, HEADER false, DELIMITER ',')");
        using var reader = new StreamReader(fullFileName);
        while (reader.ReadLine() is { } line)
            writer.WriteLine(line);
    }
}