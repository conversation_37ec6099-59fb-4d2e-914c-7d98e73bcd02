using Bootis.Pessoa.Domain.AggregatesModel.TipoContatoAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;

public class ClienteContato : Contato, IAggregateRoot, ITenant
{
    public ClienteContato()
    {
    }

    public ClienteContato(Cliente cliente,
        string identificacao) : this()
    {
        Cliente = cliente;
        Identificacao = identificacao;
    }

    public ClienteContato(Cliente cliente,
        Guid tipoContatoId,
        string identificacao,
        bool principal,
        string observacao) : this()
    {
        Cliente = cliente;
        TipoContatoId = tipoContatoId;
        Identificacao = identificacao;
        Principal = principal;
        Observacao = observacao;
    }

    public Guid ClienteId { get; private set; }
    public virtual Cliente Cliente { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void Atualizar(Guid clienteId, string identificacao,
        Guid tipoContatoId, bool contatoPrincipal, string observacao)
    {
        ClienteId = clienteId;
        Identificacao = identificacao;
        TipoContatoId = tipoContatoId;
        Principal = contatoPrincipal;
        Observacao = observacao;
    }

    public void AtualizarPrincipal(bool contatoPrincipal)
    {
        Principal = contatoPrincipal;
    }
}