using Bootis.Shared.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.ValueGeneration;

namespace Bootis.Shared.Infrastructure.ValueGenerators;

public class GroupTenantSequenceValueGenerator : ValueGenerator<int>
{
    public override bool GeneratesTemporaryValues => false;

    public override int Next(EntityEntry entry)
    {
        var context = (BootisContext)entry.Context;

        if (!entry.CurrentValues.TryGetValue("GroupTenantId", out Guid groupTenantId))
            return -1;

        if (groupTenantId == Guid.Empty)
            groupTenantId = context.GroupTenantId;

        return context.GetNextSequence(entry.Entity.GetType().Name, groupTenantId, Guid.Empty);
    }
}