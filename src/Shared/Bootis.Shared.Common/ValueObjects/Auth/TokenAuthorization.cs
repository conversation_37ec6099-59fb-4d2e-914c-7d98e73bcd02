using System.Collections.Immutable;
using Bootis.Shared.Common.Helpers;

namespace Bootis.Shared.Common.ValueObjects.Auth;

public record AuthorizeToken(Guid SessionId, ImmutableHashSet<int> RequiredRoles);

public class AuthorizeSuccess
{
    public bool IsActive { get; set; } = true;
    public bool Authorized { get; set; } = true;
    public Guid UserId { get; set; }
    public Guid SessionId { get; set; }
    public IEnumerable<int> GrantedRoles { get; set; } = [];
}

public class AuthorizeFailure(AuthenticationError errorCode)
{
    public bool IsActive { get; set; }
    public bool Authorized { get; set; }
    public AuthenticationError ErrorCode { get; set; } = errorCode;
    public string ErrorMessage { get; } = AuthHelper.GetDefaultErrorMessage(errorCode);
    public IEnumerable<int> MissingRoles { get; set; } = [];
    public DateTimeOffset FailedAt { get; set; } = DateTimeOffset.UtcNow;
}