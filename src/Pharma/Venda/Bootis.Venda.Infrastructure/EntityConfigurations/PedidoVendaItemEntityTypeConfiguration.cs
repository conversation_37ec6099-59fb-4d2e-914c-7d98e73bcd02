using Bootis.Shared.Infrastructure.Extensions;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Venda.Infrastructure.EntityConfigurations;

public class PedidoVendaItemEntityTypeConfiguration : IEntityTypeConfiguration<PedidoVendaItem>
{
    public void Configure(EntityTypeBuilder<PedidoVendaItem> builder)
    {
        builder.UseTptMappingStrategy()
            .ToTable("pedidos_venda_itens");

        builder.Property(c => c.Descricao)
            .Texto()
            .IsRequired();

        builder
            .Property(c => c.Quantidade)
            .Estoque()
            .IsRequired();

        builder
            .Property(c => c.ValorUnitario)
            .Financeiro()
            .IsRequired();

        builder
            .Property(c => c.ValorDescontoUnitario)
            .Financeiro()
            .IsRequired();

        builder
            .Property(c => c.ValorTotalItem)
            .Financeiro()
            .IsRequired();

        builder
            .Property(c => c.Status)
            .IsRequired();

        builder
            .Property(c => c.Selecionado)
            .HasDefaultValue(true)
            .IsRequired();

        builder.Property(c => c.Ordem)
            .HasDefaultValue(0)
            .IsRequired();
    }
}