using Asp.Versioning;
using Bootis.Producao.Application.Requests.Periodo.Listar;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Producao.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Producao")]
[Route("periodo/v{version:apiVersion}/[controller]")]
public class PeriodoController(IMediator mediator) : ControllerBase
{
    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(ListarResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ListarTipoPessoa()
    {
        var request = new ListarRequest();

        var result = await mediator.Send(request);

        return Ok(result);
    }
}