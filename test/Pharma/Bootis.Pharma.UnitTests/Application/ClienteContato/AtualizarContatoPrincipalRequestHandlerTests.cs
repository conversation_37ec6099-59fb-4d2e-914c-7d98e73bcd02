using Bootis.Pessoa.Application.UseCases.Cliente;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.UnitTests.Fixtures.ClienteContato;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Pessoa.UnitTests.Application.ClienteContato;

public class AtualizarContatoPrincipalRequestHandlerTests : BaseTest
{
    private readonly Mock<IClienteRepository> _clienteRepositoryMock;
    private readonly AtualizarContatoPrincipalRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarContatoPrincipalRequestHandlerTests()
    {
        _clienteRepositoryMock = new Mock<IClienteRepository>();
        _handler = new AtualizarContatoPrincipalRequestHandler(unitOfWork.Object, _clienteRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarClienteContatoPrincipal_ExecutadoComSucesso()
    {
        // Arrange
        var cmd = AtualizarRequestClienteContatoPrincipalFakes.CriarCommandValido();
        var contatos = ClienteContatoFake.CriarCommandValidoLista();

        _clienteRepositoryMock.Setup(l => l.ObterContatoPorContatoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(contatos[2]);

        _clienteRepositoryMock.Setup(l => l.ObterContatosPorIdEhIdAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
            .ReturnsAsync(contatos);

        _clienteRepositoryMock.Setup(l =>
                l.Update(It.IsAny<Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        // Action
        await _handler.Handle(cmd, default);

        // Assert
        _clienteRepositoryMock
            .Verify(
                l => l.Update(It.Is<Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato>(c =>
                    c.Identificacao == "Contato 1" && c.Principal == false)), Times.Once);
    }

    [Fact]
    public async Task AtualizarClienteContatoPrincipal_ExecutadoComErro()
    {
        // Arrange
        var cmd = AtualizarRequestClienteContatoPrincipalFakes.CriarCommandValido();

        _clienteRepositoryMock.Setup(l => l.ObterContatoPorContatoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato)null);

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        // Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        // Assert
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
