using Bootis.Organizacional.Application.Requests.Grupo.Atualizar;
using Bootis.Organizacional.Application.UseCases.Grupo;
using Bootis.Organizacional.UnitTests.Fixtures;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using DomainAggregate = Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate;

namespace Bootis.Organizacional.UnitTests.Application.Grupo;

[Collection(nameof(AccountManagerDomainCollection))]
public class AtualizarRequestHandlerTests : BaseTest
{
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<DomainAggregate.IGrupoRepository> _mockGrupoRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarRequestHandlerTests()
    {
        _mockGrupoRepository = new Mock<DomainAggregate.IGrupoRepository>();

        _handler = new AtualizarRequestHandler(unitOfWork.Object, _mockGrupoRepository.Object);
    }

    [Fact]
    public async Task AtualizarGrupo_ExecutadoComSucesso()
    {
        //Arrange
        var model = GrupoFakes.GerarGrupoValido();

        var cmd = new AtualizarRequest
        {
            Usuarios = new List<Guid>(),
            Descricao = "teste 2",
            Id = model.Id,
            Nome = "nome 2"
        };

        _mockGrupoRepository.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(model));

        _mockGrupoRepository.Setup(l => l.ExistByNome(It.IsAny<string>())).Returns(
            Task.FromResult(false));

        _mockGrupoRepository.Setup(l => l.Update(It.IsAny<DomainAggregate.Grupo>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));


        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _mockGrupoRepository.Verify(l => l.Update(It.Is<DomainAggregate.Grupo>(m => m.SeAtivo == true)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarGrupoNomeJaExistente_ExecutadoComErro()
    {
        //Arrange
        var model = GrupoFakes.GerarGrupoValido();

        var cmd = new AtualizarRequest
        {
            Usuarios = new List<Guid>(),
            Descricao = "teste 2",
            Id = model.Id,
            Nome = "nome 2"
        };

        _mockGrupoRepository.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(model));

        _mockGrupoRepository.Setup(l => l.ExistByNome(It.IsAny<string>())).Returns(
            Task.FromResult(true));

        _mockGrupoRepository.Setup(l => l.Update(It.IsAny<DomainAggregate.Grupo>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var actionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(actionResult);
        _mockGrupoRepository.Verify(l => l.Update(It.Is<DomainAggregate.Grupo>(m => m.Id == model.Id)),
            Times.Never);
    }
}
