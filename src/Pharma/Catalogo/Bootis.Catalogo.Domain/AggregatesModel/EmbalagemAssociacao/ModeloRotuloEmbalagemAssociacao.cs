using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao;

public class ModeloRotuloEmbalagemAssociacao() : Entity()
{
    public ModeloRotuloEmbalagemAssociacao(
        ProdutoEmbalagem produtoEmbalagem,
        Guid? formaFarmaceuticaId,
        Guid modeloRotuloId) : this()
    {
        ProdutoEmbalagem = produtoEmbalagem;
        FormaFarmaceuticaId = formaFarmaceuticaId;
        ModeloRotuloId = modeloRotuloId;
    }

    public Guid ProdutoEmbalagemId { get; private set; }
    public Guid? FormaFarmaceuticaId { get; private set; }
    public Guid ModeloRotuloId { get; private set; }

    #region Navigation properties

    public virtual ProdutoEmbalagem ProdutoEmbalagem { get; set; }
    public virtual FormaFarmaceutica FormaFarmaceutica { get; set; }
    public virtual ModeloRotulo ModeloRotulo { get; set; }
    
    #endregion
}
