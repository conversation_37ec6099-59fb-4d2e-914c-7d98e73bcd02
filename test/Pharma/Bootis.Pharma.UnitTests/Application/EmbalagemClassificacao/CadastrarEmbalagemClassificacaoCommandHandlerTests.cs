using Bootis.Catalogo.Application.UseCases.EmbalagemClassificacao;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.EmbalagemClassificacao;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Producao.UnitTests.Application.EmbalagemClassificacao;

public class CadastrarEmbalagemClassificacaoCommandHandlerTests : BaseTest
{
    private readonly CadastrarRequestHandler _cadastrarEmbalagemClassificacaoCommandHandler;
    private readonly Mock<IEmbalagemClassificacaoRepository> _embalagemClassificacaoRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarEmbalagemClassificacaoCommandHandlerTests()
    {
        _embalagemClassificacaoRepositoryMock = new Mock<IEmbalagemClassificacaoRepository>();

        unitOfWork.Setup(l => l.GetRepository<IEmbalagemClassificacaoRepository>())
            .Returns(_embalagemClassificacaoRepositoryMock.Object);

        _cadastrarEmbalagemClassificacaoCommandHandler = new CadastrarRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task CadastrarEmbalagemClassificacao_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = CadastrarEmbalagemClassificacaoCommandFake.CreateCadastrarEmbalagemClassificacaoCommandValido();
        var embalagemClassificacao = EmbalagemClassificacaoFake.CreateEmbalagemClassificacao_Ativo();

        _embalagemClassificacaoRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao)).Returns(
            Task.FromResult(false));

        _embalagemClassificacaoRepositoryMock.Setup(l => l.Add(embalagemClassificacao))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _cadastrarEmbalagemClassificacaoCommandHandler.Handle(cmd, default);

        //Assert
        _embalagemClassificacaoRepositoryMock.Verify(
            l => l.Add(
                It.Is<Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao>(m =>
                    m.Descricao == embalagemClassificacao.Descricao)),
            Times.Once);
    }

    [Fact]
    public async Task CadastrarEmbalagemClassificacao_ExecutadoComErro()
    {
        //Arrange
        var cmd = CadastrarEmbalagemClassificacaoCommandFake.CreateCadastrarEmbalagemClassificacaoCommandValido();

        var model =
            new Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao(cmd.Descricao,
                true);

        _embalagemClassificacaoRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao)).Returns(
            Task.FromResult(true));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarEmbalagemClassificacaoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _embalagemClassificacaoRepositoryMock.Verify(
            l => l.Add(
                It.Is<Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao>(m =>
                    m.Descricao == model.Descricao)),
            Times.Never);
    }
}
