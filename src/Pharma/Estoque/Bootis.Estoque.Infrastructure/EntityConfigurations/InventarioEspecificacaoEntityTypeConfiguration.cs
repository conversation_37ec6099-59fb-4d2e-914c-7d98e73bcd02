using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class InventarioEspecificacaoEntityTypeConfiguration : BaseEntityTypeConfiguration<InventarioEspecificacao>
{
    public override void Configure(EntityTypeBuilder<InventarioEspecificacao> builder)
    {
        builder.ToTable("inventario_especificacoes");

        builder
            .HasOne(c => c.Inventario)
            .WithMany(c => c.InventarioEspecificacao)
            .HasForeignKey(c => c.InventarioId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(c => c.LocalEstoque)
            .WithMany()
            .HasForeignKey(c => c.LocalEstoqueId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.Grupo)
            .WithMany()
            .HasForeignKey(c => c.GrupoId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.SubGrupo)
            .WithMany()
            .HasForeignKey(c => c.SubGrupoId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        base.Configure(builder);
    }
}