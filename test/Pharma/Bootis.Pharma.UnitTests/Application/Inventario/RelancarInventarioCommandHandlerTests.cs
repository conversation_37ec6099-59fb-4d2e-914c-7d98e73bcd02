using Bootis.Estoque.Application.UseCases.Inventario;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Inventario;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.Inventario;

public class RelancarInventarioCommandHandlerTests : BaseTest
{
    private readonly RelancarRequestHandler _handler;
    private readonly Mock<IInventarioRepository> _inventarioRepositoryMock;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RelancarInventarioCommandHandlerTests()
    {
        _inventarioRepositoryMock = new Mock<IInventarioRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _handler = new RelancarRequestHandler(unitOfWork.Object, UserContext.Object, _inventarioRepositoryMock.Object,
            _usuarioRepositoryMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task RelancarLancamentoInventario_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = RelancarInventarioCommandFake.CreateRelancarInventarioCommandValido();
        var cmdInterno = FinalizarLancamentoInventarioCommandFake.CreateFinalizarLancamentoInventarioCommandValido();
        var inventario = InventarioFake.CriarInventarioValido();
        var usuarioId = 1.ToGuid();
        var saldosEstoque = new List<SaldoEstoque> { SaldoEstoqueFake.CreateSaldoEstoqueValido() };
        var responsaveisOperadorIds = new Dictionary<Guid, int>
        {
            { cmdInterno.Itens.First().UsuarioId, 1 }
        };

        inventario.IniciarLancamento(saldosEstoque, usuarioId);
        inventario.FinalizarLancamento(inventario.InventarioLancamento.First(), usuarioId, cmdInterno.Itens);

        _inventarioRepositoryMock.Setup(l => l.ObterInventarioPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(inventario));

        _inventarioRepositoryMock.Setup(l => l.Update(inventario))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _inventarioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario>(m =>
                    m.StatusInventario == inventario.StatusInventario)),
            Times.Once);
    }
}
