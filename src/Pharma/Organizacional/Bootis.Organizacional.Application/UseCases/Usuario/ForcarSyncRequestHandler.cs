using Bootis.Organizacional.Application.Requests.Usuario;
using Bootis.Organizacional.Contracts;
using Bootis.Organizacional.Contracts.Empresa;
using Bootis.Organizacional.Contracts.Grupo;
using Bootis.Organizacional.Contracts.Usuario;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Enumerations;
using MassTransit;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Usuario;

public class ForcarSyncRequestHandler(
    IBus bus,
    IUsuarioRepository usuarioRepository,
    IEmpresaRepository empresaRepository,
    IGrupoRepository grupoRepository)
    : IRequestHandler<ForcarSyncRequest>
{
    public async Task Handle(ForcarSyncRequest request, CancellationToken cancellationToken)
    {
        var empresasEvents = from empresa in await empresaRepository.ObterTodasEmpresasAsync()
            select CreateEvent(empresa);

        await bus.PublishBatch(empresasEvents, cancellationToken);

        var grupoEvents = from grupo in await grupoRepository.ObterTodosGruposAsync()
            select CreateEvent(grupo);

        await bus.PublishBatch(grupoEvents, cancellationToken);

        var usuariosEvents =
            from usuario in await usuarioRepository.ObterTodosUsuariosAsync()
            select CreateEvent(usuario);

        await bus.PublishBatch(usuariosEvents, cancellationToken);
    }

    private static UsuarioAtualizadoEvent CreateEvent(Domain.AggregatesModel.UsuarioAggregate.Usuario user)
    {
        return new UsuarioAtualizadoEvent
        {
            Email = user.Email,
            Nome = user.Nome,
            Ativo = user.Ativo,
            Id = user.Id,
            TenantId = user.TenantId,
            GroupTenantId = user.GroupTenantId,
            Grupos = user.Grupos.Select(c => c.Id),
            Permissoes = user.Permissoes.Select(c => new Permissao
            {
                Ativo = c.Ativo,
                Codigo = c.PermissaoId
            }),
            Preferencias = new UsuarioAtualizadoEvent.Preferencia
            {
                ContrasteAumentado = user.Preferencias.ContrasteAumentado,
                Idioma = user.Preferencias.Idioma,
                PadraoData = user.Preferencias.PadraoData,
                PadraoHora = user.Preferencias.PadraoHora,
                TemaUsuario = (int)user.Preferencias.TemaUsuario,
                TextoAmpliado = user.Preferencias.TextoAmpliado,
                TextoNegrito = user.Preferencias.TextoNegrito,
                TimeZone = user.Preferencias.TimeZone
            }
        };
    }

    private static EmpresaAtualizadoEvent CreateEvent(Domain.AggregatesModel.EmpresaAggregate.Empresa empresa)
    {
        return new EmpresaAtualizadoEvent
        {
            TenantId = empresa.Id,
            TipoMoedaId = empresa.Configuracao?.TipoMoedaId ?? TipoMoeda.BRL.Id
        };
    }


    private static GrupoAtualizadoEvent CreateEvent(Domain.AggregatesModel.GrupoAggregate.Grupo grupo)
    {
        return new GrupoAtualizadoEvent
        {
            Id = grupo.Id,
            Ativo = grupo.SeAtivo,
            Permissoes = grupo.Permissoes.Select(c => new Permissao { Ativo = true, Codigo = c.PermissaoId }),
            Usuarios = grupo.Usuarios.Select(c => c.Id)
        };
    }
}