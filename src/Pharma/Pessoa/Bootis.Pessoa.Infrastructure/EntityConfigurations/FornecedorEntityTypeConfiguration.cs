using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Pessoa.Infrastructure.EntityConfigurations;

public class FornecedorEntityTypeConfiguration : BaseEntityTypeConfiguration<Fornecedor>
{
    public override void Configure(EntityTypeBuilder<Fornecedor> builder)
    {
        builder.ToTable("fornecedores");

        builder
            .Property(c => c.Id)
            .IsRequired();

        builder
            .Property(c => c.Nome)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired(false);

        builder
            .HasOne(p => p.TipoFornecedor)
            .WithMany()
            .HasForeignKey(p => p.TipoFornecedorId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.LinkImagem)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired(false);

        builder
            .Property(c => c.Ativo)
            .HasDefaultValue(true)
            .IsRequired();

        builder
            .Property(c => c.TipoPessoa)
            .IsRequired();

        builder
            .Property(c => c.RazaoSocial)
            .Documento(TamanhoTexto.Cem)
            .IsRequired(false);

        builder
            .Property(c => c.Observacao)
            .Observacao(TamanhoTexto.Mil)
            .IsRequired(false);

        builder
            .Property(c => c.Cpf)
            .Documento(TamanhoTexto.Vinte)
            .IsRequired(false);

        builder
            .Property(c => c.Cnpj)
            .Documento(TamanhoTexto.Vinte)
            .IsRequired(false);

        base.Configure(builder);
    }
}