using Asp.Versioning;
using Bootis.Identity.Application.Authentication;
using Bootis.Identity.Application.Authentication.Models;
using Bootis.Shared.Common.ValueObjects.Auth;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Identity.Api.Controllers.V1;

[ApiVersion(1)]
[Route("v{version:apiVersion}/token")]
[ApiController]
[Authorize]
public class TokenController(IMediator mediator, ILogger<TokenController> logger) : ControllerBase
{
    [HttpPost("introspect")]
    [ProducesResponseType(typeof(TokenValidationSuccess), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(TokenValidationFailure), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Introspect([FromBody] TokenValidationRequest request)
    {
        logger.LogDebug("Processing token introspection request");

        var response = await mediator.Send(request);

        return response.Match<IActionResult>(
            success =>
            {
                logger.LogDebug("Token introspection successful");
                return Ok(success);
            },
            failure =>
            {
                logger.LogWarning("Token introspection failed: {Error}", failure.ErrorCode);
                return BadRequest(failure);
            }
        );
    }

    [HttpPost("authorize")]
    [ProducesResponseType(typeof(AuthorizeSuccess), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(AuthorizeFailure), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> Authorize([FromBody] AuthorizeUserRequest request)
    {
        logger.LogDebug("Processing token authorization request");

        var response = await mediator.Send(request);

        return response.Match<IActionResult>(
            success =>
            {
                logger.LogDebug("Token authorization successful");
                return Ok(success);
            },
            failure =>
            {
                logger.LogWarning("Token authorization failed: {Error}", failure.ErrorCode);

                return failure.ErrorCode switch
                {
                    AuthenticationError.InsufficientPermissions => Forbid(),
                    AuthenticationError.SessionRevoked => Unauthorized(failure),
                    _ => BadRequest(failure)
                };
            }
        );
    }

    [HttpGet("temporary")]
    [ProducesResponseType(typeof(CreateTemporaryTokenResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> CreateOneTimeToken()
    {
        try
        {
            var request = new CreateTemporaryTokenRequest();
            var response = await mediator.Send(request);

            logger.LogInformation("One-time token created for user {UserId}", User.Identity?.Name);
            return Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating one-time token");
            return StatusCode(500, "Internal server error");
        }
    }
}