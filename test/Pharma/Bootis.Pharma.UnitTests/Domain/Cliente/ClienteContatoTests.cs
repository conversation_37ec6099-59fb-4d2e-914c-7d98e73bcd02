using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.UnitTests.Fixtures.ClienteContato;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;

namespace Bootis.Pessoa.UnitTests.Domain.Cliente;

public class ClienteContatoTests : BaseTest
{
    private readonly ClienteContato ClienteContato = ClienteContatoFake.CriarCommandValido();

    [Fact]
    public void ClienteContato_DeveAtualizarClienteContato()
    {
        //Arrange 
        var cmd = AtualizarRequestClienteContatoFake.CriarCommandValido();

        //Action
        ClienteContato.Atualizar(1.ToGuid(), cmd.Identificacao, cmd.TipoContatoId, cmd.Principal, cmd.Observacao);

        //Assert
        Assert.Equal(cmd.Identificacao, ClienteContato.Identificacao);
        Assert.Equal(cmd.Principal, ClienteContato.Principal);
        Assert.Equal(cmd.TipoContatoId, ClienteContato.TipoContatoId);
    }

    [Theory]
    [InlineData(false)]
    [InlineData(true)]
    public void ClienteContato_DeveAtualizarContatoPrincipal(bool principal)
    {
        //Action
        ClienteContato.AtualizarPrincipal(principal);

        //Assert
        Assert.Equal(principal, ClienteContato.Principal);
    }
}
