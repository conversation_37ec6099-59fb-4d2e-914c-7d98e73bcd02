using System.Globalization;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Infrastructure.Query.Enums;

namespace Bootis.Shared.Infrastructure.Query.SearchFields;

public class DateSearchField : SearchField
{
    private readonly string _dateFormat = "yyyy-MM-dd";

    public override SearchType Type => SearchType.Date;

    public DateCompareType CompareType { get; init; }

    public string DateFormat
    {
        get => _dateFormat;
        init => _dateFormat = value.CorrectDateFormat();
    }


    public override bool CanSearch(string search)
    {
        return CompareType switch
        {
            DateCompareType.Exact => DateTime.TryParseExact(search, DateFormat, CultureInfo.InvariantCulture,
                DateTimeStyles.None, out _),
            DateCompareType.Contains => !string.IsNullOrWhiteSpace(search),
            _ => throw new ArgumentOutOfRangeException(nameof(CompareType))
        };
    }

    public override string GetCondition(string search)
    {
        return CompareType switch
        {
            DateCompareType.Exact when DateTime.TryParseExact(search, DateFormat, CultureInfo.InvariantCulture,
                    DateTimeStyles.None, out _) => $"CAST({Field} AS DATE) = @{Parameter}",

            DateCompareType.Contains when int.TryParse(search, out _) =>
                $"EXTRACT(YEAR FROM {Field}) = @{Parameter} OR EXTRACT(MONTH FROM {Field}) = @{Parameter} OR EXTRACT(DAY FROM {Field}) = @{Parameter}",

            DateCompareType.Contains when DateTime.TryParseExact(search, DateFormat, CultureInfo.InvariantCulture,
                    DateTimeStyles.None, out _) => $"CAST({Field} AS DATE) = @{Parameter}",

            DateCompareType.Contains => $"TO_CHAR({Field}, '{DateFormat}') LIKE @{Parameter}",

            _ => throw new ArgumentOutOfRangeException(nameof(CompareType))
        };
    }

    public override object GetConditionValue(string search)
    {
        return CompareType switch
        {
            DateCompareType.Exact when DateTime.TryParseExact(search, DateFormat, CultureInfo.InvariantCulture,
                DateTimeStyles.None, out var dateSearch) => dateSearch,

            DateCompareType.Contains when int.TryParse(search, out var intSearch) => intSearch,

            DateCompareType.Contains when DateTime.TryParseExact(search, DateFormat, CultureInfo.InvariantCulture,
                DateTimeStyles.None, out var dateSearch) => dateSearch,

            DateCompareType.Contains => $"%{search}%",

            _ => throw new ArgumentOutOfRangeException(nameof(CompareType))
        };
    }
}