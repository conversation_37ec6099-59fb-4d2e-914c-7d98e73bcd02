using Bootis.Shared.Common.Query;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Application.Requests.Rastreabilidade.Listar;

public class ListarVendasResponse
{
    public int? TotalItens { get; set; }
    public PaginatedResult<ListarVendas> Vendas { get; set; }
}

public class ListarVendas
{
    public Guid VendaId { get; set; }
    public DateTime DataMovimento { get; set; }
    public decimal QuantidadeMovimento { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao => UnidadeMedidaId.ToString();
    public int NumeroPedido { get; set; }
    public decimal CustoMovimentacao { get; set; }
    public decimal ValorUnitario { get; set; }
    public decimal ValorLiquido { get; set; }
    public decimal Desconto { get; set; }
    public string NomeOperador { get; set; }
    public string TipoMovimento { get; set; }
    public string Operacao { get; set; }
}