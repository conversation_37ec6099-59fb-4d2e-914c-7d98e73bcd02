using Asp.Versioning;
using Bootis.Catalogo.Application.Requests.EmbalagemClassificacaoFormaFarmaceutica.Cadastrar;
using Bootis.Catalogo.Application.Requests.EmbalagemClassificacaoFormaFarmaceutica.Remover;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Catalogo.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Catalogo")]
[Route("catalogo/v{version:apiVersion}/[controller]")]
public class EmbalagemClassificacaoFormaFarmaceuticaController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Cadastrar(CadastrarRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpDelete]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Remover(RemoverRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }
}