using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.UseCases.Perda;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate;
using Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Perda;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;
using MediatR;
using Moq;
using DomainAggregate = Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;

namespace Bootis.Estoque.UnitTests.Application.Perda;

public class CadastrarLancamentoPerdaCommandHandlerTest : BaseTest
{
    private readonly CadastrarRequestHandler _handler;
    private readonly Mock<DomainAggregate.IPerdaRepository> _lancamentoPerdaRepositoryMock;
    private readonly Mock<ILocalEstoqueRepository> _localEstoqueRepositoryMock;
    private readonly Mock<ILoteRepository> _loteRepositoryMock;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<IMotivoPerdaRepository> _motivoPerdaRepositoryMock;
    private readonly Mock<IProdutoRepository> _produtoRepositoryMock;
    private readonly Mock<ISaldoEstoqueRepository> _saldoEstoqueRepositoryMock;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IOperacaoEstoqueRepository> _operacaoEstoqueRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarLancamentoPerdaCommandHandlerTest()
    {
        _mediatorMock = new Mock<IMediator>();
        _saldoEstoqueRepositoryMock = new Mock<ISaldoEstoqueRepository>();
        _operacaoEstoqueRepositoryMock = new Mock<IOperacaoEstoqueRepository>();
        _motivoPerdaRepositoryMock = new Mock<IMotivoPerdaRepository>();
        _lancamentoPerdaRepositoryMock = new Mock<DomainAggregate.IPerdaRepository>();
        _localEstoqueRepositoryMock = new Mock<ILocalEstoqueRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _produtoRepositoryMock = new Mock<IProdutoRepository>();
        _loteRepositoryMock = new Mock<ILoteRepository>();
        _handler = new CadastrarRequestHandler(unitOfWork.Object, UserContext.Object,
            _lancamentoPerdaRepositoryMock.Object,
            _saldoEstoqueRepositoryMock.Object,
            _loteRepositoryMock.Object, _motivoPerdaRepositoryMock.Object, _localEstoqueRepositoryMock.Object,
            _usuarioRepositoryMock.Object, _produtoRepositoryMock.Object, _operacaoEstoqueRepositoryMock.Object, _mediatorMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido();

        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();
        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult(usuario));

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(LoteFake.CreateLoteValido()));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(It.IsAny<Guid>())).Returns(
            Task.FromResult(ProdutoFake.CreateProdutoMateriaPrimaFake()));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(localEstoque));

        _motivoPerdaRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(MotivoPerdaFake.CreateMotivoPerdaValido()));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(saldoEstoque));

        _lancamentoPerdaRepositoryMock.Setup(l => l.Add(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Once);
    }

    [Fact]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComErro_UsuarioGuidInvalido()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());


        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }

    [Fact]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComErro_LoteGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(cmd.LoteId)).Returns(
            Task.FromResult<Lote>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }

    [Fact]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComErro_LocalEstoqueGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(LoteFake.CreateLoteValido()));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult<Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }

    [Fact]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComErro_ProdutoGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(LoteFake.CreateLoteValido()));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult<Produto>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }

    [Fact]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComErro_MotivoPerdaGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(LoteFake.CreateLoteValido()));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(ProdutoFake.CreateProdutoUsoConsumoFake()));

        _motivoPerdaRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult<MotivoPerda>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }

    [Fact]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComErro_SaldoEstoqueIdNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();
        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(LoteFake.CreateLoteValido()));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(ProdutoFake.CreateProdutoUsoConsumoFake()));

        _motivoPerdaRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(MotivoPerdaFake.CreateMotivoPerdaValido()));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult<SaldoEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }

    [Fact]
    public async Task CadastrarLancamentoPerdaCommand_ExecutadoComErro_SaldoEstoqueInsuficiente()
    {
        //Arrange
        var cmd = CadastrarLancamentoPerdaCommandFake.CreateCommandValido_QuantidadeInvalidade();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();
        var saldoEstoque = SaldoEstoqueFake.CreateSaldoEstoqueValido();

        var model = new DomainAggregate.Perda(DateTime.UtcNow.ToDateOnly(),
            LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoMateriaPrimaFake().Id,
            localEstoque.Id,
            10,
            UnidadeMedidaAbreviacao.UI,
            MotivoPerdaFake.CreateMotivoPerdaValido().Id,
            "Falha Produ��o",
            usuario.Id,
            1.ToGuid());

        _loteRepositoryMock.Setup(l => l.ObterLotePorIdAsync(cmd.LoteId)).Returns(
            Task.FromResult(LoteFake.CreateLoteValido()));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(ProdutoFake.CreateProdutoUsoConsumoFake()));

        _motivoPerdaRepositoryMock.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult(MotivoPerdaFake.CreateMotivoPerdaValido()));

        _saldoEstoqueRepositoryMock
            .Setup(l => l.ObterSaldoEstoquePorLoteLocalEstoqueAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(SaldoEstoqueFake.CreateSaldoEstoqueValido()));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _lancamentoPerdaRepositoryMock
            .Verify(l => l.Add(It.Is<DomainAggregate.Perda>(m => m.ProdutoId == model.ProdutoId)), Times.Never);
    }
}
