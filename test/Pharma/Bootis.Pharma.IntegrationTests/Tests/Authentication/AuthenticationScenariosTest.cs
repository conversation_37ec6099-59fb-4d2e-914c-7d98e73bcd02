using System.Net;
using System.Net.Http.Headers;
using Bootis.Pharma.IntegrationTests.Base;
using Bootis.Pharma.IntegrationTests.Infrastructure;

namespace Bootis.Pharma.IntegrationTests.Tests.Authentication;

[Collection(IntegrationTestCollection.CollectionName)]
public class AuthenticationScenariosTest : IntegrationTestBase
{
    private readonly JwtTokenGenerator _tokenGenerator;

    public AuthenticationScenariosTest(CustomWebApplicationFactory factory) : base(factory)
    {
        _tokenGenerator = GetService<JwtTokenGenerator>();
    }

    [Fact]
    public async Task Request_SemToken_DeveRetornar401()
    {
        // Arrange - Remove any existing authorization
        Client.DefaultRequestHeaders.Authorization = null;

        // Act
        var response = await Client.GetAsync("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task Request_ComTokenInvalido_DeveRetornar401()
    {
        // Arrange
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", "token-invalido");

        // Act
        var response = await Client.GetAsync("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task Request_ComTokenForce401_DeveRetornar401()
    {
        // Arrange
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", TestAuthenticationScenarios.Force401Token);

        // Act
        var response = await Client.GetAsync("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task Request_ComTokenForce403_DeveRetornar403()
    {
        // Arrange
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", TestAuthenticationScenarios.Force403Token);

        // Act
        var response = await Client.GetAsync("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task Request_ComTokenSemRoles_DeveRetornar403()
    {
        // Arrange
        var token = TestAuthenticationScenarios.GetTokenWithoutRoles(_tokenGenerator);
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await Client.GetAsync("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }

    [Fact]
    public async Task Request_ComTokenValidoAdmin_DeveRetornar200()
    {
        // Arrange
        var token = TestAuthenticationScenarios.GetValidAdminToken(_tokenGenerator);
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await Client.GetAsync("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10");

        // Assert
        var content = await response.Content.ReadAsStringAsync();
        if (response.StatusCode != HttpStatusCode.OK)
        {
            // Output error details to understand what's failing
            Assert.True(false, $"Expected 200 but got {response.StatusCode}. Content: {content}");
        }
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        content.Should().NotBeNullOrEmpty();
        content.Should().Contain("data");
    }

    [Fact]
    public async Task Request_ComTokenValidoUser_DeveRetornar200()
    {
        // Arrange
        var token = TestAuthenticationScenarios.GetValidUserToken(_tokenGenerator);
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await Client.GetAsync("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
        content.Should().Contain("data");
    }

    [Theory]
    [InlineData("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10")]
    [InlineData("/accountmanager/v1/Usuario/Listar?PageIndex=1&PageSize=5")]
    [InlineData("/accountmanager/v1/Empresa/ListarDetalhado?PageIndex=1&PageSize=10")]
    public async Task MultipleEndpoints_ComTokenValido_DevemRetornar200(string endpoint)
    {
        // Arrange
        var token = TestAuthenticationScenarios.GetValidAdminToken(_tokenGenerator);
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await Client.GetAsync(endpoint);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        content.Should().NotBeNullOrEmpty();
        content.Should().Contain("data");
    }

    [Theory]
    [InlineData("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10")]
    [InlineData("/accountmanager/v1/Usuario/Listar?PageIndex=1&PageSize=5")]
    [InlineData("/accountmanager/v1/Empresa/ListarDetalhado?PageIndex=1&PageSize=10")]
    public async Task MultipleEndpoints_SemToken_DevemRetornar401(string endpoint)
    {
        // Arrange
        Client.DefaultRequestHeaders.Authorization = null;

        // Act
        var response = await Client.GetAsync(endpoint);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Theory]
    [InlineData("/accountmanager/v1/Usuario/ListarDetalhado?PageIndex=1&PageSize=10")]
    [InlineData("/accountmanager/v1/Usuario/Listar?PageIndex=1&PageSize=5")]
    [InlineData("/accountmanager/v1/Empresa/ListarDetalhado?PageIndex=1&PageSize=10")]
    public async Task MultipleEndpoints_ComTokenSemPermissoes_DevemRetornar403(string endpoint)
    {
        // Arrange
        Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", TestAuthenticationScenarios.Force403Token);

        // Act
        var response = await Client.GetAsync(endpoint);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
    }
}