using Bootis.Estoque.Application.Requests.Inventario.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Estoque.Application.Validations.Inventario;

public class CadastrarInventarioRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarInventarioRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.OcultarQuantidadeLancamento)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.OcultarQuantidadeLancamento)));

        RuleFor(c => c.OcultarQuantidadeImpressao)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.OcultarQuantidadeImpressao)));
    }
}