using Bootis.Compra.Application.UseCases.PedidoCompra;
using Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate;
using Bootis.Compra.Domain.Enumerations;
using Bootis.Compra.UnitTests.Fixtures;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Compra.UnitTests.Application.PedidoCompraAprovacao;

public class ReprovarPedidoCompraCommandHandlerTests : BaseTest
{
    private readonly ReprovarRequestHandler _handler;
    private readonly Mock<IPedidoCompraRepository> _pedidoCompraRepositoryMock;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public ReprovarPedidoCompraCommandHandlerTests()
    {
        _pedidoCompraRepositoryMock = new Mock<IPedidoCompraRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _handler = new ReprovarRequestHandler(unitOfWork.Object, UserContext.Object, _pedidoCompraRepositoryMock.Object,
            _usuarioRepositoryMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task ReprovarPedidoCompra_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = ReprovarPedidoCompraCommandFake.CriarCommandValido();
        var cmdInterno = CadastrarPedidoCompraCommandFake.CreateCadastrarPedidoCompraComandValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        var pedidoCompraItem = new PedidoCompraItem(pedidoCompra, ProdutoFake.CreateProdutoMateriaPrimaFake(),
            cmdInterno.PedidoCompraItens.First());

        pedidoCompra.AdicionarItem(pedidoCompraItem);
        pedidoCompra.AtualizarStatus(StatusCompra.AguardandoAprovacao, usuario, false);

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(UserContext.Object.UserId))
            .Returns(
                Task.FromResult(usuario));

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.PedidoCompraId)).Returns(
            Task.FromResult(pedidoCompra));

        _pedidoCompraRepositoryMock.Setup(l => l.Update(pedidoCompra))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(
                It.Is<Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra>(m =>
                    m.Status == StatusCompra.Reprovado)),
            Times.Once);
    }

    [Fact]
    public async Task ReprovarPedidoCompra_GuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = ReprovarPedidoCompraCommandFake.CriarCommandValido();

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.PedidoCompraId)).Returns(
            Task.FromResult<Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(
                It.Is<Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra>(m =>
                    m.Status == StatusCompra.Reprovado)),
            Times.Never);
    }

    [Fact]
    public async Task ReprovarPedidoCompra_UsuarioNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = ReprovarPedidoCompraCommandFake.CriarCommandValido();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.PedidoCompraId)).Returns(
            Task.FromResult(pedidoCompra));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(
                It.Is<Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra>(m =>
                    m.Status == StatusCompra.Reprovado)),
            Times.Never);
    }

    [Fact]
    public async Task ReprovarPedidoCompra_StatusInvalido_ExecutadoComErro()
    {
        //Arrange
        var cmd = ReprovarPedidoCompraCommandFake.CriarCommandValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        pedidoCompra.AtualizarStatus(StatusCompra.Liberado, usuario, false);

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.PedidoCompraId)).Returns(
            Task.FromResult(pedidoCompra));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(It.Is<Compra.Domain.AggregatesModel.PedidoCompraAggregate.PedidoCompra>(m =>
                m.Status == StatusCompra.Reprovado)),
            Times.Never);
    }
}
