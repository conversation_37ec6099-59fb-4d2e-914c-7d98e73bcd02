using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class LoteInformacaoTecnicaEntityTypeConfiguration : IEntityTypeConfiguration<LoteInformacaoTecnica>
{
    public void Configure(EntityTypeBuilder<LoteInformacaoTecnica> builder)
    {
        builder.ToTable("lotes_informacao_tecnica")
            .HasKey(b => new { b.LoteId });

        builder
            .HasOne(c => c.Lote)
            .WithOne(c => c.LoteInformacaoTecnica)
            .HasForeignKey<LoteInformacaoTecnica>(c => c.LoteId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .Property(c => c.<PERSON>oFornecedor)
            .ManipulacaoPrecisao()
            .IsRequired();

        builder
            .Property(c => c.FatorDiluicaoFornecedor)
            .ManipulacaoPrecisao()
            .IsRequired();

        builder
            .Property(c => c.FatorConcentracaoAgua)
            .ManipulacaoPrecisao()
            .IsRequired(false);

        builder
            .Property(c => c.ConcentracaoAgua)
            .ManipulacaoPrecisao()
            .IsRequired(false);

        builder
            .Property(c => c.FatorDiluicaoInterna)
            .ManipulacaoPrecisao()
            .IsRequired(false);

        builder
            .Property(c => c.DiluicaoInterna)
            .ManipulacaoPrecisao()
            .IsRequired(false);

        builder
            .Property(c => c.Densidade)
            .Manipulacao()
            .IsRequired();

        builder
            .HasOne(c => c.LoteOrigem)
            .WithMany()
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(c => c.Pais)
            .WithMany()
            .HasForeignKey(c => c.PaisOrigemId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        builder
            .OwnsMany(b => b.LotesUnidadeAlternativa,
                b =>
                {
                    b.ToTable("lotes_unidade_alternativa");

                    b.WithOwner();

                    b.Property(b => b.UnidadeAlternativaId)
                        .IsRequired();

                    b.Property(b => b.UnidadeAlternativaConversaoId)
                        .IsRequired();

                    b.Property(b => b.QuantidadeUnidadeAlternativa)
                        .ManipulacaoPrecisao()
                        .IsRequired();
                });
    }
}