using Asp.Versioning;
using Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;
using Bootis.Pessoa.Application.Requests.Prescritor.Cadastrar;
using Bootis.Pessoa.Application.Requests.Prescritor.Listar;
using Bootis.Pessoa.Application.Requests.Prescritor.Obter;
using Bootis.Pessoa.Application.Requests.Prescritor.Remover;
using Bootis.Pessoa.Application.Requests.Prescritor.Validar;
using Bootis.Shared.Api.Attributes;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Query;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Pessoa.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Pessoa")]
[Route("pessoa/v{version:apiVersion}/[controller]")]
public class Prescritor<PERSON>ontroller(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_Cadastrar)]
    public async Task<IActionResult> Cadastrar(CadastrarRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpPost]
    [Route("CadastrarRapido")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_Cadastrar)]
    public async Task<IActionResult> CadastrarRapido(CadastrarRapidoRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(PaginatedResult<ListarResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_VerLista)]
    public async Task<IActionResult> Listar([FromQuery] ListarRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_VerLista)]
    public async Task<IActionResult> ListarDetalhado([FromQuery] ListarDetalhadoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarTipoRegistro")]
    [ProducesResponseType(typeof(PaginatedResult<ListarTipoRegistroResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_VerLista)]
    public async Task<IActionResult> ListarTipoRegistro([FromQuery] ListarTipoRegistroRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpPut]
    [Route("AtualizarStatus")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_AlterarStatus)]
    public async Task<IActionResult> AtualizarStatus(AtualizarStatusRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_VerDetalhes)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var request = new ObterRequest { Id = id };
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterSimplificado/{id:Guid}")]
    [ProducesResponseType(typeof(ObterSimplificadoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_VerDetalhes)]
    public async Task<IActionResult> ObterSimplificado(Guid id)
    {
        var request = new ObterSimplificadoRequest { Id = id };
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterDetalhesReceita/{id:Guid}")]
    [ProducesResponseType(typeof(ObterDetalhesReceitaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_VerDetalhes)]
    public async Task<IActionResult> ObterDetalhesReceita(Guid id)
    {
        var request = new ObterDetalhesReceitaRequest { Id = id };
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpDelete]
    [ProducesResponseType(typeof(RemoverResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_Excluir)]
    public async Task<IActionResult> Remover(RemoverRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPost]
    [Route("ValidarRegistro")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ValidarRegistro([FromBody] ValidarRegistroRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPost]
    [Route("CadastrarContato")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarContatos)]
    public async Task<IActionResult> CadastrarContato(CadastrarContatoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPost]
    [Route("CadastrarDocumento")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarDocumentos)]
    public async Task<IActionResult> CadastrarDocumento(CadastrarDocumentoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPost]
    [Route("CadastrarEndereco")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarEnderecos)]
    public async Task<IActionResult> CadastrarEndereco(CadastrarEnderecoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarDetalhes)]
    public async Task<IActionResult> Atualizar(AtualizarRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarEndereco")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarEnderecos)]
    public async Task<IActionResult> AtualizarEndereco(AtualizarEnderecoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarEnderecoPrincipal")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarEnderecos)]
    public async Task<IActionResult> AtualizarEnderecoPrincipal(AtualizarEnderecoPrincipalRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarContato")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarContatos)]
    public async Task<IActionResult> AtualizarContato(AtualizarContatoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarContatoPrincipal")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarContatos)]
    public async Task<IActionResult> AtualizarContatoPrincipal(AtualizarContatoPrincipalRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("AtualizarDocumento")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarDocumentos)]
    public async Task<IActionResult> AtualizarDocumento(AtualizarDocumentoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpDelete]
    [Route("RemoverContato/{id:Guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarContatos)]
    public async Task<IActionResult> RemoverContato(Guid id)
    {
        await mediator.Send(new RemoverContatoRequest(id));

        return Ok();
    }

    [HttpDelete]
    [Route("RemoverDocumento/{id:Guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarDocumentos)]
    public async Task<IActionResult> RemoverDocumento(Guid id)
    {
        await mediator.Send(new RemoverDocumentoRequest(id));

        return Ok();
    }

    [HttpDelete]
    [Route("RemoverEndereco/{id:Guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Vendas_Prescritores_EditarEnderecos)]
    public async Task<IActionResult> RemoverEndereco(Guid id)
    {
        await mediator.Send(new RemoverEnderecoRequest(id));

        return Ok();
    }
}