using Bootis.Catalogo.Application.Requests.ModeloOrdemManipulacao.Remover;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.ModeloOrdemManipulacaoValidation;

public class RemoverRequestValidator : AbstractValidator<RemoverRequest>
{
    public RemoverRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Id)
            .NotEmpty();
    }
}