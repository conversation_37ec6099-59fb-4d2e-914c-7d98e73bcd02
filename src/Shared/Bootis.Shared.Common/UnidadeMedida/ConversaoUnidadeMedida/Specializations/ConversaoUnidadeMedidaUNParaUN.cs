using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida.Specializations;

public class ConversaoUnidadeMedidaUNParaUN : ConversaoUnidadeMedida
{
    public override UnidadeMedidaAbreviacao UnidadeMedidaOrigem => UnidadeMedidaAbreviacao.un;
    public override UnidadeMedidaAbreviacao UnidadeMedidaConversao => UnidadeMedidaAbreviacao.un;
    public override decimal TaxaConversao => 1;
    public override TipoCalculoDensidade TipoCalculoDensidade => TipoCalculoDensidade.Nenhum;
}