using System.Globalization;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Security;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace Bootis.Shared.Api.Filters;

public interface IAuthenticationConsumeFilter<T> : IFilter<ConsumeContext<T>> where T : class;

public class AuthenticationConsumeFilter<T>(
    IUserContext userContext,
    ILogger<AuthenticationConsumeFilter<T>> logger) : IAuthenticationConsumeFilter<T> where T : class
{
    private const string DefaultCulture = "pt-BR";
    private const string AuthenticationHeaderKey = "Authentication";

    public Task Send(ConsumeContext<T> context, IPipe<ConsumeContext<T>> next)
    {
        try
        {
            userContext.UserSession = context.Headers.Get<UserSession>(AuthenticationHeaderKey);

            if (userContext.UserSession is not null)
            {
                var userSession = userContext.UserSession;

                userContext.UserSession = userSession;
                SetCultureFromUserPreferences(userSession.UserPreferences);

                logger.LogDebug(
                    "User context set for message consumption: UserId={UserId}, SessionId={SessionId}, Culture={Culture}",
                    userSession.UserIdentity.UserId,
                    userSession.SessionId,
                    Thread.CurrentThread.CurrentCulture.Name);
            }
            else
            {
                logger.LogDebug("No user authentication found in message headers");
                SetDefaultCulture();
            }

            return next.Send(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing authentication in consume filter");
            SetDefaultCulture();
            return next.Send(context);
        }
    }

    public void Probe(ProbeContext context)
    {
        context.CreateScope("authentication");
    }

    private void SetCultureFromUserPreferences(UserPreferences userPreferences)
    {
        try
        {
            var culture = GetValidCulture(userPreferences.Language);
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;

            logger.LogDebug("Culture set to {Culture} from user preferences", culture.Name);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to set culture from user preferences, using default");
            SetDefaultCulture();
        }
    }

    private static void SetDefaultCulture()
    {
        var defaultCulture = new CultureInfo(DefaultCulture);
        Thread.CurrentThread.CurrentCulture = defaultCulture;
        Thread.CurrentThread.CurrentUICulture = defaultCulture;
    }

    private static CultureInfo GetValidCulture(string language)
    {
        if (string.IsNullOrWhiteSpace(language))
            return new CultureInfo(DefaultCulture);

        try
        {
            // Verifica se a cultura específica existe
            if (CultureInfo.GetCultures(CultureTypes.AllCultures)
                .Any(c => c.Name.Equals(language, StringComparison.OrdinalIgnoreCase)))
                return new CultureInfo(language);

            // Fallback para apenas o idioma (ex: "pt" de "pt-BR")
            var languageOnly = language.Split('-')[0];
            var culture = CultureInfo.GetCultures(CultureTypes.AllCultures)
                .FirstOrDefault(c =>
                    c.TwoLetterISOLanguageName.Equals(languageOnly, StringComparison.OrdinalIgnoreCase));

            return culture ?? new CultureInfo(DefaultCulture);
        }
        catch (Exception)
        {
            return new CultureInfo(DefaultCulture);
        }
    }
}