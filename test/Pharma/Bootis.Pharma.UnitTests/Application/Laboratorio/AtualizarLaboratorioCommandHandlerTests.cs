using Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Producao.Application.UseCases.Laboratorio;
using Bootis.Producao.Domain.AggregatesModel.LaboratorioAggregate;
using Bootis.Producao.UnitTests.Fixtures.Laboratorio;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Producao.UnitTests.Application.Laboratorio;

public class AtualizarLaboratorioCommandHandlerTests : BaseTest
{
    private readonly Mock<IEmpresaRepository> _empresaRepositoryMock;
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<ILaboratorioRepository> _laboratorioRepositoryMock;
    private readonly Mock<ILocalEstoqueRepository> _localEstoqueRepositoryMock;
    private readonly Mock<IModeloOrdemManipulacaoRepository> _modeloOrdemManipulacaoMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarLaboratorioCommandHandlerTests()
    {
        _laboratorioRepositoryMock = new Mock<ILaboratorioRepository>();
        _empresaRepositoryMock = new Mock<IEmpresaRepository>();
        _localEstoqueRepositoryMock = new Mock<ILocalEstoqueRepository>();
        _modeloOrdemManipulacaoMock = new Mock<IModeloOrdemManipulacaoRepository>();
        _handler = new AtualizarRequestHandler(unitOfWork.Object, _laboratorioRepositoryMock.Object,
            _empresaRepositoryMock.Object, _localEstoqueRepositoryMock.Object, _modeloOrdemManipulacaoMock.Object);
    }

    [Fact]
    public async Task AtualizarLaboratorio_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarLaboratorioCommandFake.CriarAtualizarLaboratorioCommandValido();
        var empresa = EmpresaFakes.GerarEmpresaValido();
        var localEstoque = new LocalEstoque("Teste", TipoEstoque.Armazenagem, empresa);
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();
        var modeloOrdemManipulacao = ModeloOrdemManipulacaoFake.CreateModeloOrdemManipulacao();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LaboratorioId)).Returns(
            Task.FromResult(laboratorio));

        _empresaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.EmpresaId)).Returns(
            Task.FromResult(empresa));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _modeloOrdemManipulacaoMock.Setup(l => l.ObterPorIdAsync(cmd.ModeloOrdemManipulacaoId)).Returns(
            Task.FromResult(modeloOrdemManipulacao));

        _laboratorioRepositoryMock.Setup(l => l.Update(laboratorio))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.NomeLaboratorio == laboratorio.NomeLaboratorio)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarLaboratorio_LocalEstoqueEmpresaDiferente_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarLaboratorioCommandFake.CriarAtualizarLaboratorioCommandValido();
        var empresa = EmpresaFakes.GerarEmpresaValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LaboratorioId)).Returns(
            Task.FromResult(laboratorio));

        _empresaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.EmpresaId)).Returns(
            Task.FromResult(empresa));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.NomeLaboratorio == laboratorio.NomeLaboratorio)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarLaboratorio_GuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarLaboratorioCommandFake.CriarAtualizarLaboratorioCommandValido();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LaboratorioId)).Returns(
            Task.FromResult<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.NomeLaboratorio == laboratorio.NomeLaboratorio)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarLaboratorio_EmpresaGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarLaboratorioCommandFake.CriarAtualizarLaboratorioCommandValido();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LaboratorioId)).Returns(
            Task.FromResult(laboratorio));

        _empresaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.EmpresaId)).Returns(
            Task.FromResult<Empresa>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.NomeLaboratorio == laboratorio.NomeLaboratorio)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarLaboratorio_LocalEstoqueGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarLaboratorioCommandFake.CriarAtualizarLaboratorioCommandValido();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();
        var empresa = EmpresaFakes.GerarEmpresaValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LaboratorioId)).Returns(
            Task.FromResult(laboratorio));

        _empresaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.EmpresaId)).Returns(
            Task.FromResult(empresa));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult<LocalEstoque>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.NomeLaboratorio == laboratorio.NomeLaboratorio)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarLaboratorio_LaboratorioDescricaoExistente_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarLaboratorioCommandFake.CriarAtualizarLaboratorioCommandValido();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();
        var empresa = EmpresaFakes.GerarEmpresaValido();
        var localEstoque = LocalEstoqueFake.CreateLocalEstoqueValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LaboratorioId)).Returns(
            Task.FromResult(laboratorio));

        _empresaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.EmpresaId)).Returns(
            Task.FromResult(empresa));

        _localEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque));

        _laboratorioRepositoryMock.Setup(l =>
            l.ValidarPorIdEEmpresaIdEDescricaoAsync(cmd.LaboratorioId, empresa.Id, cmd.Nome)).Returns(
            Task.FromResult(false));

        _laboratorioRepositoryMock.Setup(l => l.ValidarPorEmpresaIdEDescricaoAsync(empresa.Id, cmd.Nome)).Returns(
            Task.FromResult(true));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.NomeLaboratorio == laboratorio.NomeLaboratorio)),
            Times.Never);
    }
}
