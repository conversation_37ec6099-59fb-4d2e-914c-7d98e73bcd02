using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Identity.Domain.Entities;

public class Group : ITenant, IAggregateRoot
{
    public Guid Id { get; set; }

    public bool IsActive { get; set; }

    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }


    #region Navigation Properties

    public virtual ICollection<GroupPermission> GroupPermissions { get; set; } = new List<GroupPermission>();
    public virtual ICollection<UserGroup> UserGroups { get; set; } = new List<UserGroup>();

    #endregion
}