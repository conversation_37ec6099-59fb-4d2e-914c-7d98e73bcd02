using UUIDNext;

namespace Bootis.Shared.Domain.SeedWork;

public abstract class Entity : Entity<Guid>
{
    public override Guid Id { get; protected set; }

    public void GenerateId()
    {
        if (Id == Guid.Empty)
            Id = Uuid.NewSequential();
    }
}

public abstract class Entity<TId> : ILifecycleEvents where TId : struct
{
    public abstract TId Id { get; protected set; }

    public bool IsRemoved { get; private set; }

    public virtual void OnAdded()
    {
    }

    public virtual void OnUpdated()
    {
    }

    public virtual void OnRemoved()
    {
    }

    public virtual void Remove()
    {
        IsRemoved = true;
    }
}