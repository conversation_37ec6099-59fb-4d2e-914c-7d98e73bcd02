using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoAssociado.Obter;

public class ObteProdutoAssociadoRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder($"""
                                     SELECT pro.id AS produto_id,
                                            pro.descricao AS produto_descricao,
                                            proa.id AS produto_associado_id,
                                            proa.descricao AS produto_associado_descricao,
                                            ff.id AS forma_farmaceutica_id,
                                            ff.descricao AS forma_farmaceutica_descricao,
                                            pa.dosagem_minima,
                                            pa.dosagem_maxima,
                                            pa.unidade_medida_dosagem,
                                            und.abreviacao AS unidade_medida_dosagem_abreviacao,
                                            pa.quantidade_associada,
                                            pa.unidade_medida_quantidade_associada,
                                            unq.abreviacao AS unidade_medida_quantidade_associada_abreviacao,
                                            pa.tipo_relacao_quantidade,
                                            pa.acumula
                                     FROM produtos_associado pa
                                              LEFT JOIN produtos pro ON pro.id = pa.produto_id
                                              LEFT JOIN produtos proa ON proa.id = pa.produto_associado_id
                                              LEFT JOIN formas_farmaceutica ff ON ff.id = pa.forma_farmaceutica_id
                                              LEFT JOIN unidades_medida unq ON unq.id = pa.unidade_medida_quantidade_associada
                                              LEFT JOIN unidades_medida und ON und.id = pa.unidade_medida_dosagem
                                     WHERE pa.id = '{request.Id}'
                                       AND pa.group_tenant_id = '{userContext.GroupTenantId}';
                                     """);

        var result = await connection.QuerySingleOrDefaultAsync<ObterResponse>(sql.ToString(),
            new { request.Id, userContext.GroupTenantId });

        if (result is null)
        {
            var message = Localizer.Instance.GetMessage_ProdutoAssociado_GuidNaoEncontrado(request.Id);
            throw new DomainException(message);
        }

        return result;
    }
}