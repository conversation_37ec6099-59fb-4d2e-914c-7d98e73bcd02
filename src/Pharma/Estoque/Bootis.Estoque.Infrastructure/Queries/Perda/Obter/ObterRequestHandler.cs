using System.Data;
using System.Text;
using Bootis.Estoque.Application.Requests.Perda.Obter;
using Bootis.Estoque.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Perda.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder($"""
                                     SELECT lp.id,
                                            lp.data_lancamento,
                                            CONCAT(eu.nome, ' ', eu.sobrenome) AS responsavel,
                                            eu.id AS usuario_id,
                                            ep.descricao AS descricao_produto,
                                            ep.id AS produto_id,
                                            ele.descricao AS local_estoque_descricao,
                                            emp.nome_fantasia,
                                            ele.id AS local_estoque_id,
                                            el.numero AS numero_lote,
                                            el.id AS lote_id,
                                            ef.nome AS fornecedor,
                                            ef.id AS fornecedor_id,
                                            el.numero_nf,
                                            el.serie_nf,
                                            lp.data_perda,
                                            em.descricao AS motivo_perda_descricao,
                                            em.codigo_sngpc,
                                            em.id AS motivo_perda_id,
                                            (me.saldo_final + lp.quantidade) AS quantidade_antes_perda,
                                            lp.quantidade AS quantidade_perdida,
                                            me.saldo_final AS quantidade_apos_perda,
                                            lp.unidade_medida_id,
                                            lp.observacao       
                                     FROM perdas lp
                                          LEFT JOIN lotes el ON el.id = lp.lote_id
                                          LEFT JOIN locais_estoque ele ON ele.id = lp.local_estoque_id
                                          LEFT JOIN empresas emp ON ele.empresa_id = emp.id
                                          LEFT JOIN motivos_perda em ON em.id = lp.motivo_perda_id
                                          LEFT JOIN usuarios eu ON eu.id = lp.usuario_id
                                          LEFT JOIN produtos ep ON ep.id = lp.produto_id
                                          LEFT JOIN fornecedores ef ON ef.id = el.fornecedor_id
                                          LEFT JOIN movimentos_estoque me ON me.id = lp.movimento_estoque_id
                                     WHERE lp.id = '{request.Id}'
                                           AND lp.group_tenant_id = '{userContext.GroupTenantId}'
                                     """);

        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql.ToString(),
            new { request.Id, userContext.GroupTenantId });

        if (result is null)
            throw new DomainException(
                Localizer.Instance.GetMessage_LancamentoPerda_GuidNaoEncontrado(request.Id));

        return result;
    }
}