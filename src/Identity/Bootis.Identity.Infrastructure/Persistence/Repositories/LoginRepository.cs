using System.Data;
using Bootis.Identity.Domain.DTOs;
using Bootis.Identity.Domain.Entities;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bootis.Identity.Infrastructure.Persistence.Repositories;

public class LoginRepository(
    IDbContext context,
    IDbConnection dbConnection,
    ILogger<LoginRepository> logger)
    : Repository<Login>(context), ILoginRepository
{
    public Task<TemporaryLogin> GetOnceTemporaryLogin(Guid temporaryLoginId, CancellationToken cancellationToken)
    {
        try
        {
            const string sql = """
                               DELETE FROM temporary_logins 
                               WHERE id = @temporaryLoginId
                               RETURNING *
                               """;

            return dbConnection.QueryFirstOrDefaultAsync<TemporaryLogin>(sql, new { temporaryLoginId });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error revoking session by temporary session ID");
            throw;
        }
    }

    #region Helper Methods

    private async Task InvalidatePreviousSessionAsync(Guid userId, Guid newSessionId, string reason,
        CancellationToken cancellationToken)
    {
        await Context.Database.ExecuteSqlInterpolatedAsync($"""
                                                            UPDATE logins 
                                                            SET is_revoked = true, 
                                                                revoked_at = {DateTimeOffset.UtcNow},
                                                                revoked_reason = {reason},
                                                                refresh_token_value = null,
                                                                refresh_token_expires_at = null
                                                            WHERE user_id = {userId} 
                                                              AND session_id != {newSessionId}
                                                              AND is_revoked = false
                                                            """, cancellationToken);
    }

    #endregion

    #region Session Queries

    public async Task<Guid?> GetLastSessionLogged(Guid userId, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT l.session_id
                           FROM logins l
                           WHERE l.user_id = @userId 
                             AND l.is_revoked = false
                           ORDER BY l.date DESC
                           LIMIT 1
                           """;

        return await dbConnection.QueryFirstOrDefaultAsync<Guid?>(sql, new { userId });
    }

    public async Task<Login> GetActiveSessionAsync(Guid userId, CancellationToken cancellationToken)
    {
        return await Context.Set<Login>()
            .AsNoTracking()
            .Where(l => l.UserId == userId && !l.IsRevoked)
            .OrderByDescending(l => l.Date)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<bool> HasActiveSessionAsync(Guid userId, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 1 
                           FROM logins l 
                           WHERE l.user_id = @userId 
                             AND l.is_revoked = false 
                             AND refresh_token_expires_at > @now 
                           LIMIT 1
                           """;

        var hasActive = await dbConnection.QueryFirstOrDefaultAsync<int?>(sql, new
        {
            userId,
            now = DateTimeOffset.UtcNow
        });

        return hasActive.HasValue;
    }

    #endregion

    #region Login Management

    public async Task<Login> AddLoginAsync(
        Login login,
        User user,
        UserPreference userPreference,
        IEnumerable<int> roles,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        await InvalidatePreviousSessionAsync(user.Id, sessionId, "New login", cancellationToken);

        await Context.AddAsync(login, cancellationToken);

        return login;
    }

    public async Task<LoginWithUserAndPreferenceDto> GetLoginWithUserAndPreferenceAsync(
        Guid refreshToken, CancellationToken cancellationToken)
    {
        var query =
            from l in Context.Set<Login>().AsNoTracking()
            join u in Context.Set<User>().AsNoTracking().Include(c => c.Preference) on l.UserId equals u.Id
            join t in Context.Set<Tenant>().AsNoTracking() on u.TenantId equals t.TenantId
            where l.RefreshToken != null &&
                  l.RefreshToken.Value == refreshToken &&
                  !l.IsRevoked &&
                  l.RefreshToken.ExpiresAt > DateTimeOffset.UtcNow
            select new
            {
                Login = l,
                User = u,
                t.CurrencyTypeId
            };

        var result = await query.FirstOrDefaultAsync(cancellationToken);
        return result == null
            ? null
            : new LoginWithUserAndPreferenceDto
            {
                Login = result.Login,
                User = result.User,
                CurrencyTypeId = result.CurrencyTypeId
            };
    }

    public async Task<bool> UpdateLoginRefreshTokenAsync(Guid currentRefreshToken, Guid newRefreshToken,
        DateTimeOffset expiresAt, CancellationToken cancellationToken)
    {
        const string sql = """
                           UPDATE logins 
                           SET refresh_token_value = @newRefreshToken,
                               refresh_token_expires_at = @expiresAt
                           WHERE refresh_token_value = @currentRefreshToken 
                             AND is_revoked = false
                             AND refresh_token_expires_at > @now
                           """;

        var affectedRows = await dbConnection.ExecuteAsync(sql, new
        {
            currentRefreshToken,
            newRefreshToken,
            expiresAt,
            now = DateTimeOffset.UtcNow
        });

        return affectedRows > 0;
    }

    #endregion

    #region Session Revocation

    public async Task RevokeUserSessionAsync(Guid userId, CancellationToken cancellationToken)
    {
        await RevokeUserSessionAsync(userId, "User logout", null, cancellationToken);
    }

    public async Task RevokeUserSessionAsync(Guid userId, string reason, Guid? revokedBySessionId,
        CancellationToken cancellationToken)
    {
        await using var transaction = await Context.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            var activeCount = await Context.Set<Login>()
                .CountAsync(l => l.UserId == userId && !l.IsRevoked, cancellationToken);

            if (activeCount == 0)
            {
                logger.LogDebug("No active sessions to revoke for user {UserId}", userId);
                return;
            }

            if (activeCount <= 3)
            {
                var sessions = await Context.Set<Login>()
                    .Where(l => l.UserId == userId && !l.IsRevoked)
                    .ToListAsync(cancellationToken);

                foreach (var session in sessions)
                    session.RevokeSession(reason, revokedBySessionId);

                await Context.SaveChangesAsync(cancellationToken);
                logger.LogInformation("Revoked {Count} sessions via EF for user {UserId}", sessions.Count, userId);
            }
            else
            {
                var affectedRows = await Context.Database.ExecuteSqlInterpolatedAsync($"""
                     UPDATE logins 
                     SET is_revoked = true, 
                         revoked_at = {DateTimeOffset.UtcNow},
                         revoked_reason = {reason},
                         revoked_by_session_id = {revokedBySessionId}
                     WHERE user_id = {userId} 
                       AND is_revoked = false
                     """, cancellationToken);

                logger.LogWarning("Bulk revoked {Count} sessions for user {UserId} - unusual high count",
                    affectedRows, userId);
            }

            await transaction.CommitAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            logger.LogError(ex, "Error revoking sessions for user {UserId}", userId);
            throw;
        }
    }

    public async Task RevokeSessionByRefreshTokenAsync(Guid refreshToken, string reason,
        CancellationToken cancellationToken)
    {
        try
        {
            var login = await Context.Set<Login>()
                .FirstOrDefaultAsync(l => l.RefreshToken.Value == refreshToken, cancellationToken);

            if (login != null && !login.IsRevoked)
            {
                login.RevokeSession(reason);
                Context.Set<Login>().Update(login);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error revoking session by refresh token");
            throw;
        }
    }

    #endregion
}