using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Application.Requests.Lote.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public string Numero { get; set; }
    public string Produto { get; set; }
    public Guid ProdutoId { get; set; }
    public string Fornecedor { get; set; }
    public Guid FornecedorId { get; set; }
    public DateTime DataLancamento { get; set; }
    public DateTime DataValidade { get; set; }
    public int NumeroNf { get; set; }
    public int SerieNf { get; set; }
    public double Quantidade { get; set; }
    public Domain.Enumerations.SituacaoLote Situacao { get; set; }

    public virtual ListarSituacaoLoteResponse SituacaoLote =>
        new()
        {
            Id = Situacao.ToInt(),
            Descricao = Enum.GetName(Situacao)
        };

    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeAbreviacao { get; set; }
}