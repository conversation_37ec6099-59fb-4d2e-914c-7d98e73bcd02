using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Pessoa.Infrastructure.Repositories;

public class ClienteRepository(IDbContext context) : Repository<Cliente>(context), IClienteRepository
{
    public async Task<Cliente> ObterPorIdAsync(Guid id)
    {
        return await DbSet
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<List<Cliente>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<Cliente>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }

    public async Task<Cliente> ObterPorCnpjAsync(string cnpj)
    {
        return await DbSet
            .FirstOrDefaultAsync(c => c.Cnpj == cnpj);
    }

    public async Task<Cliente> ObterPorCpfAsync(string cpf)
    {
        return await DbSet
            .FirstOrDefaultAsync(c => c.Cpf == cpf);
    }

    public async Task<ClienteDocumento> ObterDocumentoPorDocumentoIdAsync(Guid clienteDocumentoId)
    {
        return await Context.Set<ClienteDocumento>()
            .FirstOrDefaultAsync(c => c.Id == clienteDocumentoId);
    }

    public async Task<List<ClienteDocumento>> ObterDocumentosPorClienteIdAsync(Guid clienteId)
    {
        return await Context.Set<ClienteDocumento>()
            .Where(cd => cd.ClienteId == clienteId)
            .ToListAsync();
    }

    public async Task<ClienteContato> ObterContatoPorContatoIdAsync(Guid clienteContatoId)
    {
        return await Context.Set<ClienteContato>()
            .FirstOrDefaultAsync(c => c.Id == clienteContatoId);
    }

    public async Task<List<ClienteContato>> ObterContatosPorIdEhIdAsync(Guid clienteId,
        Guid clienteContatoId)
    {
        return await Context.Set<ClienteContato>()
            .Where(c => c.ClienteId == clienteId && c.Id != clienteContatoId)
            .ToListAsync();
    }

    public async Task<ClienteEndereco> ObterEnderecoPorEnderecoIdAsync(Guid clienteEnderecoId)
    {
        return await Context.Set<ClienteEndereco>()
            .Include(c => c.Cidade)
            .Include(c => c.Estado)
            .Include(c => c.Pais)
            .FirstOrDefaultAsync(c => c.Id == clienteEnderecoId);
    }

    public async Task<List<ClienteEndereco>> ObterEnderecosPorIdEhIdAsync(Guid clienteId,
        Guid clienteEnderecoId)
    {
        return await Context.Set<ClienteEndereco>()
            .Where(c => c.ClienteId == clienteId && c.Id != clienteEnderecoId)
            .ToListAsync();
    }

    public void Update(ClienteContato clienteContato)
    {
        Context.Set<ClienteContato>()
            .Update(clienteContato);
    }

    public void Update(ClienteEndereco clienteEndereco)
    {
        Context.Set<ClienteEndereco>()
            .Update(clienteEndereco);
    }

    public void Remove(ClienteContato clienteContato)
    {
        Context.Set<ClienteContato>()
            .Remove(clienteContato);
    }

    public void Remove(ClienteEndereco clienteEndereco)
    {
        Context.Set<ClienteEndereco>()
            .Remove(clienteEndereco);
    }

    public void Remove(ClienteDocumento clienteDocumento)
    {
        Context.Set<ClienteDocumento>()
            .Remove(clienteDocumento);
    }

    public async Task<int> VerificarDependenciaClienteAsync(Guid id)
    {
        const string sql = """
                           SELECT COUNT(*) 
                           FROM atendimentos 
                           WHERE cliente_id = @id
                           """;
        
        var count = await Context.Database.GetDbConnection()
            .ExecuteScalarAsync<int>(sql, new { id },
                Context.Database.CurrentTransaction?.GetDbTransaction());
        return count;
    }
}