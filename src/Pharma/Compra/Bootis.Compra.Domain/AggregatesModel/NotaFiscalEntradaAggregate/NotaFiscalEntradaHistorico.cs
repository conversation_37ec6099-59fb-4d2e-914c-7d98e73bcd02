using Bootis.Compra.Domain.Enumerations;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;

public class NotaFiscalEntradaHistorico : Entity, IAggregateRoot, ITenant
{
    protected NotaFiscalEntradaHistorico()
    {
    }

    public NotaFiscalEntradaHistorico(Guid usuarioId,
        StatusNotaFiscal statusAlterado) : this()
    {
        UsuarioId = usuarioId;
        StatusAlterado = statusAlterado;
        Data = DateTime.UtcNow;
    }

    public Guid UsuarioId { get; private set; }
    public Guid NotaFiscalEntradaId { get; private set; }
    public DateTime Data { get; private set; }
    public StatusNotaFiscal StatusAlterado { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    #region Navigation properties

    public virtual Usuario Usuario { get; set; }
    public virtual NotaFiscalEntrada NotaFiscalEntrada { get; set; }

    #endregion
}