using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Application.Requests.Grupo.Atualizar;
using Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Grupo;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork,
    IGrupoRepository grupoRepository) : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        var grupo = await grupoRepository.ObterGrupoUsuarioAsync(request.Id);

        if (grupo.Nome != request.Nome && await grupoRepository.ExistByNome(request.Nome))
            throw new ValidationException(Localizer.Instance.GetMessage_Grupo_NomeJaCadastrado());

        grupo.AlterarNome(request.Nome);
        grupo.AlterarDescricao(request.Descricao);

        AtualizarUsuarios(request.Usuarios, grupo);

        grupoRepository.Update(grupo);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private void AtualizarUsuarios(ICollection<Guid> usuariosId, Domain.AggregatesModel.GrupoAggregate.Grupo grupo)
    {
        foreach (var usuario in grupo.Usuarios.ToArray())
        {
            if (usuariosId.Contains(usuario.Id))
            {
                usuariosId.Remove(usuario.Id);
                continue;
            }

            grupo.RemoverUsuario(usuario);
        }

        foreach (var usuario in grupoRepository.ListarUsuarios(usuariosId)) grupo.AdicionarUsuario(usuario);
    }
}