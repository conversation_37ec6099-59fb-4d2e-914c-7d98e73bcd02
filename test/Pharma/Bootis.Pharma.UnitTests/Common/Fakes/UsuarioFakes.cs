using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Dtos;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Domain.ValuesObject;
using Bootis.Shared.Common.Extensions;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class UsuarioFakes
{
    public static Usuario GerarUsuarioValidoComPermissao(IEnumerable<int> permissoesId)
    {
        var usuario = GerarUsuarioValido();

        usuario.AdicionarPermissoes(permissoesId.Select(x => new PermissaoUsuarioDto
        {
            PermissaoId = x,
            Ativo = true
        }).ToList());

        return usuario;
    }

    public static Usuario GerarUsuarioValido()
    {
        var usuario = new Usuario("Usuario", "Teste", "04272056733", TipoUsuario.Comum.Id,
            DateTime.UtcNow.Date.ToDateOnly(),
            "<EMAIL>",
            "<EMAIL>", GerarEmpresaValida());

        usuario.AtualizarCelular("69912345678");

        usuario.AtualizarPreferencia(new PreferenciasUsuario
        {
            Idioma = "PT-br",
            TemaUsuario = TipoTemaUsuario.Dark,
            ContrasteAumentado = true,
            PadraoData = "ddMMyyyy",
            PadraoHora = "HH:mm",
            TextoAmpliado = false,
            TextoNegrito = false,
            TimeZone = "GTM"
        });

        return usuario;
    }

    private static Empresa GerarEmpresaValida()
    {
        return new Empresa("RazaoTeste", "FantasiaTeste", "033234567000123",
            1, GerarEnderecoValido(), null, null, "1234567",
            "12345678912345", "12345678912", null, null, true, "11999999999");
    }

    private static Endereco GerarEnderecoValido()
    {
        return new Endereco("49000000", "rua a", 429, "torre a", "atalaia",
            "aracaju", "sergipe");
    }
}
