using System.Data;
using Bootis.Catalogo.Application.Requests.TipoCapsula.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.TipoCapsula.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               tc.descricao,
                               tc.id,
                               tc.capsula_vegetal,
                               tc.capsula_gastroresistente
                           FROM tipos_capsula tc
                           """;

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .ExecuteAsync();
    }
}