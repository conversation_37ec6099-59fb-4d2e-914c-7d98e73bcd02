using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Organizacional.Domain.Enumerations;

public class TipoTelefoneFixo() : TipoTelefone(1, "Fixo");

public class TipoTelefoneCelular() : TipoTelefone(2, "Celular");

public class TipoTelefone(int id, string name) : Enumeration(id, name)
{
    public static readonly TipoTelefone Fixo = new TipoTelefoneFixo();
    public static readonly TipoTelefone Celular = new TipoTelefoneCelular();

    public virtual bool Valid(string number)
    {
        return false;
    }
}