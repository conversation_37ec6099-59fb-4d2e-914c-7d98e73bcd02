using System.Linq.Expressions;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Text.Json;

namespace Bootis.Shared.UnitTests.Extensions;

public static class ObjectExtensions
{
    public static ByteArrayContent ToByteArrayContent(this object obj)
    {
        var content = JsonSerializer.Serialize(obj);
        var buffer = Encoding.UTF8.GetBytes(content);
        ByteArrayContent byteContent = new(buffer);
        byteContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

        return byteContent;
    }

    public static T GetFieldValue<T>(this object source, string fieldName)
    {
        var type = source.GetType();
        var fieldInfo = type.GetField(fieldName,
            BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

        if (fieldInfo is null) throw new Exception($"Field {fieldName} not found.");

        var valueType = typeof(T);

        if (fieldInfo.FieldType != valueType)
            throw new Exception($"Field {fieldName} is {fieldInfo.FieldType.Name} and Value is {valueType.Name}.");

        return (T)fieldInfo.GetValue(source);
    }

    public static void SetFieldValue<T>(this object source, string fieldName, T value)
    {
        var type = source.GetType();
        var fieldInfo = type.GetField(fieldName,
            BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

        if (fieldInfo is null) throw new Exception($"Field {fieldName} not found.");

        source.SetFieldValue(fieldInfo, value);
    }

    private static void SetFieldValue<T>(this object source, FieldInfo fieldInfo, T value)
    {
        var valueType = value.GetType();

        if (fieldInfo.FieldType != valueType)
            throw new Exception(
                $"Field {fieldInfo.Name} is {fieldInfo.FieldType.Name} and Value is {valueType.Name}.");

        fieldInfo.SetValue(source, value);
    }

    public static void SetPropertyValue<T>(this object source, string propertyName, T value)
    {
        var type = source.GetType();
        var propertyInfo = type.GetProperty(propertyName,
            BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

        if (propertyInfo is null) throw new Exception($"Field {propertyName} not found.");

        source.SetPropertyValue(propertyInfo, value);
    }

    private static void SetPropertyValue<T>(this object source, PropertyInfo propertyInfo, T value)
    {
        if (!propertyInfo.CanWrite)
        {
            // Try to find a backing field for the property
            var backingFieldName = $"<{propertyInfo.Name}>k__BackingField";
            var backingField = source.GetType().GetField(backingFieldName,
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (backingField != null)
            {
                var type = value.GetType();
                if (backingField.FieldType != type)
                    throw new Exception(
                        $"Property {propertyInfo.Name} is {backingField.FieldType.Name} and Value is {type.Name}.");

                backingField.SetValue(source, value);
                return;
            }

            throw new Exception($"Property {propertyInfo.Name} can't write.");
        }

        var valueType = value.GetType();
        if (propertyInfo.PropertyType != valueType)
            throw new Exception(
                $"Property {propertyInfo.Name} is {propertyInfo.PropertyType.Name} and Value is {valueType.Name}.");

        propertyInfo.SetValue(source, value);
    }

    public static void SetPropertyValue<TSource, TProperty>(
        this TSource source, Expression<Func<TSource, TProperty>> propertyLambda, TProperty value)
    {
        if (propertyLambda.Body is not MemberExpression memberExpression)
            throw new ArgumentException(
                $"Expression '{propertyLambda}' refers to a method, not a property.");

        if (memberExpression.Member is not PropertyInfo propertyInfo)
            throw new ArgumentException(
                $"Expression '{propertyLambda}' refers to a field, not a property.");

        var type = typeof(TSource);
        if (propertyInfo.ReflectedType != null && type != propertyInfo.ReflectedType &&
            !type.IsSubclassOf(propertyInfo.ReflectedType))
            throw new ArgumentException(
                $"Expression '{propertyLambda}' refers to a property that is not from type {type}.");

        source.SetPropertyValue(propertyInfo, value);
    }

    public static void SetFieldValue<TSource, TField>(
        this TSource source, Expression<Func<TSource, TField>> propertyLambda, TField value)
    {
        if (propertyLambda.Body is not MemberExpression memberExpression)
            throw new ArgumentException(
                $"Expression '{propertyLambda}' refers to a method, not a property.");

        if (memberExpression.Member is not FieldInfo fieldInfo)
            throw new ArgumentException(
                $"Expression '{propertyLambda}' refers to a field, not a property.");

        var type = typeof(TSource);
        if (fieldInfo.ReflectedType != null && type != fieldInfo.ReflectedType &&
            !type.IsSubclassOf(fieldInfo.ReflectedType))
            throw new ArgumentException(
                $"Expression '{propertyLambda}' refers to a property that is not from type {type}.");

        source.SetFieldValue(fieldInfo, value);
    }
}