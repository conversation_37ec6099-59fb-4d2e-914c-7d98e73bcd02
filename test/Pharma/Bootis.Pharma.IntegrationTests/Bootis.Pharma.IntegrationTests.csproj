<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.8" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Testcontainers.PostgreSql" Version="4.7.0" />
        <PackageReference Include="Respawn" Version="6.2.1" />
        <PackageReference Include="FluentAssertions" Version="7.1.0" />
        <PackageReference Include="Bogus" Version="35.6.3" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.8" />
        <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.8" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit" />
        <Using Include="FluentAssertions" />
    </ItemGroup>

    <ItemGroup>
        <!-- Main Pharma API Project -->
        <ProjectReference Include="..\..\..\src\Pharma\Bootis.Pharma.Api\Bootis.Pharma.Api.csproj" />
        
        <!-- Shared Projects -->
        <ProjectReference Include="..\..\..\src\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Shared\Bootis.Shared.Infrastructure\Bootis.Shared.Infrastructure.csproj" />
        
        <!-- Pharma Modules - API -->
        <ProjectReference Include="..\..\..\src\Pharma\Catalogo\Bootis.Catalogo.Api\Bootis.Catalogo.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Compra\Bootis.Compra.Api\Bootis.Compra.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Estoque\Bootis.Estoque.Api\Bootis.Estoque.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Localidade\Bootis.Localidade.Api\Bootis.Localidade.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Organizacional\Bootis.Organizacional.Api\Bootis.Organizacional.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Pessoa\Bootis.Pessoa.Api\Bootis.Pessoa.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Producao\Bootis.Producao.Api\Bootis.Producao.Api.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Venda\Bootis.Venda.Api\Bootis.Venda.Api.csproj" />
        
        <!-- Pharma Modules - Domain, Application, Infrastructure (for direct DB access) -->
        <ProjectReference Include="..\..\..\src\Pharma\Pessoa\Bootis.Pessoa.Domain\Bootis.Pessoa.Domain.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Pessoa\Bootis.Pessoa.Application\Bootis.Pessoa.Application.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Pessoa\Bootis.Pessoa.Infrastructure\Bootis.Pessoa.Infrastructure.csproj" />
        
        <ProjectReference Include="..\..\..\src\Pharma\Catalogo\Bootis.Catalogo.Domain\Bootis.Catalogo.Domain.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Catalogo\Bootis.Catalogo.Infrastructure\Bootis.Catalogo.Infrastructure.csproj" />
        
        <ProjectReference Include="..\..\..\src\Pharma\Venda\Bootis.Venda.Domain\Bootis.Venda.Domain.csproj" />
        <ProjectReference Include="..\..\..\src\Pharma\Venda\Bootis.Venda.Infrastructure\Bootis.Venda.Infrastructure.csproj" />
    </ItemGroup>

</Project>