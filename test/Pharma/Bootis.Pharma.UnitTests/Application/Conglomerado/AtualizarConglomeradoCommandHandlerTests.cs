using Bootis.Organizacional.Application.Requests.Conglomerado.Atualizar;
using Bootis.Organizacional.Application.UseCases.Conglomerado;
using Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.UnitTests.Fixtures;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Organizacional.UnitTests.Application.Conglomerado;

[Collection(nameof(AccountManagerDomainCollection))]
public class AtualizarConglomeradoCommandHandlerTests : BaseTest
{
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<IConglomeradoRepository> _mockConglomeradoRepository;
    private readonly Mock<IEmpresaRepository> _mockEmpresaRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarConglomeradoCommandHandlerTests()
    {
        _mockConglomeradoRepository = new Mock<IConglomeradoRepository>();
        _mockEmpresaRepository = new Mock<IEmpresaRepository>();
        _handler = new AtualizarRequestHandler(unitOfWork.Object, _mockConglomeradoRepository.Object,
            _mockEmpresaRepository.Object);
    }

    [Fact]
    public async Task AtualizarConglomerado_ExecutadoComSucesso()
    {
        //Arrange
        var model = ConglomeradoFakes.GerarConglomeradoValido();
        var empresaModel = EmpresaFakes.GerarEmpresaValido();
        model.AdicionarEmpresa(empresaModel);

        var cmd = new AtualizarRequest
        {
            Ativo = false,
            ConglomeradoId = model.Id,
            EmpresaMatrizId = empresaModel.Id,
            Nome = "Conglomerado"
        };

        _mockConglomeradoRepository.Setup(l => l.ObterPorIdAsync(cmd.ConglomeradoId)).Returns(
            Task.FromResult(model));

        _mockEmpresaRepository.Setup(l => l.ValidarPorIdAsync(cmd.EmpresaMatrizId))
            .Returns(Task.FromResult(true));

        _mockConglomeradoRepository
            .Setup(l => l.Update(It.IsAny<Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _mockConglomeradoRepository.Verify(
            l => l.Update(It.Is<Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado>(m => m.Nome == cmd.Nome)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarConglomeradoInexistente_ExecutadoComErro()
    {
        //Arrange
        var cmd = new AtualizarRequest();

        _mockConglomeradoRepository.Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>())).Returns(
            Task.FromResult<Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado>(default));

        //Action
        var actionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(actionResult);
    }
}
