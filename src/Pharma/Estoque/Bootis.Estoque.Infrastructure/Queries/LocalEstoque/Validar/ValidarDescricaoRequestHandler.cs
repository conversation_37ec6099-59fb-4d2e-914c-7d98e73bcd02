using System.Data;
using System.Text;
using Bootis.Estoque.Application.Requests.LocalEstoque.Validar;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.LocalEstoque.Validar;

public class ValidarDescricaoRequestHandler(
    IDbConnection connection) : IRequestHandler<ValidarDescricaoRequest>
{
    public async Task Handle(ValidarDescricaoRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder("""
                                    SELECT e.id
                                      FROM locais_estoque le
                                           JOIN empresas e ON
                                                     e.id = le.empresa_id
                                     WHERE UPPER(le.descricao) ILIKE UPPER(@Descricao)
                                       AND e.id = @EmpresaId
                                    """);

        if (request.LocalEstoqueId != null)
            sql.Append($" AND le.id != '{request.LocalEstoqueId}' ");

        var result = await connection.QueryFirstOrDefaultAsync<Guid>(sql.ToString(),
            new
            {
                request.Descricao,
                request.EmpresaId,
                Id = request.LocalEstoqueId
            });

        if (result != Guid.Empty)
            throw new DomainException(
                Localizer.Instance.GetMessage_LocalEstoque_DescricaoExistente(request.Descricao, result));
    }
}