using System.Runtime.Serialization;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Application.Requests.Inventario.Obter;

public class ObterDetalhesResponse
{
    public int Codigo { get; set; }
    public StatusInventario StatusInventarioId { get; set; }
    public IEnumerable<DetalhesEspecificacaoResponse> Especificacao { get; set; }
}

public class DetalhesEspecificacaoResponse
{
    public Guid LocalEstoqueId { get; set; }
    public string LocalEstoqueDescricao { get; set; }
    public IEnumerable<DetalhesItensResponse> Itens { get; set; }
}

public class DetalhesItensResponse
{
    public Guid ProdutoId { get; set; }
    public int ProdutoCodigo { get; set; }
    public string ProdutoDescricao { get; set; }
    public UnidadeMedidaAbreviacao ClasseProdutoId { get; set; }
    public int UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public Guid LoteId { get; set; }
    public string LoteNumero { get; set; }
    public string NotaFiscalNumero { get; set; }
    public decimal Quantidade { get; set; }

    [IgnoreDataMember] public Guid LocalEstoqueId { get; set; }
}