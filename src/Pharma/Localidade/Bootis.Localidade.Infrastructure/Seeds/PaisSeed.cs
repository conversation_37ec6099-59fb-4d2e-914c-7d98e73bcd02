using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace Bootis.Localidade.Infrastructure.Seeds;

public class PaisSeed : ISeed
{
    public int Order => 0;

    public void Seed(DbContext dbContext)
    {
        var fullFileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Seeds", "Data", "Paises.csv");

        var npgsqlConnection = dbContext.Database.GetDbConnection() as NpgsqlConnection;

        using var writer =
            npgsqlConnection!.BeginTextImport(
                "COPY paises (descricao, abreviacao, iso_id, id, abreviacao_iso2, en_us, pt_br) FROM STDIN (FORMAT csv, HEADER false, DELIMITER '|')");
        using var reader = new StreamReader(fullFileName);
        while (reader.ReadLine() is { } line)
            writer.WriteLine(line);
    }
}