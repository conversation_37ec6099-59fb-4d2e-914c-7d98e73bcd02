using Bootis.Pessoa.Application.Requests.EspecialidadePrescritor.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.EspecialidadePrescritor;

public class CadastrarEspecialidadePrescritorRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarEspecialidadePrescritorRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(body => body)
            .NotEmpty();

        RuleFor(c => c.Descricao)
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Descricao)))
            .MaximumLength(100)
            .WithMessage(f => localizer.GetMessage_Validation_TamanhoMaximo(nameof(f.Descricao), 100));
    }
}