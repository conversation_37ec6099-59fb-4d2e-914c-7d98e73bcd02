using Bootis.Catalogo.Application.UseCases.FormaFarmaceutica;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Estoque.UnitTests.Fixtures.FormaFarmaceutica;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Producao.UnitTests.Application.FormaFarmaceutica;

public class CadastrarFormaFarmaceuticaCommandHandlerTests : BaseTest
{
    private readonly CadastrarRequestHandler _cadastrarFormaFarmaceuticaCommandHandler;

    private readonly Mock<IEmbalagemClassificacaoFormaFarmaceuticaRepository>
        _embalagemClassificacaoFormaFarmaceuticaRepositoryMock;

    private readonly Mock<IEmbalagemClassificacaoRepository> _embalagemClassificacaoRepositoryMock;
    private readonly Mock<IFormaFarmaceuticaRepository> _formaFarmaceuticaRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarFormaFarmaceuticaCommandHandlerTests()
    {
        _formaFarmaceuticaRepositoryMock = new Mock<IFormaFarmaceuticaRepository>();
        _embalagemClassificacaoFormaFarmaceuticaRepositoryMock =
            new Mock<IEmbalagemClassificacaoFormaFarmaceuticaRepository>();
        _embalagemClassificacaoRepositoryMock = new Mock<IEmbalagemClassificacaoRepository>();

        unitOfWork.Setup(l => l.GetRepository<IFormaFarmaceuticaRepository>())
            .Returns(_formaFarmaceuticaRepositoryMock.Object);
        unitOfWork.Setup(l => l.GetRepository<IEmbalagemClassificacaoFormaFarmaceuticaRepository>())
            .Returns(_embalagemClassificacaoFormaFarmaceuticaRepositoryMock.Object);
        unitOfWork.Setup(l => l.GetRepository<IEmbalagemClassificacaoRepository>())
            .Returns(_embalagemClassificacaoRepositoryMock.Object);

        _cadastrarFormaFarmaceuticaCommandHandler = new CadastrarRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task CadastrarFormaFarmaceuticaCommand_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();
        var embalagemClassificacao = EmbalagemClassificacaoFake.CreateEmbalagemClassificacao_Ativo();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ObterPorIdAsync(cmd.EmbalagemClassificacaoId.First())).Returns(
                Task.FromResult(embalagemClassificacao));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.Add(formaFarmaceutica))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        _embalagemClassificacaoFormaFarmaceuticaRepositoryMock.Setup(l =>
            l.Add(It
                .IsAny<Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate.
                    EmbalagemClassificacaoFormaFarmaceutica>()));

        unitOfWork.Setup(l => l.SaveChangesAsync(default));

        //Action
        await _cadastrarFormaFarmaceuticaCommandHandler.Handle(cmd, default);

        //Assert
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                m.Descricao == formaFarmaceutica.Descricao)),
            Times.Once);
        _embalagemClassificacaoFormaFarmaceuticaRepositoryMock.Verify(l =>
                l.Add(
                    It.Is<Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate.
                        EmbalagemClassificacaoFormaFarmaceutica>(m =>
                        m.FormaFarmaceutica.Descricao == formaFarmaceutica.Descricao)),
            Times.Once);
    }

    [Fact]
    public async Task CadastrarFormaFarmaceuticaCommand_DescricaoExistente_ExecutadoComErro()
    {
        //Arrange
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao)).Returns(
            Task.FromResult(true));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarFormaFarmaceuticaCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                m.Descricao == formaFarmaceutica.Descricao)),
            Times.Never);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task CadastrarFormaFarmaceuticaCommand_LaboratorioGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao)).Returns(
            Task.FromResult(false));


        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarFormaFarmaceuticaCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                m.Descricao == formaFarmaceutica.Descricao)),
            Times.Never);
    }

    [Fact]
    public async Task CadastrarFormaFarmaceuticaCommand_EmbalagemClassificacaoGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao)).Returns(
            Task.FromResult(false));


        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ObterPorIdAsync(cmd.EmbalagemClassificacaoId.First())).Returns(
                Task.FromResult<Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao>(
                    default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarFormaFarmaceuticaCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                m.Descricao == formaFarmaceutica.Descricao)),
            Times.Once);
        _embalagemClassificacaoFormaFarmaceuticaRepositoryMock.Verify(l =>
                l.Add(
                    It.Is<
                        Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate.
                        EmbalagemClassificacaoFormaFarmaceutica>(m =>
                        m.FormaFarmaceutica.Descricao == formaFarmaceutica.Descricao)),
            Times.Never);
    }

    [Fact]
    public async Task
        CadastrarFormaFarmaceuticaCommand_EmbalagemClassificacaoFormaFarmaceuticaVinculoExistente_ExecutadoComErro()
    {
        //Arrange
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();
        var embalagemClassificacao = EmbalagemClassificacaoFake.CreateEmbalagemClassificacao_Ativo();
        var embalagemClassificacaoFormaFarmaceutica =
            EmbalagemClassificacaoFormaFarmaceuticaFake.CreateEmbalagemClassificacaoFormaFarmaceutica();

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao)).Returns(
            Task.FromResult(false));

        _embalagemClassificacaoRepositoryMock
            .Setup(l => l.ObterPorIdAsync(cmd.EmbalagemClassificacaoId.First())).Returns(
                Task.FromResult(embalagemClassificacao));

        _embalagemClassificacaoFormaFarmaceuticaRepositoryMock
            .Setup(l => l.ObterPorIdAsync(It.IsAny<Guid>(), It.IsAny<Guid>())).Returns(
                Task.FromResult(embalagemClassificacaoFormaFarmaceutica));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarFormaFarmaceuticaCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                m.Descricao == formaFarmaceutica.Descricao)),
            Times.Once);
        _embalagemClassificacaoFormaFarmaceuticaRepositoryMock.Verify(l =>
                l.Add(
                    It.Is<Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate.
                        EmbalagemClassificacaoFormaFarmaceutica>(m =>
                        m.FormaFarmaceutica.Descricao == formaFarmaceutica.Descricao)),
            Times.Never);
    }
}
