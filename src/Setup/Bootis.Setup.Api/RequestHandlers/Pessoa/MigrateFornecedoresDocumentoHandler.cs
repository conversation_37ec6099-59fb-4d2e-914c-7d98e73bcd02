using System.Text;
using Bootis.Setup.Api.Models.Pessoa;

namespace Bootis.Setup.Api.RequestHandlers.Pessoa;

public record MigrateFornecedoresDocumento(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateFornecedoresDocumentoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateFornecedoresDocumento, FornecedorDocumento>(configuration)
{
    private static readonly List<FornecedorDocumento> FornecedorDocumentos = [];

    public override async Task Handle(MigrateFornecedoresDocumento request, CancellationToken cancellationToken)
    {
        var filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "Docs", "SeedsCsv",
            "FornecedorDocumentos.csv");

        var lines = File.ReadAllLines(filePath, Encoding.GetEncoding("ISO-8859-1"));

        for (var i = 0; i < lines.Length; i++)
        {
            var values = lines[i].Split(',');

            var fornecedorDocumento = new FornecedorDocumento
            {
                FornecedorIdentificacao = values[0],
                TipoDocumentoNome = values[1],
                Identificacao = values[2],
                Observacao = values[3]
            };

            FornecedorDocumentos.Add(fornecedorDocumento);
        }

        var dictFornecedor = await GetDictionaryFromDatabase(
            "SELECT cnpj, id FROM fornecedores WHERE tenant_id = @TenantId AND group_tenant_id = @GroupTenantId",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            new { request.TenantId, request.GroupTenantId },
            cancellationToken
        );

        var dictTipoDocumento = await GetDictionaryFromDatabase(
            "SELECT nome, id FROM tipos_documento",
            reader => reader.GetString(0),
            reader => reader.GetGuid(1),
            null,
            cancellationToken
        );

        var dataTable = GetData(request, cancellationToken, dictFornecedor, dictTipoDocumento);

        await InsertOrUpdateData(dataTable, cancellationToken);
    }

    private IAsyncEnumerable<FornecedorDocumento> GetData(MigrateFornecedoresDocumento request,
        CancellationToken cancellationToken,
        Dictionary<string, Guid> dictFornecedor, Dictionary<string, Guid> dictTipoDocumento)
    {
        return request.DefaultValues
            ? GetDataFromDefault(request, dictFornecedor, dictTipoDocumento)
            : GetDataFromSourceDatabase(request.SourceConnectionString, "SELECT * FROM fornecedores_documento",
                cancellationToken,
                fornecedorDocumento =>
                {
                    fornecedorDocumento.FornecedorId = dictFornecedor[fornecedorDocumento.FornecedorIdentificacao];
                    fornecedorDocumento.TipoDocumentoId = dictTipoDocumento[fornecedorDocumento.TipoDocumentoNome];
                    return Task.CompletedTask;
                });
    }

    private static async IAsyncEnumerable<FornecedorDocumento> GetDataFromDefault(MigrateFornecedoresDocumento request,
        Dictionary<string, Guid> dictFornecedor, Dictionary<string, Guid> dictTipoDocumento)
    {
        foreach (var fornecedorDocumento in FornecedorDocumentos)
            yield return fornecedorDocumento with
            {
                TenantId = request.TenantId,
                GroupTenantId = request.GroupTenantId,
                FornecedorId = dictFornecedor[fornecedorDocumento.FornecedorIdentificacao],
                TipoDocumentoId = dictTipoDocumento[fornecedorDocumento.TipoDocumentoNome]
            };

        await Task.CompletedTask;
    }
}