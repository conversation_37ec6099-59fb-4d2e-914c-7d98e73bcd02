using Bootis.Organizacional.Application.Requests.Conglomerado.Cadastrar;
using Bootis.Organizacional.Application.UseCases.Conglomerado;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Dtos;
using Bootis.Organizacional.UnitTests.Fixtures;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using DomainAggregate = Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate;

namespace Bootis.Organizacional.UnitTests.Application.Conglomerado;

[Collection(nameof(AccountManagerDomainCollection))]
public class CadastrarConglomeradoCommandHandlerTests : BaseTest
{
    private readonly Mock<IEmpresaRepository> _empresaRepositoryMock;
    private readonly CadastrarRequestHandler _handler;
    private readonly Mock<DomainAggregate.IConglomeradoRepository> _mockConglomeradoRepository;
    private readonly Mock<IUsuarioRepository> _mockUsuarioRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarConglomeradoCommandHandlerTests()
    {
        _mockConglomeradoRepository = new Mock<DomainAggregate.IConglomeradoRepository>();
        _mockUsuarioRepository = new Mock<IUsuarioRepository>();
        _empresaRepositoryMock = new Mock<IEmpresaRepository>();
        _handler = new CadastrarRequestHandler(unitOfWork.Object, UserContext.Object,
            _mockConglomeradoRepository.Object,
            _empresaRepositoryMock.Object,
            _mockUsuarioRepository.Object);
    }

    [Fact]
    public async Task CadastrarConglomerado_ExecutadoComSucesso()
    {
        //Arrange
        var model = ConglomeradoFakes.GerarConglomeradoValido();
        var cmd = new CadastrarRequest
        {
            Nome = model.Nome,
            Usuario = ConglomeradoFakes.MapperUsuarioDto(model.Matriz.Empresa.Responsavel.Usuario),
            Empresa = ConglomeradoFakes.MapperEmpresaDto(model.Matriz.Empresa),
            Endereco = ConglomeradoFakes.MapperEnderecoDto(model.Matriz.Empresa.Endereco)
        };

        _mockConglomeradoRepository.Setup(l => l.Add(model)).Verifiable();
        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(Task.FromResult(1));
        _mockUsuarioRepository.Setup(l => l.ValidarUsuarioEmail(cmd.Usuario.Email)).Returns(Task.FromResult(false));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _mockConglomeradoRepository.Verify(l => l.Add(It.Is<DomainAggregate.Conglomerado>(m => m.Nome == cmd.Nome)),
            Times.Once);
    }

    [Fact]
    public async Task CadastrarConglomeradoEmailResponsavelJaUtilizado_ExecutadoComErro()
    {
        //Arrange
        var model = ConglomeradoFakes.GerarConglomeradoValido();
        var cmd = new CadastrarRequest
        {
            Usuario = new UsuarioDto
            {
                Email = "<EMAIL>"
            }
        };

        _mockUsuarioRepository.Setup(l => l.ValidarUsuarioEmail(cmd.Usuario.Email)).Returns(Task.FromResult(true));


        //Action
        var actionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(actionResult);
    }
}
