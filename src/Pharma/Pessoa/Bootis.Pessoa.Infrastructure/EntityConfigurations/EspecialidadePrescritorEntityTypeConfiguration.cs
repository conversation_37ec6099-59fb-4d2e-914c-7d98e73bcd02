using Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Pessoa.Infrastructure.EntityConfigurations;

public class EspecialidadePrescritorEntityTypeConfiguration : BaseEntityTypeConfiguration<EspecialidadePrescritor>
{
    public override void Configure(EntityTypeBuilder<EspecialidadePrescritor> builder)
    {
        builder.ToTable("especialidades_prescritor");

        builder
            .Property(c => c.Id)
            .IsRequired();

        builder
            .Property(c => c.Descricao)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired();

        builder
            .Property(c => c.Ativo)
            .HasDefaultValue(true)
            .IsRequired();

        base.Configure(builder);
    }
}