using Bootis.Identity.Application.Companies;
using Bootis.Organizacional.Contracts.Empresa;
using MassTransit;
using MediatR;

namespace Bootis.Identity.Infrastructure.Consumers;

public class ChangeCompanyConsumer(IMediator mediator) : IConsumer<EmpresaAtualizadoEvent>
{
    public async Task Consume(ConsumeContext<EmpresaAtualizadoEvent> context)
    {
        await mediator.Send(new ChangeCompanyRequest
        {
            TenantId = context.Message.TenantId,
            CurrencyTypeId = context.Message.TipoMoedaId
        }, context.CancellationToken);
    }
}