using Bootis.Organizacional.Domain.Dtos;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Shared.Application.Extensions;
using Bootis.Shared.Domain.SeedWork;
using FluentValidation;

namespace Bootis.Organizacional.Application.Validations;

public class TelefoneDtoValidator : AbstractValidator<TelefoneDto>
{
    public TelefoneDtoValidator()
    {
        RuleFor(c => c.Numero)
            .NotEmpty();

        RuleFor(c => c.TipoId)
            .ExistInEnumeration(Enumeration.GetAll<TipoTelefone>());
    }
}