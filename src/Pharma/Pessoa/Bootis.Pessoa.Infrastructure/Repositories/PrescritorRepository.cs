using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Pessoa.Infrastructure.Repositories;

public class PrescritorRepository(IDbContext context) : Repository<Prescritor>(context), IPrescritorRepository
{
    public async Task<Prescritor> ObterPorIdAsync(Guid id)
    {
        return await DbSet
            .Include(c => c.UfRegistro)
            .Include(c => c.Especialidades)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<List<Prescritor>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<Prescritor>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }

    public async Task<Prescritor> ObterSemInclusoesAsync(Guid id)
    {
        return await DbSet
            .SingleOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Guid> ObterIdAsync(Guid id)
    {
        return await DbSet
            .Where(c => c.Id == id)
            .Select(c => c.Id)
            .FirstAsync();
    }

    public async Task<PrescritorEndereco> ObterEnderecoPorEnderecoIdAsync(Guid prescritorEnderecoExteralId)
    {
        return await Context.Set<PrescritorEndereco>()
            .FirstOrDefaultAsync(c => c.Id == prescritorEnderecoExteralId);
    }

    public async Task<List<PrescritorEndereco>> ObterEnderecosPorIdEhEnderecoIdAsync(Guid prescritorId,
        Guid prescritorEnderecoId)
    {
        return await Context.Set<PrescritorEndereco>()
            .Where(c => c.PrescritorId == prescritorId && c.Id != prescritorEnderecoId)
            .ToListAsync();
    }

    public async Task<PrescritorDocumento> ObterDocumentoPorDocumentoIdAsync(Guid prescritorDocumentoId)
    {
        return await Context.Set<PrescritorDocumento>()
            .FirstOrDefaultAsync(c => c.Id == prescritorDocumentoId);
    }

    public async Task<List<PrescritorDocumento>> ObterDocumentosPorPrescritorIdAsync(Guid prescritorId)
    {
        return await Context.Set<PrescritorDocumento>()
            .Where(cd => cd.PrescritorId == prescritorId)
            .ToListAsync();
    }

    public async Task<PrescritorContato> ObterContatoPorContatoIdAsync(Guid prescritorContatoId)
    {
        return await Context.Set<PrescritorContato>()
            .FirstOrDefaultAsync(c => c.Id == prescritorContatoId);
    }

    public async Task<List<PrescritorContato>> ObterContatosPorIdEhContatoIdAsync(Guid prescritorId,
        Guid prescritorContatoId)
    {
        return await Context.Set<PrescritorContato>()
            .Where(c => c.PrescritorId == prescritorId && c.Id != prescritorContatoId)
            .ToListAsync();
    }

    public async Task<TipoRegistro> ObterTipoRegistroPorIdAsync(Guid requestTipoRegistroId)
    {
        return await Context.Set<TipoRegistro>()
            .FirstOrDefaultAsync(c => c.Id == requestTipoRegistroId);
    }

    public async Task<Prescritor> ObterPorTipoRegistroEhCodigoRegistroEhUf(TipoRegistro tipoRegistro,
        string codigoRegistro, Estado ufRegistro)
    {
        return await Context.Set<Prescritor>()
            .FirstOrDefaultAsync(p =>
                p.TipoRegistroId == tipoRegistro.Id && p.CodigoRegistro == codigoRegistro &&
                p.UfRegistroId == ufRegistro.Id);
    }

    public void Remove(PrescritorContato prescritorContato)
    {
        Context.Set<PrescritorContato>()
            .Remove(prescritorContato);
    }

    public void Remove(PrescritorDocumento prescritorDocumento)
    {
        Context.Set<PrescritorDocumento>()
            .Remove(prescritorDocumento);
    }

    public void Remove(PrescritorEndereco prescritorEndereco)
    {
        Context.Set<PrescritorEndereco>()
            .Remove(prescritorEndereco);
    }

    public void Update(PrescritorContato prescritorContato)
    {
        Context.Set<PrescritorContato>()
            .Update(prescritorContato);
    }

    public void Update(PrescritorEndereco prescritorEndereco)
    {
        Context.Set<PrescritorEndereco>()
            .Update(prescritorEndereco);
    }

    public async Task<int> VerificarDependenciaPrescritorAsync(Guid id)
    {
        const string sqlId = """
                             SELECT id
                               FROM prescritores
                              WHERE id = @id
                             """;

        var dbConnection = Context.Database.GetDbConnection();

        var prescritorId = await dbConnection.QueryFirstOrDefaultAsync<int>(sqlId, new { id },
            Context.Database.CurrentTransaction?.GetDbTransaction());

        const string sqlCount = """
                                SELECT 
                                    (SELECT COUNT(*) FROM receitas_manipuladas WHERE prescritor_id = @prescritorId) +
                                    (SELECT COUNT(*) FROM pedidos_venda_itens_produto_acabado WHERE prescritor_id = @prescritorId)
                                """;

        var count = await dbConnection.QueryFirstOrDefaultAsync<int>(sqlCount, new { prescritorId },
            Context.Database.CurrentTransaction?.GetDbTransaction());

        return count;
    }
}