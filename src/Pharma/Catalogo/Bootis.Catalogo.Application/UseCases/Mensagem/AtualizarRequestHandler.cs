using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.Mensagem.Atualizar;
using Bootis.Catalogo.Domain.AggregatesModel.MensagemAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.Mensagem;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork) : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        var mensagemRepository =
            unitOfWork.GetRepository<IMensagemRepository>();

        await mensagemRepository.ObterMensagemValidarDescricaoAsync(request.Id, request.Descricao);
        var mensagem = await mensagemRepository.ObterPorIdAsync(request.Id);

        mensagem.Atualizar(request.Descricao, request.ExibeVenda, request.ExibeRotulagem,
            request.ExibeFichaPesagem, request.ExibeImpressaoRotulo);

        mensagemRepository.Update(mensagem);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}