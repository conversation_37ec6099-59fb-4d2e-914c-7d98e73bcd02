using Bootis.Estoque.Application.Requests.Inventario.Atualizar;
using Bootis.Estoque.Domain.Dtos.Inventario;
using Bootis.Shared.Common.Extensions;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Inventario;

public class FinalizarConferenciaInventarioCommandFake
{
    public static FinalizarConferenciaRequest CreateFinalizarConferenciaInventarioCommandValido()
    {
        var conferencia = new List<InventarioConferenciaDto>
        {
            new()
            {
                Aprovado = true,
                InventarioItemId = 0.ToGuid()
            }
        };

        return new FinalizarConferenciaRequest
        {
            InventarioId = Uuid.NewSequential(),
            Conferencia = conferencia
        };
    }
}
