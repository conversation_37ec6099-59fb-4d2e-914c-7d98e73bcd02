using Bootis.Organizacional.Application.Requests.Empresa.Atualizar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Organizacional.Application.Validations.Empresa;

public class AtualizarUsuarioResponsavelRequestValidator : AbstractValidator<AtualizarUsuarioResponsavelRequest>
{
    public AtualizarUsuarioResponsavelRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.EmpresasId)
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.EmpresasId)));

        RuleFor(c => c.UsuarioResponsavelId)
            .NotEmpty();
    }
}