using Bootis.Compra.Application.Requests.PedidoCompra.Atualizar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Compra.Application.Validations.PedidoCompra;

public class EstornarLiberadoRequestValidator : AbstractValidator<EstornarLiberadoRequest>
{
    public EstornarLiberadoRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.PedidoCompraId)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.PedidoCompraId)));
    }
}