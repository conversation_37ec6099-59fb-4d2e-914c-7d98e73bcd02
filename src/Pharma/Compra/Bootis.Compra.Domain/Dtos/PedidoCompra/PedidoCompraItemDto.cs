using Bootis.Compra.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Compra.Domain.Dtos.PedidoCompra;

public class PedidoCompraItemDto
{
    public Guid? PedidoCompraItemId { get; set; }
    public Guid ProdutoId { get; set; }
    public decimal Quantidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public decimal PrecoUnitario { get; set; }
    public decimal ValorDescontoUnitario { get; set; }
    public TipoDesconto TipoDesconto { get; set; }
}