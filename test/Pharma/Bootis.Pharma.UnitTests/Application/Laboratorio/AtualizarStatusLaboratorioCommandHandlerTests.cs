using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Producao.Application.UseCases.Laboratorio;
using Bootis.Producao.Domain.AggregatesModel.LaboratorioAggregate;
using Bootis.Producao.UnitTests.Fixtures.Laboratorio;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Producao.UnitTests.Application.Laboratorio;

public class AtualizarStatusLaboratorioCommandHandlerTests : BaseTest
{
    private readonly AtualizarStatusRequestHandler _handler;
    private readonly Mock<ILaboratorioRepository> _laboratorioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarStatusLaboratorioCommandHandlerTests()
    {
        _laboratorioRepositoryMock = new Mock<ILaboratorioRepository>();
        _handler = new AtualizarStatusRequestHandler(unitOfWork.Object, _laboratorioRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarStatusLaboratorio_ExecutadoComSucesso_Ativo()
    {
        //Arrange
        var cmd = AtualizarStatusLaboratorioCommandFake.CriarCommandValido_Ativo();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id.First())).Returns(
            Task.FromResult(laboratorio));

        _laboratorioRepositoryMock.Setup(l => l.Update(laboratorio))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.Ativo == laboratorio.Ativo)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarStatusLaboratorio_ExecutadoComSucesso_Inativo()
    {
        //Arrange
        var cmd = AtualizarStatusLaboratorioCommandFake.CriarCommandValido_Inativo();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id.First())).Returns(
            Task.FromResult(laboratorio));

        _laboratorioRepositoryMock.Setup(l => l.Update(laboratorio))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.Ativo == laboratorio.Ativo)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarStatusLaboratorio_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarStatusLaboratorioCommandFake.CriarCommandValido_Inativo();
        var laboratorio = LaboratorioFake.CreateLaboratorioValido();

        _laboratorioRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id.First())).Returns(
            Task.FromResult<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _laboratorioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.LaboratorioAggregate.Laboratorio>(m =>
                    m.Ativo == laboratorio.Ativo)),
            Times.Never);
    }
}
