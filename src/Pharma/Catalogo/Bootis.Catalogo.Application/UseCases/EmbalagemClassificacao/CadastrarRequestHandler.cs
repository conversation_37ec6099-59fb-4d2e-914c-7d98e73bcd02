using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.EmbalagemClassificacao.Cadastrar;
using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.EmbalagemClassificacao;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork)
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var embalagemClassificacaoRepository =
            unitOfWork.GetRepository<IEmbalagemClassificacaoRepository>();

        await embalagemClassificacaoRepository.ValidarEmbalagemClassificacaoPorDescricaoAsync(request.Descricao);

        var embalagemClassificacao =
            new Domain.AggregatesModel.EmbalagemClassificacaoAggregate.EmbalagemClassificacao(request.Descricao, true);

        embalagemClassificacaoRepository.Add(embalagemClassificacao);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}