using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Fornecedor.Atualizar;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Fornecedor;

public class AtualizarStatusRequestHandler(
    IUnitOfWork unitOfWork,
    IFornecedorRepository fornecedorRepository)
    : IRequestHandler<AtualizarStatusRequest>
{
    public async Task Handle(AtualizarStatusRequest request, CancellationToken cancellationToken)
    {
        foreach (var item in request.FornecedoresId)
        {
            var fornecedor = await fornecedorRepository.ObterFornecedorAsync(item);

            fornecedor.AtualizarAtivo(request.Ativo);

            fornecedorRepository.Update(fornecedor);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}