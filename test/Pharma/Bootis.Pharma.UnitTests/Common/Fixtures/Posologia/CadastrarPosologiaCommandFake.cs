using Bootis.Producao.Application.Requests.Posologia.Cadastrar;
using Bootis.Producao.Domain.Enumerations;
using UUIDNext;

namespace Bootis.Producao.UnitTests.Fixtures.Posologia;

public class CadastrarPosologiaCommandFake
{
    public static CadastrarRequest CriarCommandValido()
    {
        return new CadastrarRequest
        {
            Descricao = "Capsula",
            FormaFarmaceuticaId = Uuid.NewSequential(),
            Periodo = PeriodosPosologia.Dia,
            QuantidadeDosePorPeriodo = 1
        };
    }
}
