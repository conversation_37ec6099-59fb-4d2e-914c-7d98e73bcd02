using Bootis.Catalogo.Application.Requests.CalculoConversaoUnidadeMedida.Calcular;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.UnitTests.Fixtures.ConversaoUnidadeMedida;

public class CalcularUnidadeMedidaRequestFakes
{
    public static CalcularConversaoUnidadeMedidaRequest CriarCommandValido()
    {
        var conversoes = new List<ConversaoUnidadeMedidaACalcular>();
        var conversao = new ConversaoUnidadeMedidaACalcular
        {
            Quantidade = 10,
            Densidade = 1,
            UnidadeMedidaConversaoId = UnidadeMedidaAbreviacao.mg,
            UnidadeMedidaOrigemId = UnidadeMedidaAbreviacao.g
        };

        conversoes.Add(conversao);

        return new CalcularConversaoUnidadeMedidaRequest(conversoes);
    }

    public static CalcularConversaoUnidadeMedidaRequest CriarCommandInvalido()
    {
        var conversoes = new List<ConversaoUnidadeMedidaACalcular>();
        var conversao = new ConversaoUnidadeMedidaACalcular
        {
            Quantidade = 10,
            Densidade = 1,
            UnidadeMedidaConversaoId = UnidadeMedidaAbreviacao.UI,
            UnidadeMedidaOrigemId = UnidadeMedidaAbreviacao.UI
        };

        conversoes.Add(conversao);

        return new CalcularConversaoUnidadeMedidaRequest(conversoes);
    }

    public static CalcularConversaoUnidadeMedidaRequest CriarCommandSemUnidadeMedida()
    {
        var conversoes = new List<ConversaoUnidadeMedidaACalcular>();
        var conversao = new ConversaoUnidadeMedidaACalcular
        {
            Quantidade = 10,
            Densidade = 1
        };

        conversoes.Add(conversao);

        return new CalcularConversaoUnidadeMedidaRequest(conversoes);
    }
}
