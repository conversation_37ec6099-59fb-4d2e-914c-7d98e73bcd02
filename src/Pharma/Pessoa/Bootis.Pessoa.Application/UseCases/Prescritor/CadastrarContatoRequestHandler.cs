using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Prescritor.Cadastrar;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Prescritor;

public class CadastrarContatoRequestHandler(
    IUnitOfWork unitOfWork,
    IPrescritorRepository prescritorRepository)
    : IRequestHandler<CadastrarContatoRequest>
{
    public async Task Handle(CadastrarContatoRequest request, CancellationToken cancellationToken)
    {
        var prescritor = await prescritorRepository.ObterPrescritorAsync(request.PrescritorId);

        var prescritorContato = new PrescritorContato(prescritor.Id,
            request.TipoContatoId,
            request.Identificacao,
            request.Principal,
            request.Observacao);

        prescritor.Adicionar<PERSON>ontato(prescritorContato);

        prescritorRepository.Update(prescritor);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}