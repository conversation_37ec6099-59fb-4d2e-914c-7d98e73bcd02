using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate.Specializations;

public class UnidadeMedidaG : UnidadeMedida
{
    public override UnidadeMedidaAbreviacao Abreviacao => UnidadeMedidaAbreviacao.g;
    public override string Descricao => "Grama";
    public override bool Ativo => true;
    public override bool UnidadeAlternativa => false;
    public override bool UnidadeEstoque => true;
    public override bool UnidadePrescricao => true;
    public override bool UnidadePosologia => false;
    public override TipoUnidade TipoUnidade => TipoUnidade.Peso;
}