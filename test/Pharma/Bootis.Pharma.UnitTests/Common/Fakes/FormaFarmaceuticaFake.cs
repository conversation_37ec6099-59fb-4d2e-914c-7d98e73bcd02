using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Estoque.UnitTests.Fixtures.FormaFarmaceutica;
using UUIDNext;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class FormaFarmaceuticaFake
{
    public static FormaFarmaceutica
        CreateFormaFarmaceutica_Ativo()
    {
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();

        return new FormaFarmaceutica(cmd, Uuid.NewSequential(), Uuid.NewSequential());
    }

    public static FormaFarmaceutica
        CreateFormaFarmaceutica_NaoAtivo()
    {
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();

        return new FormaFarmaceutica(cmd, Uuid.NewSequential(), Uuid.NewSequential());
    }

    public static FormaFarmaceutica
        CreateFormaFarmaceutica_Ativo_Ml()
    {
        var cmd = CadastrarFormaFarmaceuticaCommandFake.CreateCadastrarFormaFarmaceuticaCommandValido();

        return new FormaFarmaceutica(cmd, Uuid.NewSequential(), Uuid.NewSequential());
    }
}
