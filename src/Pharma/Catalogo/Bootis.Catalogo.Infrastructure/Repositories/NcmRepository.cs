using Bootis.Catalogo.Domain.AggregatesModel.NcmAggregate;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Catalogo.Infrastructure.Repositories;

public class NcmRepository(IDbContext context) : Repository<Ncm>(context), INcmRepository
{
    public async Task<List<Ncm>> ObterNcmsPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<Ncm>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }
}