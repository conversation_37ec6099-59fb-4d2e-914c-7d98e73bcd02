using Bootis.Pessoa.Application.Requests.Cliente.Cadastrar;
using Bootis.Pessoa.Domain.Enumerations;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.Cliente;

public class CadastrarClienteRapidoRequestValidator : AbstractValidator<CadastrarRapidoRequest>
{
    public CadastrarClienteRapidoRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Nome)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Nome)));

        RuleFor(c => c.Nome)
            .MaximumLength(100)
            .WithMessage(f => localizer.GetMessage_Validation_TamanhoMaximo(nameof(f.Nome), 100));

        RuleFor(c => c.Pessoa)
            .IsInEnum()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Pessoa)));

        When(c => c.Pessoa == TipoPessoa.Fisica, () =>
        {
            RuleFor(c => c.Cpf).NotEmpty().WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Cpf))).IsValidCPF();
            RuleFor(c => c.Cnpj).Empty().WithMessage(f => localizer.GetMessage_Validation_CampoInvalido(nameof(f.Cnpj)));
        });

        When(c => c.Pessoa == TipoPessoa.Juridica, () =>
        {
            RuleFor(c => c.Cnpj).NotEmpty().WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Cnpj))).IsValidCNPJ();
            RuleFor(c => c.RazaoSocial).NotEmpty().WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.RazaoSocial)));
            RuleFor(c => c.Cpf).Empty().WithMessage(f => localizer.GetMessage_Validation_CampoInvalido(nameof(f.Cpf)));
        });
    }
}