using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Pessoa.UnitTests.Fixtures.PrescritorDocumento;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;

namespace Bootis.Pessoa.UnitTests.Domain.Prescritor;

public class PrescritorDocumentoTests : BaseTest
{
    private readonly PrescritorDocumento PrescritorDocumento = PrescritorDocumentoFakes.CriarCommandValido();

    [Fact]
    public void PrescritorDocumento_DeveAtualizarPrescritorDocumento()
    {
        //Arrange 
        var cmd = AtualizarRequestPrescritorDocumentoFakes.CriarCommandValido();

        //Action
        PrescritorDocumento.Atualizar(1.ToGuid(), cmd.TipoDocumentoId, cmd.Observacao, cmd.Identificacao);

        //Assert
        Assert.Equal(cmd.TipoDocumentoId, PrescritorDocumento.TipoDocumentoId);
        Assert.Equal(cmd.Identificacao, PrescritorDocumento.Identificacao);
    }
}
