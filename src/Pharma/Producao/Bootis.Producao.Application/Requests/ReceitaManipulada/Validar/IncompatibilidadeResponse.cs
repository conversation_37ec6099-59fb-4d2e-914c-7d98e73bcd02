using Bootis.Catalogo.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;

public class IncompatibilidadeResponse
{
    public NivelIncompatibilidade NivelMaisRestritivo { get; set; } = NivelIncompatibilidade.Ok;
    public IncompatibilidadeTipo Tipo { get; set; } = IncompatibilidadeTipo.Nenhuma;
    public IEnumerable<ParIncompativelDto> Incompatibilidades { get; set; } = [];
}

public class ParIncompativelDto
{
    public Guid ProdutoId { get; set; }
    public string ProdutoDescricao { get; set; } = string.Empty;
    public Guid ProdutoIncompativelId { get; set; }
    public string ProdutoIncompativelDescricao { get; set; } = string.Empty;
    public string Descricao { get; set; } = string.Empty;
}

public enum IncompatibilidadeTipo
{
    Nenhuma = 0,
    Unica = 1,
    Multipla = 2
}
