using Bootis.Localidade.Application.Extensions;
using Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate;
using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Fornecedor.Atualizar;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Fornecedor;

public class AtualizarEnderecoRequestHandler(
    IUnitOfWork unitOfWork,
    IFornecedorRepository fornecedorRepository,
    IPaisRepository paisRepository,
    IEstadoRepository estadoRepository,
    ICidadeRepository cidadeRepository)
    : IRequestHandler<AtualizarEnderecoRequest>
{
    public async Task Handle(AtualizarEnderecoRequest request, CancellationToken cancellationToken)
    {
        var fornecedorEndereco =
            await fornecedorRepository.ObterFornecedorEnderecoAsync(request.FornecedorEnderecoId);

        if (fornecedorEndereco == null)
            throw new ValidationException(nameof(request.FornecedorEnderecoId), "FornecedorEndereco não encontrado");

        var fornecedor = await fornecedorRepository.GetByIdAsync(fornecedorEndereco.FornecedorId);

        if (fornecedor == null)
            throw new ValidationException(nameof(fornecedorEndereco.FornecedorId), "Fornecedor não encontrado");

        var pais = await paisRepository.ObterPaisAsync(request.PaisId);

        if (pais == null)
            throw new ValidationException(nameof(request.PaisId), "País não encontrado");

        var estado = await estadoRepository.ObterEstadoAsync(request.EstadoId);

        if (estado == null)
            throw new ValidationException(nameof(request.EstadoId), "Estado não encontrado");

        var cidade = await cidadeRepository.ObterCidadeAsync(request.CidadeId);

        if (cidade == null)
            throw new ValidationException(nameof(request.CidadeId), "Cidade não encontrada");

        fornecedorEndereco.Atualizar(fornecedor.Id,
            pais,
            estado,
            cidade,
            request.Descricao,
            request.Bairro,
            request.Cep,
            request.Logradouro,
            request.Numero,
            request.Complemento,
            request.Principal);

        fornecedor.AdicionarEndereco(fornecedorEndereco);

        fornecedorRepository.Update(fornecedor);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}