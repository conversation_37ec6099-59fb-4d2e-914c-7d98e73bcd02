using Bootis.Pessoa.Domain.Enumerations;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;

public class Fornecedor : Entity, IAggregateRoot, ITenant
{
    public Fornecedor()
    {
    }

    public Fornecedor(bool ativo,
        TipoPessoa tipoPessoa,
        int tipoFornecedorId,
        string observacao,
        Guid tenantId,
        Guid groupTenantId) : this()
    {
        Ativo = ativo;
        TipoPessoa = tipoPessoa;
        TipoFornecedorId = tipoFornecedorId;
        Observacao = observacao;
        TenantId = tenantId;
        GroupTenantId = groupTenantId;
    }

    public Fornecedor(string nome,
        TipoPessoa tipoPessoa,
        TipoFornecedor tipoFornecedor,
        string observacao,
        string cpf,
        string linkImagem) : this()
    {
        Nome = nome;
        TipoPessoa = tipoPessoa;
        Observacao = observacao;
        Cpf = cpf;
        LinkImagem = linkImagem;
        Ativo = true;
        SetTipoFornecedor(tipoFornecedor);
    }

    public Fornecedor(string razaoSocial,
        string nome,
        TipoFornecedor tipoFornecedor,
        string observacao,
        TipoPessoa tipoPessoa,
        string cnpj,
        string linkImagem) : this()
    {
        RazaoSocial = razaoSocial;
        Nome = nome;
        Observacao = observacao;
        TipoPessoa = tipoPessoa;
        Cnpj = cnpj;
        LinkImagem = linkImagem;
        Ativo = true;
        SetTipoFornecedor(tipoFornecedor);
    }

    public string Nome { get; private set; }
    public TipoPessoa TipoPessoa { get; private set; }
    public string Cpf { get; private set; }
    public string Cnpj { get; private set; }
    public string LinkImagem { get; private set; }
    public string RazaoSocial { get; private set; }
    public string Observacao { get; private set; }
    public int TipoFornecedorId { get; private set; }
    public bool Ativo { get; private set; }


    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    private void SetTipoFornecedor(TipoFornecedor tipoFornecedor)
    {
        TipoFornecedor = tipoFornecedor;
    }

    public void Atualizar(string nome, TipoFornecedor tipoFornecedor, string observacao,
        TipoPessoa pessoa, string cpf, string cnpj, string razaoSocial, string linkImagem)
    {
        Nome = nome;
        TipoFornecedor = tipoFornecedor;
        Observacao = observacao;
        LinkImagem = linkImagem;

        switch (pessoa)
        {
            case TipoPessoa.Fisica:
                TipoPessoa = TipoPessoa.Fisica;
                Cpf = cpf;
                Cnpj = null;
                RazaoSocial = null;
                break;
            case TipoPessoa.Juridica:
                TipoPessoa = TipoPessoa.Juridica;
                RazaoSocial = razaoSocial;
                Cnpj = cnpj;
                Cpf = null;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(pessoa), pessoa, null);
        }
    }

    public void AtualizarFornecedorStep(string nome, TipoFornecedor tipoFornecedor, bool ativo,
        string observacao, TipoPessoa pessoa, string cpf, string cnpj, string razaoSocial)
    {
        Nome = nome;
        TipoFornecedor = tipoFornecedor;
        Ativo = ativo;
        Observacao = observacao;

        switch (pessoa)
        {
            case TipoPessoa.Fisica:
                TipoPessoa = TipoPessoa.Fisica;
                Cpf = cpf;
                Cnpj = null;
                RazaoSocial = null;
                break;
            case TipoPessoa.Juridica:
                TipoPessoa = TipoPessoa.Juridica;
                RazaoSocial = razaoSocial;
                Cnpj = cnpj;
                Cpf = null;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(pessoa), pessoa, null);
        }
    }

    public void AtualizarAtivo(bool ativo)
    {
        Ativo = ativo;
    }

    public void AdicionarContato(FornecedorContato contato)
    {
        ValidarContatoPrincipal(contato.Principal);
        Contatos.Add(contato);
    }

    public void AtualizarContato(FornecedorContato contato)
    {
        var contatoExistente = Contatos.FirstOrDefault(x => x.Id == contato.Id);

        if (contatoExistente != null)
            Contatos.Remove(contato);

        AdicionarContato(contato);
    }

    public void AdicionarEndereco(FornecedorEndereco endereco)
    {
        ValidarEnderecoPrincipal(endereco.Principal);
        Enderecos.Add(endereco);
    }

    public void AtualizarEndereco(FornecedorEndereco endereco)
    {
        var enderecoExistente = Enderecos.FirstOrDefault(x => x.Id == endereco.Id);

        if (enderecoExistente != null)
            Enderecos.Remove(endereco);

        AdicionarEndereco(endereco);
    }

    public void AdicionarDocumento(FornecedorDocumento documento)
    {
        Documentos.Add(documento);
    }

    private void ValidarContatoPrincipal(bool ehContatoPrincipal)
    {
        if (!ehContatoPrincipal || !Contatos.Any(x => x.Principal)) return;

        foreach (var contato in Contatos.Where(x => x.Principal)) contato.AtualizarPrincipal(false);
    }

    private void ValidarEnderecoPrincipal(bool ehEnderecoPrincipal)
    {
        if (!ehEnderecoPrincipal || !Enderecos.Any(x => x.Principal)) return;

        foreach (var endereco in Enderecos.Where(x => x.Principal)) endereco.AtualizarPrincipal(false);
    }


    #region Navigation Properties

    public virtual TipoFornecedor TipoFornecedor { get; set; }
    public virtual ICollection<FornecedorEndereco> Enderecos { get; set; } = new List<FornecedorEndereco>();
    public virtual ICollection<FornecedorContato> Contatos { get; set; } = new List<FornecedorContato>();
    public virtual ICollection<FornecedorDocumento> Documentos { get; set; } = new List<FornecedorDocumento>();

    #endregion
}