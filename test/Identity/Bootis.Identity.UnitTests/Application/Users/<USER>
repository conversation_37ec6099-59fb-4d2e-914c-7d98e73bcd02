using Bootis.Identity.Application.Users;
using Bootis.Identity.Domain.Entities;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Identity.UnitTests.Fixtures;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.Security;
using Bootis.Shared;

using FluentAssertions;
using Microsoft.Extensions.Localization;
using Moq;
using Xunit;

namespace Bootis.Identity.UnitTests.Application.Users;

public class ForgetPasswordRequestHandlerTests
{
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly ForgetPasswordRequestHandler _handler;
    private static readonly Mock<IStringLocalizer> _mockLocalizer = new();
    private static readonly Mock<IDomainEventDispatcher> _mockDomainEventDispatcher = new();
    private static readonly Mock<IUserContext> _mockUserContext = new();
    private static readonly Mock<IServiceProvider> _mockServiceProvider = new();

    static ForgetPasswordRequestHandlerTests()
    {
        _mockLocalizer.Setup(l => l[It.IsAny<string>()])
            .Returns((string key) => new LocalizedString(key, key));

        Localizer.Initialize(_mockLocalizer.Object);

        _mockServiceProvider.Setup(l => l.GetService(typeof(IDomainEventDispatcher)))
            .Returns(_mockDomainEventDispatcher.Object);
        _mockServiceProvider.Setup(l => l.GetService(typeof(IUserContext)))
            .Returns(_mockUserContext.Object);

        DomainEvent.Configure(_mockServiceProvider.Object);
        DomainEvent.SetDispatcher(_mockDomainEventDispatcher.Object);

        var userSession = new UserSession
        {
            UserIdentity = new UserIdentity
            {
                GroupTenantId = DefaultsValues.BootisId,
                TenantId = DefaultsValues.BootisId,
                UserId = DefaultsValues.BootisId
            },
            UserPreferences = new UserPreferences()
        };
        _mockUserContext.Setup(l => l.UserSession).Returns(userSession);
    }

    public ForgetPasswordRequestHandlerTests()
    {
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _userRepositoryMock = new Mock<IUserRepository>();
        
        _unitOfWorkMock.Setup(x => x.GetRepository<IUserRepository>())
            .Returns(_userRepositoryMock.Object);

        _handler = new ForgetPasswordRequestHandler(_unitOfWorkMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidEmail_ShouldGeneratePasswordResetCode()
    {
        // Arrange
        var email = "<EMAIL>";
        var request = new ForgetPasswordRequest(email);
        var user = UserFakes.CreateValidUser();
        
        _userRepositoryMock.Setup(x => x.GetByEmail(email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        user.PasswordResetCode.Should().NotBeNull();
        user.PasswordResetCode.Value.Should().NotBeEmpty();
        user.PasswordResetCode.ExpiresAt.Should().BeCloseTo(DateTimeOffset.UtcNow.AddMinutes(20), TimeSpan.FromSeconds(5));
        user.UpdatedAt.Should().BeCloseTo(DateTimeOffset.UtcNow, TimeSpan.FromSeconds(5));
        
        _userRepositoryMock.Verify(x => x.GetByEmail(email, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistentEmail_ShouldThrowValidationException()
    {
        // Arrange
        var email = "<EMAIL>";
        var request = new ForgetPasswordRequest(email);
        
        _userRepositoryMock.Setup(x => x.GetByEmail(email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User)null);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<ValidationException>()
            .Where(ex => ex.Errors.Any(e => e.PropertyName == nameof(request.Email)));
        
        _userRepositoryMock.Verify(x => x.GetByEmail(email, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("invalid-email")]
    [InlineData("@example.com")]
    [InlineData("test@")]
    public async Task Handle_WithInvalidEmailFormat_ShouldStillQueryRepository(string email)
    {
        // Arrange
        var request = new ForgetPasswordRequest(email);
        
        _userRepositoryMock.Setup(x => x.GetByEmail(email, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User)null);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<ValidationException>();
        
        _userRepositoryMock.Verify(x => x.GetByEmail(email, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithExistingPasswordResetCode_ShouldReplaceWithNewCode()
    {
        // Arrange
        var email = "<EMAIL>";
        var request = new ForgetPasswordRequest(email);
        var user = UserFakes.CreateValidUserWithPasswordResetCode();
        var originalResetCode = user.PasswordResetCode.Value;
        
        _userRepositoryMock.Setup(x => x.GetByEmail(email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        user.PasswordResetCode.Should().NotBeNull();
        user.PasswordResetCode.Value.Should().NotBe(originalResetCode);
        user.PasswordResetCode.ExpiresAt.Should().BeCloseTo(DateTimeOffset.UtcNow.AddMinutes(20), TimeSpan.FromSeconds(5));
        
        _userRepositoryMock.Verify(x => x.GetByEmail(email, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenRepositoryThrows_ShouldPropagateException()
    {
        // Arrange
        var email = "<EMAIL>";
        var request = new ForgetPasswordRequest(email);
        var expectedException = new InvalidOperationException("Database error");
        
        _userRepositoryMock.Setup(x => x.GetByEmail(email, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage(expectedException.Message);
        
        _userRepositoryMock.Verify(x => x.GetByEmail(email, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WhenSaveChangesThrows_ShouldPropagateException()
    {
        // Arrange
        var email = "<EMAIL>";
        var request = new ForgetPasswordRequest(email);
        var user = UserFakes.CreateValidUser();
        var expectedException = new InvalidOperationException("Save failed");
        
        _userRepositoryMock.Setup(x => x.GetByEmail(email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);
        
        _unitOfWorkMock.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var action = async () => await _handler.Handle(request, CancellationToken.None);
        
        await action.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage(expectedException.Message);
        
        _userRepositoryMock.Verify(x => x.GetByEmail(email, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithInactiveUser_ShouldStillGenerateResetCode()
    {
        // Arrange
        var email = "<EMAIL>";
        var request = new ForgetPasswordRequest(email);
        var user = UserFakes.CreateInactiveUser();
        
        _userRepositoryMock.Setup(x => x.GetByEmail(email, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        user.PasswordResetCode.Should().NotBeNull();
        user.IsActive.Should().BeFalse(); // User should remain inactive
        
        _userRepositoryMock.Verify(x => x.GetByEmail(email, It.IsAny<CancellationToken>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}