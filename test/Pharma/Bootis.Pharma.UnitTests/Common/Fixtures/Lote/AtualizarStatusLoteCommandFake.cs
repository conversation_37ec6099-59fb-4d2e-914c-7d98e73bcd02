using Bootis.Estoque.Application.Requests.Lote.Atualizar;
using Bootis.Estoque.Domain.Enumerations;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Lote;

public static class AtualizarStatusLoteCommandFake
{
    public static AtualizarStatusRequest CreateAtualizarStausLoteCommandValido()
    {
        return new AtualizarStatusRequest(new List<Guid> { Uuid.NewSequential() }, SituacaoLote.ControleQualidade);
    }
}
