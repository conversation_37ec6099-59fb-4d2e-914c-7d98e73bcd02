using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bootis.Setup.Api.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "capsulas_cor",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    cor_capsula = table.Column<string>(type: "text", nullable: true),
                    transparente = table.Column<bool>(type: "boolean", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_capsulas_cor", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "capsulas_tamanho",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    numero_capsula = table.Column<string>(type: "text", nullable: true),
                    volume_ml = table.Column<decimal>(type: "numeric", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_capsulas_tamanho", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "clientes",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    nome = table.Column<string>(type: "text", nullable: true),
                    pessoa = table.Column<int>(type: "integer", nullable: false),
                    cpf = table.Column<string>(type: "text", nullable: true),
                    cnpj = table.Column<string>(type: "text", nullable: true),
                    razao_social = table.Column<string>(type: "text", nullable: true),
                    observacao = table.Column<string>(type: "text", nullable: true),
                    desconto_produtos_acabados = table.Column<decimal>(type: "numeric", nullable: true),
                    desconto_formulas = table.Column<decimal>(type: "numeric", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    sequencia_group_tenant = table.Column<int>(type: "integer", nullable: false),
                    data_nascimento = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_clientes", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "clientes_contato",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    cliente_identificacao = table.Column<string>(type: "text", nullable: true),
                    tipo_contato_id = table.Column<Guid>(type: "uuid", nullable: false),
                    identificacao = table.Column<string>(type: "text", nullable: true),
                    principal = table.Column<bool>(type: "boolean", nullable: false),
                    observacao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_clientes_contato", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "clientes_documento",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    cliente_identificacao = table.Column<string>(type: "text", nullable: true),
                    tipo_documento_id = table.Column<Guid>(type: "uuid", nullable: false),
                    identificacao = table.Column<string>(type: "text", nullable: true),
                    observacao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_clientes_documento", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "clientes_endereco",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    cliente_identificacao = table.Column<string>(type: "text", nullable: true),
                    pais_descricao = table.Column<string>(type: "text", nullable: true),
                    estado_descricao = table.Column<string>(type: "text", nullable: true),
                    cidade_descricao = table.Column<string>(type: "text", nullable: true),
                    bairro = table.Column<string>(type: "text", nullable: true),
                    cep = table.Column<string>(type: "text", nullable: true),
                    logradouro = table.Column<string>(type: "text", nullable: true),
                    numero = table.Column<string>(type: "text", nullable: true),
                    complemento = table.Column<string>(type: "text", nullable: true),
                    principal = table.Column<bool>(type: "boolean", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_clientes_endereco", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "data_migration_state",
                columns: table => new
                {
                    correlation_id = table.Column<Guid>(type: "uuid", nullable: false),
                    current_state = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    source_connection_string = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    failed_step = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    retry_count = table.Column<int>(type: "integer", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    company_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_data_migration_state", x => x.correlation_id);
                });

            migrationBuilder.CreateTable(
                name: "embalagens_classificacao",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_embalagens_classificacao", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "especialidades_prescritor",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_especialidades_prescritor", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "formas_farmaceutica",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    laboratorio_descricao = table.Column<string>(type: "text", nullable: true),
                    laboratorio_id = table.Column<Guid>(type: "uuid", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    ordem = table.Column<int>(type: "integer", nullable: false),
                    percentual_minimo_excipiente = table.Column<decimal>(type: "numeric", nullable: false),
                    tipo_calculo_descricao = table.Column<string>(type: "text", nullable: true),
                    tipo_calculo = table.Column<int>(type: "integer", nullable: false),
                    uso_forma_farmaceutica_descricao = table.Column<string>(type: "text", nullable: true),
                    uso_forma_farmaceutica = table.Column<int>(type: "integer", nullable: false),
                    unidade_medida_descricao = table.Column<string>(type: "text", nullable: true),
                    unidade_medida_id = table.Column<Guid>(type: "uuid", nullable: false),
                    apresentacao = table.Column<string>(type: "text", nullable: true),
                    validade_dias = table.Column<int>(type: "integer", nullable: false),
                    data_cadastro = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    custo_operacional = table.Column<decimal>(type: "numeric", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_formas_farmaceutica", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "formulas_padrao",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    forma_farmaceutica_descricao = table.Column<string>(type: "text", nullable: true),
                    formula_padrao_desmembramento = table.Column<int>(type: "integer", nullable: false),
                    unidade_medida_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantidade_padrao = table.Column<decimal>(type: "numeric", nullable: false),
                    dias_validade = table.Column<int>(type: "integer", nullable: false),
                    diluicao = table.Column<decimal>(type: "numeric", nullable: false),
                    densidade = table.Column<decimal>(type: "numeric", nullable: false),
                    procedimento = table.Column<string>(type: "text", nullable: true),
                    rodape = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_formulas_padrao", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "formulas_padrao_item",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    formula_padrao_produto_descricao = table.Column<string>(type: "text", nullable: true),
                    quantidade = table.Column<decimal>(type: "numeric", nullable: false),
                    unidade_medida_id = table.Column<Guid>(type: "uuid", nullable: false),
                    fase = table.Column<int>(type: "integer", nullable: false),
                    tipo_item = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_formulas_padrao_item", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fornecedor_contatos",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    fornecedor_identificacao = table.Column<string>(type: "text", nullable: true),
                    fornecedor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tipo_contato_nome = table.Column<string>(type: "text", nullable: true),
                    tipo_contato_id = table.Column<Guid>(type: "uuid", nullable: false),
                    identificacao = table.Column<string>(type: "text", nullable: true),
                    principal = table.Column<bool>(type: "boolean", nullable: false),
                    observacao = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fornecedor_contatos", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fornecedor_documentos",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    fornecedor_identificacao = table.Column<string>(type: "text", nullable: true),
                    fornecedor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tipo_documento_nome = table.Column<string>(type: "text", nullable: true),
                    tipo_documento_id = table.Column<Guid>(type: "uuid", nullable: false),
                    identificacao = table.Column<string>(type: "text", nullable: true),
                    observacao = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fornecedor_documentos", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fornecedor_enderecos",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    fornecedor_identificacao = table.Column<string>(type: "text", nullable: true),
                    fornecedor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    pais_descricao = table.Column<string>(type: "text", nullable: true),
                    pais_id = table.Column<Guid>(type: "uuid", nullable: false),
                    estado_descricao = table.Column<string>(type: "text", nullable: true),
                    estado_id = table.Column<Guid>(type: "uuid", nullable: false),
                    cidade_descricao = table.Column<string>(type: "text", nullable: true),
                    cidade_id = table.Column<Guid>(type: "uuid", nullable: true),
                    bairro = table.Column<string>(type: "text", nullable: true),
                    cep = table.Column<string>(type: "text", nullable: true),
                    logradouro = table.Column<string>(type: "text", nullable: true),
                    numero = table.Column<string>(type: "text", nullable: true),
                    complemento = table.Column<string>(type: "text", nullable: true),
                    principal = table.Column<bool>(type: "boolean", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fornecedor_enderecos", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fornecedores",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    nome = table.Column<string>(type: "text", nullable: true),
                    tipo_pessoa = table.Column<int>(type: "integer", nullable: false),
                    cpf = table.Column<string>(type: "text", nullable: true),
                    cnpj = table.Column<string>(type: "text", nullable: true),
                    razao_social = table.Column<string>(type: "text", nullable: true),
                    observacao = table.Column<string>(type: "text", nullable: true),
                    tipo_fornecedor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fornecedores", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "grupos",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    nome = table.Column<string>(type: "text", nullable: true),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    se_ativo = table.Column<bool>(type: "boolean", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_grupos", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "grupos_permissoes",
                columns: table => new
                {
                    grupo_id = table.Column<Guid>(type: "uuid", nullable: false),
                    permissao_id = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_grupos_permissoes", x => x.grupo_id);
                });

            migrationBuilder.CreateTable(
                name: "laboratorios",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    nome_laboratorio = table.Column<string>(type: "text", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    local_estoque_descricao = table.Column<string>(type: "text", nullable: true),
                    local_estoque_id = table.Column<Guid>(type: "uuid", nullable: false),
                    empresa_descricao = table.Column<string>(type: "text", nullable: true),
                    empresa_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_laboratorios", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "locais_estoque",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    empresa_id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    tipo_estoque = table.Column<int>(type: "integer", nullable: false),
                    sequencia_group_tenant = table.Column<int>(type: "integer", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_locais_estoque", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "lotes",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    numero = table.Column<string>(type: "text", nullable: true),
                    data_lancamento = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    data_fabricacao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    data_validade = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    numero_nf = table.Column<int>(type: "integer", nullable: false),
                    serie_nf = table.Column<int>(type: "integer", nullable: false),
                    situacao = table.Column<int>(type: "integer", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    fornecedor_identificacao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lotes", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "lotes_informacao_tecnica",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    diluicao_fornecedor = table.Column<decimal>(type: "numeric", nullable: false),
                    fator_diluicao_fornecedor = table.Column<decimal>(type: "numeric", nullable: false),
                    concentracao_agua = table.Column<decimal>(type: "numeric", nullable: true),
                    fator_concentracao_agua = table.Column<decimal>(type: "numeric", nullable: true),
                    diluicao_interna = table.Column<decimal>(type: "numeric", nullable: true),
                    fator_diluicao_interna = table.Column<decimal>(type: "numeric", nullable: true),
                    densidade = table.Column<decimal>(type: "numeric", nullable: false),
                    pais_descricao = table.Column<string>(type: "text", nullable: true),
                    lote_numero = table.Column<string>(type: "text", nullable: true),
                    lote_produto_descricao = table.Column<string>(type: "text", nullable: true),
                    lote_numero_nf = table.Column<int>(type: "integer", nullable: false),
                    lote_serie_nf = table.Column<int>(type: "integer", nullable: false),
                    lote_origem_numero = table.Column<string>(type: "text", nullable: true),
                    lote_origem_produto_descricao = table.Column<string>(type: "text", nullable: true),
                    lote_origem_numero_nf = table.Column<int>(type: "integer", nullable: false),
                    lote_origem_serie_nf = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_lotes_informacao_tecnica", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "mensagens",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    exibe_venda = table.Column<bool>(type: "boolean", nullable: false),
                    exibe_rotulagem = table.Column<bool>(type: "boolean", nullable: false),
                    exibe_ficha_pesagem = table.Column<bool>(type: "boolean", nullable: false),
                    exibe_impressao_rotulo = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_mensagens", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "posologias",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    forma_farmaceutica_descricao = table.Column<string>(type: "text", nullable: true),
                    forma_farmaceutica_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantidade_dose_por_periodo = table.Column<decimal>(type: "numeric", nullable: false),
                    unidade_medida_descricao = table.Column<string>(type: "text", nullable: true),
                    unidade_medida_id = table.Column<Guid>(type: "uuid", nullable: false),
                    periodo_descricao = table.Column<string>(type: "text", nullable: true),
                    periodo = table.Column<int>(type: "integer", nullable: false),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_posologias", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "prescritores",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    nome_completo = table.Column<string>(type: "text", nullable: true),
                    tipo_registro_sigla = table.Column<string>(type: "text", nullable: true),
                    uf_registro_descricao = table.Column<string>(type: "text", nullable: true),
                    codigo_registro = table.Column<string>(type: "text", nullable: true),
                    data_nascimento = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    observacao = table.Column<string>(type: "text", nullable: true),
                    desconto_produtos_acabados = table.Column<decimal>(type: "numeric", nullable: true),
                    desconto_formulas = table.Column<decimal>(type: "numeric", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_prescritores", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "prescritores_contato",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    prescritor_nome_completo = table.Column<string>(type: "text", nullable: true),
                    tipo_contato_id = table.Column<Guid>(type: "uuid", nullable: false),
                    identificacao = table.Column<string>(type: "text", nullable: true),
                    principal = table.Column<bool>(type: "boolean", nullable: false),
                    observacao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_prescritores_contato", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "prescritores_documento",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    prescritor_nome_completo = table.Column<string>(type: "text", nullable: true),
                    tipo_documento_id = table.Column<Guid>(type: "uuid", nullable: false),
                    identificacao = table.Column<string>(type: "text", nullable: true),
                    observacao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_prescritores_documento", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "prescritores_endereco",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    prescritor_nome_completo = table.Column<string>(type: "text", nullable: true),
                    pais_descricao = table.Column<string>(type: "text", nullable: true),
                    estado_descricao = table.Column<string>(type: "text", nullable: true),
                    cidade_descricao = table.Column<string>(type: "text", nullable: true),
                    bairro = table.Column<string>(type: "text", nullable: true),
                    cep = table.Column<string>(type: "text", nullable: true),
                    logradouro = table.Column<string>(type: "text", nullable: true),
                    numero = table.Column<string>(type: "text", nullable: true),
                    complemento = table.Column<string>(type: "text", nullable: true),
                    principal = table.Column<bool>(type: "boolean", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_prescritores_endereco", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produto_grupos",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produto_grupos", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produtos",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    sequencia_group_tenant = table.Column<int>(type: "integer", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    descricao_rotulo = table.Column<string>(type: "text", nullable: true),
                    unidade_estoque_descricao = table.Column<string>(type: "text", nullable: true),
                    unidade_estoque_id = table.Column<Guid>(type: "uuid", nullable: false),
                    fornecedor_identificacao = table.Column<string>(type: "text", nullable: true),
                    fornecedor_id = table.Column<Guid>(type: "uuid", nullable: true),
                    sub_grupo_descricao = table.Column<string>(type: "text", nullable: true),
                    sub_grupo_id = table.Column<Guid>(type: "uuid", nullable: false),
                    controla_lote = table.Column<bool>(type: "boolean", nullable: false),
                    uso_continuo = table.Column<bool>(type: "boolean", nullable: false),
                    etiqueta = table.Column<bool>(type: "boolean", nullable: false),
                    controla_qualidade = table.Column<bool>(type: "boolean", nullable: false),
                    classe_produto_descricao = table.Column<string>(type: "text", nullable: true),
                    classe_produto_id = table.Column<Guid>(type: "uuid", nullable: false),
                    valor_custo = table.Column<decimal>(type: "numeric", nullable: false),
                    margem_lucro = table.Column<decimal>(type: "numeric", nullable: false),
                    valor_venda = table.Column<decimal>(type: "numeric", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produtos_associado",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    produto_associado_descricao = table.Column<string>(type: "text", nullable: true),
                    forma_farmaceutica_descricao = table.Column<string>(type: "text", nullable: true),
                    dosagem_minima = table.Column<decimal>(type: "numeric", nullable: false),
                    dosagem_maxima = table.Column<decimal>(type: "numeric", nullable: false),
                    unidade_medida_dosagem = table.Column<int>(type: "integer", nullable: false),
                    quantidade_associada = table.Column<decimal>(type: "numeric", nullable: false),
                    unidade_medida_quantidade_associada = table.Column<int>(type: "integer", nullable: false),
                    tipo_relacao_quantidade = table.Column<int>(type: "integer", nullable: false),
                    acumula = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos_associado", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produtos_diluido",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    forma_farmaceutica_descricao = table.Column<string>(type: "text", nullable: true),
                    dosagem_minima = table.Column<decimal>(type: "numeric", nullable: false),
                    dosagem_maxima = table.Column<decimal>(type: "numeric", nullable: false),
                    unidade_medida_id = table.Column<int>(type: "integer", nullable: false),
                    diluicao = table.Column<decimal>(type: "numeric", nullable: false),
                    se_todas_formas_farmaceuticas = table.Column<bool>(type: "boolean", nullable: false),
                    se_qualquer_dosagem = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos_diluido", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produtos_embalagem",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    embalagem_classificacao_descricao = table.Column<string>(type: "text", nullable: true),
                    volume = table.Column<decimal>(type: "numeric", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos_embalagem", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produtos_incompativel",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    produto_incompativel_descricao = table.Column<string>(type: "text", nullable: true),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    nivel_incompatibilidade = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos_incompativel", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produtos_materia_prima",
                columns: table => new
                {
                    produto_descricao = table.Column<string>(type: "text", nullable: false),
                    produto_id = table.Column<Guid>(type: "uuid", nullable: false),
                    numero_dcb = table.Column<int>(type: "integer", nullable: true),
                    dcb_id = table.Column<Guid>(type: "uuid", nullable: true),
                    numero_cas = table.Column<string>(type: "text", nullable: true),
                    cas_id = table.Column<Guid>(type: "uuid", nullable: true),
                    unidade_prescricao_descricao = table.Column<string>(type: "text", nullable: true),
                    unidade_prescricao_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tipo_componente_descricao = table.Column<string>(type: "text", nullable: true),
                    tipo_componente_id = table.Column<Guid>(type: "uuid", nullable: false),
                    somente_laboratorio = table.Column<bool>(type: "boolean", nullable: true),
                    is_pellets = table.Column<bool>(type: "boolean", nullable: true),
                    is_excipiente = table.Column<bool>(type: "boolean", nullable: true),
                    is_qsp = table.Column<bool>(type: "boolean", nullable: true),
                    somente_diluido = table.Column<bool>(type: "boolean", nullable: true),
                    dias_validade = table.Column<int>(type: "integer", nullable: true),
                    exige_capsula_gastroresistente = table.Column<bool>(type: "boolean", nullable: true),
                    is_monodroga = table.Column<bool>(type: "boolean", nullable: true),
                    tolerancia_pesagem_up = table.Column<decimal>(type: "numeric", nullable: true),
                    tolerancia_pesagem_down = table.Column<decimal>(type: "numeric", nullable: true),
                    peso_molecular_sal = table.Column<decimal>(type: "numeric", nullable: false),
                    peso_molecular_base = table.Column<decimal>(type: "numeric", nullable: false),
                    fator_equivalencia = table.Column<decimal>(type: "numeric", nullable: false),
                    valencia = table.Column<decimal>(type: "numeric", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos_materia_prima", x => x.produto_descricao);
                });

            migrationBuilder.CreateTable(
                name: "produtos_sinonimo",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    produto_descricao = table.Column<string>(type: "text", nullable: true),
                    sinonimo = table.Column<string>(type: "text", nullable: true),
                    descricao_rotulo = table.Column<string>(type: "text", nullable: true),
                    fator_equivalencia = table.Column<decimal>(type: "numeric", nullable: true),
                    percentual_correcao = table.Column<decimal>(type: "numeric", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos_sinonimo", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "produtos_tipo_capsula",
                columns: table => new
                {
                    produto_descricao = table.Column<string>(type: "text", nullable: false),
                    produto_id = table.Column<int>(type: "integer", nullable: false),
                    tipo_capsula_id = table.Column<int>(type: "integer", nullable: false),
                    tipo_capsula_descricao = table.Column<string>(type: "text", nullable: true),
                    numero_capsula_id = table.Column<int>(type: "integer", nullable: false),
                    numero_capsula_tamanho = table.Column<string>(type: "text", nullable: true),
                    capsula_cor_id = table.Column<int>(type: "integer", nullable: false),
                    capsula_cor_descricao = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_produtos_tipo_capsula", x => x.produto_descricao);
                });

            migrationBuilder.CreateTable(
                name: "status_atendimento",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    ativo = table.Column<bool>(type: "boolean", nullable: false),
                    ordem = table.Column<int>(type: "integer", nullable: false),
                    acao_venda_primaria_descricao = table.Column<string>(type: "text", nullable: true),
                    acao_venda_primaria_id = table.Column<Guid>(type: "uuid", nullable: true),
                    acao_venda_secundaria_descricao = table.Column<string>(type: "text", nullable: true),
                    acao_venda_secundaria_id = table.Column<Guid>(type: "uuid", nullable: true),
                    usuario_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_status_atendimento", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "sub_grupos",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    grupo_descricao = table.Column<string>(type: "text", nullable: true),
                    grupo_id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sub_grupos", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "tipos_capsula",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    descricao = table.Column<string>(type: "text", nullable: true),
                    capsula_vegetal = table.Column<bool>(type: "boolean", nullable: false),
                    capsula_gastroresistente = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_tipos_capsula", x => x.id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "capsulas_cor");

            migrationBuilder.DropTable(
                name: "capsulas_tamanho");

            migrationBuilder.DropTable(
                name: "clientes");

            migrationBuilder.DropTable(
                name: "clientes_contato");

            migrationBuilder.DropTable(
                name: "clientes_documento");

            migrationBuilder.DropTable(
                name: "clientes_endereco");

            migrationBuilder.DropTable(
                name: "data_migration_state");

            migrationBuilder.DropTable(
                name: "embalagens_classificacao");

            migrationBuilder.DropTable(
                name: "especialidades_prescritor");

            migrationBuilder.DropTable(
                name: "formas_farmaceutica");

            migrationBuilder.DropTable(
                name: "formulas_padrao");

            migrationBuilder.DropTable(
                name: "formulas_padrao_item");

            migrationBuilder.DropTable(
                name: "fornecedor_contatos");

            migrationBuilder.DropTable(
                name: "fornecedor_documentos");

            migrationBuilder.DropTable(
                name: "fornecedor_enderecos");

            migrationBuilder.DropTable(
                name: "fornecedores");

            migrationBuilder.DropTable(
                name: "grupos");

            migrationBuilder.DropTable(
                name: "grupos_permissoes");

            migrationBuilder.DropTable(
                name: "laboratorios");

            migrationBuilder.DropTable(
                name: "locais_estoque");

            migrationBuilder.DropTable(
                name: "lotes");

            migrationBuilder.DropTable(
                name: "lotes_informacao_tecnica");

            migrationBuilder.DropTable(
                name: "mensagens");

            migrationBuilder.DropTable(
                name: "posologias");

            migrationBuilder.DropTable(
                name: "prescritores");

            migrationBuilder.DropTable(
                name: "prescritores_contato");

            migrationBuilder.DropTable(
                name: "prescritores_documento");

            migrationBuilder.DropTable(
                name: "prescritores_endereco");

            migrationBuilder.DropTable(
                name: "produto_grupos");

            migrationBuilder.DropTable(
                name: "produtos");

            migrationBuilder.DropTable(
                name: "produtos_associado");

            migrationBuilder.DropTable(
                name: "produtos_diluido");

            migrationBuilder.DropTable(
                name: "produtos_embalagem");

            migrationBuilder.DropTable(
                name: "produtos_incompativel");

            migrationBuilder.DropTable(
                name: "produtos_materia_prima");

            migrationBuilder.DropTable(
                name: "produtos_sinonimo");

            migrationBuilder.DropTable(
                name: "produtos_tipo_capsula");

            migrationBuilder.DropTable(
                name: "status_atendimento");

            migrationBuilder.DropTable(
                name: "sub_grupos");

            migrationBuilder.DropTable(
                name: "tipos_capsula");
        }
    }
}
