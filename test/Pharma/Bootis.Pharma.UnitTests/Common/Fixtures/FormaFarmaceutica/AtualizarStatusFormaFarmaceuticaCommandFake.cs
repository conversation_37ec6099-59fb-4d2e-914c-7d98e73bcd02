using Bootis.Catalogo.Application.Requests.FormaFarmaceutica.Atualizar;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.FormaFarmaceutica;

public class AtualizarStatusFormaFarmaceuticaCommandFake
{
    public static AtualizarStatusRequest CriarCommandValido_Ativo()
    {
        var listaIds = new List<Guid>
        {
            Uuid.NewSequential()
        };

        return new AtualizarStatusRequest
        {
            Id = listaIds,
            Ativo = true
        };
    }

    public static AtualizarStatusRequest CriarCommandValido_Inativo()
    {
        var listaIds = new List<Guid>
        {
            Uuid.NewSequential()
        };

        return new AtualizarStatusRequest
        {
            Id = listaIds,
            Ativo = false
        };
    }
}
