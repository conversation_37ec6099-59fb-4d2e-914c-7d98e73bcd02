using System.Data;
using Bootis.Estoque.Application.Requests.OperacaoEstoque.Obter;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.OperacaoEstoque.Obter;

public class ObterRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT id,
                                  id,
                                  descricao,
                                  tipo_operacao,
                                  se_aquisicao,
                                  se_projecao,
                                  se_perda,
                                  se_inventario
                           FROM operacoes_estoque
                           WHERE id = @id;
                           """;
        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql, new { request.Id });

        if (result is null)
        {
            var message = Localizer.Instance.GetMessage_OperacaoEstoque_GuidNaoEncontrado(request.Id);
            throw new DomainException(message);
        }

        return result;
    }
}