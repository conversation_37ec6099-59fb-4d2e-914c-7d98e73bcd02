using System.Data;
using Bootis.Catalogo.Application.Requests.UnidadeMedida.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.UnidadeMedida.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT un.id,
                                  un.abreviacao AS unidade_abreviacao,
                                  un.descricao,
                                  un.ativo,
                                  un.unidade_alternativa
                           FROM unidades_medida un
                           WHERE un.id is not Null
                           !@SEARCH_CONDITION@!
                           """;

        var conditionalTipoUnidade = new ConditionalFilterField<ListarRequest>
        {
            Filter = "UN.tipo_unidade = @TipoUnidade",
            Predicate = filter => filter.TipoUnidade.HasValue
        };

        var searchAbreviacao = new StringSearchField
        {
            Field = "UN.abreviacao",
            CompareType = StringCompareType.Contains
        };

        var searchDescricao = new StringSearchField
        {
            Field = "UN.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchAbreviacao)
            .AddSearchField(searchDescricao)
            .AddFilter(conditionalTipoUnidade)
            .ExecuteAsync();
    }
}