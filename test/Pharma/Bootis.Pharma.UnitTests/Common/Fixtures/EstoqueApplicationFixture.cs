using Bootis.Catalogo.Application.UseCases.Grupo;
using Bootis.Estoque.Application.Requests.TransferenciaLote.Cadastrar;
using Bootis.Estoque.Domain.Dtos.TransferenciaLote;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Moq.AutoMock;
using UUIDNext;
using Xunit;

namespace Bootis.Estoque.UnitTests.Fixtures;

[CollectionDefinition(nameof(EstoqueApplicationCollection))]
public class EstoqueApplicationCollection : ICollectionFixture<EstoqueApplicationFixture>
{
}

public class EstoqueApplicationFixture : IDisposable
{
    public AutoMocker Mocker;

    public void Dispose()
    {
        Mocker = null;
        GC.SuppressFinalize(this);
    }

    public CadastrarRequestHandler ObterCadastrarGrupoCommandHandler()
    {
        Mocker = new AutoMocker();
        return Mocker.CreateInstance<CadastrarRequestHandler>();
    }

    public static TransferenciaLoteItensDto ObterCadastrarTransferenciaLoteItensCommandValido()
    {
        return new TransferenciaLoteItensDto
        {
            LoteId = Uuid.NewSequential(),
            ProdutoId = Uuid.NewSequential(),
            QuantidadeTransferida = 10,
            UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
        };
    }

    public static TransferenciaLoteItensDto ObterCadastrarTransferenciaLoteItensCommandUnidadeInvalida()
    {
        return new TransferenciaLoteItensDto
        {
            LoteId = Uuid.NewSequential(),
            ProdutoId = Uuid.NewSequential(),
            QuantidadeTransferida = 10,
            UnidadeMedidaId = (UnidadeMedidaAbreviacao)77
        };
    }

    public static TransferenciaLoteItensDto ObterCadastrarTransferenciaLoteItensCommandLoteNulo()
    {
        return new TransferenciaLoteItensDto
        {
            ProdutoId = Uuid.NewSequential(),
            QuantidadeTransferida = 10,
            UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
        };
    }

    public static CadastrarRequest ObterCadastrarTransferenciaLoteComItensCommandValidoUnidadeInvalida()
    {
        var transferenciaLote = new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = Uuid.NewSequential(),
            LocalDeEstoqueOrigemId = Uuid.NewSequential(),
            Observacao = "teste"
        };

        var trasferenciaLotesItensList = new List<TransferenciaLoteItensDto>
        {
            new()
            {
                LoteId = Uuid.NewSequential(),
                ProdutoId = Uuid.NewSequential(),
                QuantidadeTransferida = 10,
                UnidadeMedidaId = (UnidadeMedidaAbreviacao)77
            },
            new()
            {
                LoteId = Uuid.NewSequential(),
                ProdutoId = Uuid.NewSequential(),
                QuantidadeTransferida = 15,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.mg
            }
        };

        return new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = Uuid.NewSequential(),
            LocalDeEstoqueOrigemId = Uuid.NewSequential(),
            Observacao = "teste",
            TransferenciaLoteItens = trasferenciaLotesItensList
        };
    }

    public static CadastrarRequest ObterCadastrarTransferenciaLoteComItensCommandValido()
    {
        var transferenciaLote = new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = Uuid.NewSequential(),
            LocalDeEstoqueOrigemId = Uuid.NewSequential(),
            Observacao = "teste"
        };

        var trasferenciaLotesItensList = new[]
        {
            new TransferenciaLoteItensDto
            {
                LoteId = Uuid.NewSequential(),
                ProdutoId = Uuid.NewSequential(),
                QuantidadeTransferida = 10,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
            }
        };

        return new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = Uuid.NewSequential(),
            LocalDeEstoqueOrigemId = Uuid.NewSequential(),
            Observacao = "teste",
            TransferenciaLoteItens = trasferenciaLotesItensList
        };
    }
}
