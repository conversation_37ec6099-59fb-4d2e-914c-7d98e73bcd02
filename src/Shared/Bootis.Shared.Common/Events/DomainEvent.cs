using Microsoft.Extensions.DependencyInjection;

namespace Bootis.Shared.Common.Events;

public interface IDomainEvent
{
}

public static class DomainEvent
{
    private static IServiceProvider _serviceProvider;
    private static readonly AsyncLocal<IDomainEventDispatcher> Dispatcher = new();

    public static void Configure(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public static void SetDispatcher(IDomainEventDispatcher dispatcher)
    {
        Dispatcher.Value = dispatcher;
    }

    public static void ClearDispatcher()
    {
        Dispatcher.Value = null;
    }

    public static void Raise<T>(T domainEvent) where T : IDomainEvent
    {
        var dispatcher = GetDispatcher();
        dispatcher.PublishInternal(domainEvent);
    }

    public static void RaiseExternal<T>(T domainEvent) where T : IDomainEvent
    {
        var dispatcher = GetDispatcher();
        dispatcher.PublishExternal(domainEvent);
    }

    private static IDomainEventDispatcher GetDispatcher()
    {
        if (Dispatcher.Value != null)
            return Dispatcher.Value;

        if (_serviceProvider != null) return _serviceProvider.GetRequiredService<IDomainEventDispatcher>();

        throw new InvalidOperationException(
            "DomainEvent not configured. Call DomainEvent.Configure() or DomainEvent.SetDispatcher()");
    }
}