using System.Data;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Obter;

public class
    ObterDetalhesValoresComponentesHandler(IDbConnection connection)
    : IRequestHandler<ObterDetalhesValoresRequest, ObterDetalhesValoresResponse>
{
    public async Task<ObterDetalhesValoresResponse> Handle(ObterDetalhesValoresRequest request,
        CancellationToken cancellationToken)
    {
        const string sqlValoresComponentes = @" SELECT rc.receita_manipulada_rastreio_calculo_id AS componente_id,
                                                       rc.valor_custo AS preco_custo,
                                                       rc.margem_lucro AS percentual_lucro,
                                                       rc.valor_venda AS preco_venda
                                                   FROM receitas_manipuladas_calculos rc
                                                        JOIN receitas_manipuladas rm ON
                                                             rm.id = rc.receita_manipulada_id
                                                   WHERE rm.id = @Id
        ";

        const string sqlTotais = @" SELECT SUM(rc.valor_custo) AS preco_custo_total,
                                           SUM(rc.valor_venda) AS preco_venda_total,
                                           AVG(rc.margem_lucro) AS percentual_lucro_total,
                                           rmv.custo_operacional,
                                           rmv.valor_lucro AS lucro,
                                           rmv.valor_total AS preco_receita,
                                           rmv.valor_bruto AS preco_bruto,
                                           rmv.valor_desconto AS desconto
                                       FROM receitas_manipuladas_valores rmv
                                            JOIN receitas_manipuladas rm ON
                                                 rm.id = rmv.receita_manipulada_id
                                            JOIN receitas_manipuladas_calculos rc ON
                                                 rc.receita_manipulada_id = rm.id
                                      WHERE rm.id = @Id
                                      GROUP BY rmv.custo_operacional,
                                               rmv.valor_total,
                                               rmv.valor_lucro,
                                               rmv.valor_bruto,
                                               rmv.valor_desconto
        ";

        var componentes =
            (await connection.QueryAsync<DetalheValorComponente>(sqlValoresComponentes,
                new { Id = request.ReceitaId })).ToList();
        var totais =
            await connection.QueryFirstOrDefaultAsync<ObterDetalhesValoresResponse>(sqlTotais,
                new { Id = request.ReceitaId });

        totais.Componentes = componentes;

        return totais;
    }
}