using Bootis.Organizacional.Application.Requests.Empresa.Cadastrar;
using FluentValidation;

namespace Bootis.Organizacional.Application.Validations.Empresa;

public class CadastrarRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarRequestValidator()
    {
        RuleFor(c => c.NomeFantasia)
            .NotEmpty();

        RuleFor(c => c.RazaoSocial)
            .NotEmpty();

        RuleFor(c => c.Cnpj)
            .IsValidCNPJ();

        RuleFor(c => c.Usuario)
            .NotNull();

        RuleFor(c => c.Cnae)
            .NotNull()
            .Length(7);

        RuleFor(c => c.InscricaoEstadual)
            .NotNull()
            .MaximumLength(12);

        RuleFor(c => c.InscricaoMunicipal)
            .MaximumLength(11);

        When(c => c.Usuario != null, () =>
        {
            RuleFor(c => c.Usuario!.Nome)
                .NotEmpty();

            RuleFor(c => c.Usuario!.Email)
                .NotEmpty()
                .EmailAddress();
        });

        When(c => c.Endereco != null, () =>
        {
            RuleFor(c => c.Endereco!.Cep)
                .Matches("[0-9]{8}")
                .Length(8)
                .NotEmpty();

            RuleFor(c => c.Endereco!.Bairro)
                .NotEmpty();

            RuleFor(c => c.Endereco!.Cidade)
                .NotEmpty();

            RuleFor(c => c.Endereco!.Logradouro)
                .NotEmpty();
        });

        RuleFor(c => c.Telefone)
            .MaximumLength(14);
    }
}