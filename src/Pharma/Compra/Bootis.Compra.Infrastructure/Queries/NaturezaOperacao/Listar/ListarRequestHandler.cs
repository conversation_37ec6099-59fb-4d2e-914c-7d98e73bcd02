using System.Data;
using Bootis.Compra.Application.Requests.NaturezaOperacao.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Compra.Infrastructure.Queries.NaturezaOperacao.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT descricao,
                                  id
                             FROM naturezas_operacao
                            WHERE 1 = 1
                                  !@SEARCH_CONDITION@!
                           """;

        var searchDescricao = new StringSearchField
        {
            Field = "descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricao)
            .ExecuteAsync();
    }
}