using Bootis.Compra.Domain.Enumerations;
using MediatR;

namespace Bootis.Venda.Application.Requests.PedidoVenda.Atualizar;

public abstract class AtualizarItemRequest : IRequest
{
    public Guid PedidoVendaItemId { get; set; }
    public decimal ValorUnitario { get; set; }
    public decimal Quantidade { get; set; }
    public decimal? Desconto { get; set; }
    public TipoDesconto TipoDesconto { get; set; }
}

public class AtualizarItemProdutoAcabadoRequest : AtualizarItemRequest
{
    public Guid ProdutoId { get; set; }
    public Guid? PrescritorId { get; set; }
    public DateTime? DataPrescricao { get; set; }
}