using Asp.Versioning;
using Bootis.Catalogo.Application.Requests.Grupo.Atualizar;
using Bootis.Catalogo.Application.Requests.Grupo.Cadastrar;
using Bootis.Catalogo.Application.Requests.Grupo.Listar;
using Bootis.Catalogo.Application.Requests.Grupo.Obter;
using Bootis.Catalogo.Application.Requests.Grupo.Remover;
using Bootis.Catalogo.Application.Requests.Grupo.Validar;
using Bootis.Shared.Api.Attributes;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Query;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Catalogo.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Catalogo")]
[Route("catalogo/v{version:apiVersion}/[controller]")]
public class GrupoController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(typeof(CadastrarResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_Grupos_Cadastrar)]
    public async Task<IActionResult> Cadastrar(CadastrarRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_Grupos_EditarDetalhes)]
    public async Task<IActionResult> Atualizar(AtualizarRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpDelete]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_Grupos_Excluir)]
    public async Task<IActionResult> Remover(RemoverRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_Grupos_VerDetalhes)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var request = new ObterRequest { Id = id };
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpPost]
    [Route("ValidarDescricaoGrupo")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> DescricaoGrupoValidator([FromBody] ValidarDescricaoRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(PaginatedResult<ListarResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_Grupos_VerDetalhes)]
    public async Task<IActionResult> Listar([FromQuery] ListarRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_Grupos_VerDetalhes)]
    public async Task<IActionResult> ListarDetalhado([FromQuery] ListarDetalhadoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }
}