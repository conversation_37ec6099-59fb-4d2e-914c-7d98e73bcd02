using Bootis.Pessoa.Application.Requests.Cliente.Remover;
using Bootis.Pessoa.Application.UseCases.Cliente;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Application.ClienteDocumento;

public class RemoverDocumentoRequestHandlerTests : BaseTest
{
    private readonly Mock<IClienteRepository> _clienteRepositoryMock;

    private readonly RemoverDocumentoRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverDocumentoRequestHandlerTests()
    {
        _clienteRepositoryMock = new Mock<IClienteRepository>();

        _handler = new RemoverDocumentoRequestHandler(unitOfWork.Object, _clienteRepositoryMock.Object);
    }

    [Fact]
    public async Task RemoverClienteDocumento_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = new RemoverDocumentoRequest(Uuid.NewSequential());
        var model = ClienteDocumentoFake.CriarCommandValido();

        _clienteRepositoryMock.Setup(l => l.ObterDocumentoPorDocumentoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(model);

        _clienteRepositoryMock.Setup(l => l.Remove(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _clienteRepositoryMock
            .Verify(
                l => l.Remove(
                    It.Is<Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteDocumento>(m =>
                        m.Identificacao == model.Identificacao)),
                Times.Once);
    }

    [Fact]
    public async Task RemoveClienteDocumento_ExecutadoComErro()
    {
        //Arrange
        var cmd = new RemoverDocumentoRequest(Uuid.NewSequential());
        var model = ClienteDocumentoFake.CriarCommandValido();

        _clienteRepositoryMock.Setup(l => l.ObterDocumentoPorDocumentoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteDocumento)null);

        _clienteRepositoryMock.Setup(l => l.Remove(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
