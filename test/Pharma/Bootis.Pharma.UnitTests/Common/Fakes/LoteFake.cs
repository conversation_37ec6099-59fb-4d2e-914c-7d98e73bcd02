using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.UnitTests.Fixtures.Lote;
using Bootis.Shared.Common.Extensions;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class LoteFake
{
    public static Lote CreateLoteValido()
    {
        var lote = new Lote("1",
            ProdutoFake.CreateProdutoUsoConsumoFake(), FornecedorFakes.CriarFornecedorValido(),
            DateTime.UtcNow.ToDateOnly(), DateTime.MaxValue.ToDateOnly(), 1, 1, UsuarioFakes.GerarUsuarioValido().Id,
            SituacaoLote.Liberado);

        return lote;
    }

    public static Lote CreateLoteValido_ComInformacaoTecnica()
    {
        var lote = new Lote("1",
            ProdutoFake.CreateProdutoUsoConsumoFake(), FornecedorFakes.CriarFornecedorValido(),
            DateTime.UtcNow.ToDateOnly(), DateTime.MaxValue.ToDateOnly(), 1, 1, UsuarioFakes.GerarUsuarioValido().Id,
            SituacaoLote.Liberado);

        var cmd = CadastrarLoteCommandFake.CreateCadastrarLoteCommandComInformacoesTecnicasValido();

        lote.AtualizarLoteInformacaoTecnica(1.ToGuid(), CreateLoteValido(), cmd.LoteInformacaoTecnica);

        return lote;
    }

    public static LoteInformacaoTecnica CreateLoteInformacaoTecnicaValido()
    {
        var cmd = CadastrarLoteCommandFake.CreateCadastrarLoteCommandComInformacoesTecnicasValido()
            .LoteInformacaoTecnica;

        var lote = new LoteInformacaoTecnica(CreateLoteValido().Id, Guid.NewGuid(), Guid.NewGuid(), cmd);

        return lote;
    }
}
