using System.Runtime.Serialization;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Compra.Application.Requests.PedidoCompra.Listar;

public class ListarPedidosCompraItensResponse
{
    public Guid PedidoCompraId { get; set; }
    public int PedidoCompraNumero { get; set; }
    public IEnumerable<ItemResponse> Itens { get; set; }
}

public class ItemResponse
{
    public Guid PedidoCompraItemId { get; set; }
    public int ProdutoNumero { get; set; }
    public string ProdutoDescricao { get; set; }
    public decimal PrecoUnitario { get; set; }
    public decimal Quantidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public string UnidadeMedidaDescricao { get; set; }
    public double PrecoTotalLiquido { get; set; }

    [IgnoreDataMember] public Guid PedidoCompraId { get; set; }
}