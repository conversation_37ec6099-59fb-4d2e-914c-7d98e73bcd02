using Bootis.CEP.Client;
using Bootis.Estoque.Api.Converters.Produto;
using Bootis.Shared.Api.Extensions;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;
var assembly = typeof(Program).Assembly;
builder.AddDefaultSetup(assembly, "PharmaDBConnection", jsonOptions =>
{
    jsonOptions.JsonSerializerOptions.Converters.Add(new CadastrarRequestConverter());
    jsonOptions.JsonSerializerOptions.Converters.Add(new AtualizarRequestConverter());
}, options =>
{
    options.SerializerSettings.Converters.Add(new NCadastrarRequestConverter());
    options.SerializerSettings.Converters.Add(new NAtualizarRequestConverter());
});

builder.AddServiceDefaults();

var baseAddress = configuration["Clients:Cep:BaseAddress"];

builder.Services.AddHttpClient<ICepClient, CepClient>(client => { client.BaseAddress = new Uri(baseAddress!); });

var app = builder.Build();

app.ConfigureDefaultSetup(configuration);

await app.RunAsync();

public partial class Program { }