using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Compra.Application.Requests.PedidoCompra.Atualizar;
using Bootis.Compra.Application.UseCases.PedidoCompra;
using Bootis.Compra.Domain.Enumerations;
using Bootis.Compra.UnitTests.Fixtures;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;
using DomainAggregate = Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate;

namespace Bootis.Compra.UnitTests.Application.PedidoCompra;

public class AtualizarRascunhoPedidoCompraCommandHandlerTests : BaseTest
{
    private readonly Mock<IFornecedorRepository> _fornecedorRepositoryMock;
    private readonly AtualizarRascunhoRequestHandler _handler;
    private readonly Mock<DomainAggregate.IPedidoCompraRepository> _pedidoCompraRepositoryMock;
    private readonly Mock<IProdutoRepository> _produtoRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarRascunhoPedidoCompraCommandHandlerTests()
    {
        _pedidoCompraRepositoryMock = new Mock<DomainAggregate.IPedidoCompraRepository>();
        _fornecedorRepositoryMock = new Mock<IFornecedorRepository>();
        _produtoRepositoryMock = new Mock<IProdutoRepository>();
        unitOfWork.Setup(l => l.GetRepository<DomainAggregate.IPedidoCompraRepository>())
            .Returns(_pedidoCompraRepositoryMock.Object);
        unitOfWork.Setup(l => l.GetRepository<IFornecedorRepository>()).Returns(_fornecedorRepositoryMock.Object);
        unitOfWork.Setup(l => l.GetRepository<IProdutoRepository>()).Returns(_produtoRepositoryMock.Object);
        _handler = new AtualizarRascunhoRequestHandler(unitOfWork.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AtualizarRascunhoPedidoCompra_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarRascunhoPedidoCompraCommandFake.AtualizarRascunhoPedidoCompraCommandValido_SemItem();
        var cmdInterno = CadastrarPedidoCompraCommandFake.CreateCadastrarPedidoCompraComandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var produtos = new List<Produto>
        {
            ProdutoFake.CreateProdutoMateriaPrimaFake()
        };
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        var itemEditado = new DomainAggregate.PedidoCompraItem(pedidoCompra,
            ProdutoFake.CreateProdutoMateriaPrimaFake(),
            cmdInterno.PedidoCompraItens.First());

        var itensRequest = new List<AtualizarItemRascunhoRequest>
        {
            new()
            {
                PedidoCompraItemId = itemEditado.Id,
                ProdutoId = produtos.First().Id,
                TipoDesconto = TipoDesconto.DescontoMonetario,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.g,
                PrecoUnitario = 1,
                Quantidade = 1,
                ValorDescontoUnitario = 1
            }
        };

        cmd.PedidoCompraItens = itensRequest;

        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FornecedorId.GetValueOrDefault()))
            .Returns(
                Task.FromResult(fornecedor));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(pedidoCompra));

        _pedidoCompraRepositoryMock.Setup(l => l.Update(pedidoCompra))
            .Verifiable();

        pedidoCompra.AdicionarItem(itemEditado);

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(It.Is<DomainAggregate.PedidoCompra>(m => m.Observacao == pedidoCompra.Observacao)),
            Times.Once);
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(It.Is<DomainAggregate.PedidoCompra>(m => m.Status == pedidoCompra.Status)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarRascunhoPedidoCompra_FornecedorGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarRascunhoPedidoCompraCommandFake.AtualizarRascunhoPedidoCompraCommandValido_SemItem();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FornecedorId.GetValueOrDefault()))
            .Returns(Task.FromResult<Fornecedor>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(It.Is<DomainAggregate.PedidoCompra>(m => m.Observacao == pedidoCompra.Observacao)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarRascunhoPedidoCompra_GuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarRascunhoPedidoCompraCommandFake.AtualizarRascunhoPedidoCompraCommandValido_SemItem();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var produtos = new List<Produto>();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FornecedorId.GetValueOrDefault()))
            .Returns(
                Task.FromResult(fornecedor));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult<DomainAggregate.PedidoCompra>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _pedidoCompraRepositoryMock.Verify(
            l => l.Update(It.Is<DomainAggregate.PedidoCompra>(m => m.Observacao == pedidoCompra.Observacao)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarRascunhoPedidoCompra_PedidoCompraItemGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarRascunhoPedidoCompraCommandFake.AtualizarRascunhoPedidoCompraCommandValido_ComItem();
        var cmdInterno = CadastrarPedidoCompraCommandFake.CreateCadastrarPedidoCompraComandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var produtos = new List<Produto>();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        var item = new DomainAggregate.PedidoCompraItem(pedidoCompra, ProdutoFake.CreateProdutoMateriaPrimaFake(),
            cmdInterno.PedidoCompraItens.First());

        pedidoCompra.AdicionarItem(item);

        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FornecedorId.GetValueOrDefault()))
            .Returns(
                Task.FromResult(fornecedor));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(pedidoCompra));

        _pedidoCompraRepositoryMock.Setup(l => l.Update(pedidoCompra))
            .Verifiable();

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }

    [Fact]
    public async Task AtualizarRascunhoPedidoCompra_ProdutoGuidNaoEncontrado_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarRascunhoPedidoCompraCommandFake.AtualizarRascunhoPedidoCompraCommandValido_SemItem();
        var cmdInterno = CadastrarPedidoCompraCommandFake.CreateCadastrarPedidoCompraComandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var produtos = new List<Produto>();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        var item = new DomainAggregate.PedidoCompraItem(pedidoCompra, ProdutoFake.CreateProdutoMateriaPrimaFake(),
            cmdInterno.PedidoCompraItens.First());

        pedidoCompra.AdicionarItem(item);

        var itensRequest = new List<AtualizarItemRascunhoRequest>
        {
            new()
            {
                PedidoCompraItemId = item.Id,
                ProdutoId = Uuid.NewSequential(),
                TipoDesconto = TipoDesconto.DescontoMonetario,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.g,
                PrecoUnitario = 1,
                Quantidade = 1,
                ValorDescontoUnitario = 1
            }
        };

        cmd.PedidoCompraItens = itensRequest;

        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FornecedorId.GetValueOrDefault()))
            .Returns(
                Task.FromResult(fornecedor));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(pedidoCompra));

        _pedidoCompraRepositoryMock.Setup(l => l.Update(pedidoCompra))
            .Verifiable();

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }

    [Fact]
    public async Task AtualizarRascunhoPedidoCompra_DescontoInvalido_ExecutadoComErro()
    {
        //Arrange
        var cmd = AtualizarRascunhoPedidoCompraCommandFake.AtualizarRascunhoPedidoCompraCommandValido_SemItem();
        var cmdInterno = CadastrarPedidoCompraCommandFake.CreateCadastrarPedidoCompraComandValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();
        var produtos = new List<Produto>();
        var pedidoCompra = PedidoCompraFake.CreatePedidoCompra();

        var itemDescontoInvalido = new DomainAggregate.PedidoCompraItem(pedidoCompra,
            ProdutoFake.CreateProdutoMateriaPrimaFake(),
            cmdInterno.PedidoCompraItens.First());

        var itensRequest = new List<AtualizarItemRascunhoRequest>
        {
            new()
            {
                PedidoCompraItemId = itemDescontoInvalido.Id,
                ProdutoId = Guid.Empty,
                TipoDesconto = TipoDesconto.DescontoMonetario,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.g,
                PrecoUnitario = 1,
                Quantidade = 1,
                ValorDescontoUnitario = 10
            }
        };

        produtos = new List<Produto>
        {
            ProdutoFake.CreateProdutoMateriaPrimaFake()
        };

        cmd.PedidoCompraItens = itensRequest;

        _fornecedorRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FornecedorId.GetValueOrDefault()))
            .Returns(
                Task.FromResult(fornecedor));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtos));

        _pedidoCompraRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(pedidoCompra));

        _pedidoCompraRepositoryMock.Setup(l => l.Update(pedidoCompra))
            .Verifiable();

        pedidoCompra.AdicionarItem(itemDescontoInvalido);

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
    }
}
