<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{389244fb-9bfb-4f39-bfaa-4fc2d48a91eb}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Common\Bootis.Shared.Common.csproj"/>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Domain\Bootis.Shared.Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>

