using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.SubGrupo.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.SubGrupo.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder($"""
                                     SELECT sb.descricao AS subgrupo_descricao,
                                            sb.id,
                                            gr.descricao AS grupo_descricao,    
                                            gr.id AS grupo_id
                                       FROM sub_grupos sb 
                                            LEFT JOIN produto_grupos gr ON
                                                      gr.id = sb.grupo_id
                                      WHERE sb.id = '{request.Id}' 
                                        AND sb.group_tenant_id = '{userContext.GroupTenantId}';
                                     """);

        var subgrupos = await connection.QueryAsync<ObterResponse>(sql.ToString(), new { request.Id });

        if (subgrupos == null || !subgrupos.Any())
            throw new DomainException(Localizer.Instance.GetMessage_SubGrupo_GuidNaoEncontrado(request.Id));

        return subgrupos.FirstOrDefault();
    }
}