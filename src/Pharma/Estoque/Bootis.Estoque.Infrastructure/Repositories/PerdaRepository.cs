using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class PerdaRepository(IUserContext userContext, IDbContext context)
    : Repository<Perda>(context), IPerdaRepository
{
    public Task<Perda> ObterPorId(Guid id)
    {
        return DbSet
            .FirstOrDefaultAsync(c =>
                c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }
}