using Bootis.Compra.Domain.AggregatesModel.TipoFreteAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Compra.Infrastructure.EntityConfigurations;

public class TipoFreteEntityTypeConfiguration : BaseEntityTypeConfiguration<TipoFrete>
{
    public override void Configure(EntityTypeBuilder<TipoFrete> builder)
    {
        builder.ToTable("tipos_frete");

        builder
            .Property(c => c.Descricao)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired();

        base.Configure(builder);
    }
}