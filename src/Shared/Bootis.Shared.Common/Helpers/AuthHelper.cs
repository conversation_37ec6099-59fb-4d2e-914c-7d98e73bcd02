using Bootis.Shared.Common.ValueObjects.Auth;

namespace Bootis.Shared.Common.Helpers;

public static class AuthHelper
{
    public static string GetDefaultErrorMessage(AuthenticationError errorCode)
    {
        return errorCode switch
        {
            AuthenticationError.NotAuthenticated => "Authentication required",
            AuthenticationError.MissingAuthorizationHeader => "Authorization header is missing",
            AuthenticationError.InvalidAuthorizationFormat => "Invalid authorization header format",

            AuthenticationError.InvalidToken => "Token is invalid or malformed",
            AuthenticationError.ExpiredToken => "Token has expired",
            AuthenticationError.RevokedToken => "Token has been revoked",
            AuthenticationError.MalformedToken => "Token format is malformed",

            AuthenticationError.UserNotFound => "User not found",
            AuthenticationError.UserInactive => "User account is inactive",
            AuthenticationError.UserBlocked => "User account is blocked",

            AuthenticationError.SessionExpired => "Session has expired",
            AuthenticationError.SessionRevoked => "Session has been revoked",
            AuthenticationError.InvalidSession => "Invalid session",

            AuthenticationError.InvalidTenant => "Invalid tenant",
            AuthenticationError.TenantInactive => "Tenant is inactive",

            AuthenticationError.InsufficientPermissions => "Insufficient permissions",
            AuthenticationError.RoleNotGranted => "Required role not granted",
            AuthenticationError.MissingRequiredRole => "Missing required role",

            AuthenticationError.Unauthorized => "Access denied - authentication required",
            AuthenticationError.Forbidden => "Access denied - insufficient privileges",

            AuthenticationError.InternalError => "Internal server error",
            AuthenticationError.ServiceUnavailable => "Authentication service is unavailable",
            AuthenticationError.NetworkError => "Network error occurred",

            AuthenticationError.Unknown => "Unknown authentication error",
            _ => "Authentication failed"
        };
    }
}