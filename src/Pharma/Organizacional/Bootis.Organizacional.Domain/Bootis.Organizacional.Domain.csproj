<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{04b501ae-f413-45b9-bfcf-fce17d85f595}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Domain\Bootis.Shared.Domain.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Common\Bootis.Organizacional.Common.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Contracts\Bootis.Organizacional.Contracts.csproj"/>
        <ProjectReference Include="..\Bootis.Organizacional.Resources\Bootis.Organizacional.Resources.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>

