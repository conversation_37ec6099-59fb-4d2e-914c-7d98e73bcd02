using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.TransferenciaLote.Remover;
using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.TransferenciaLote;

public class RemoverItensRequestHandler(
    IUnitOfWork unitOfWork,
    ITransferenciaLoteRepository transferenciaLoteRepository)
    : IRequestHandler<RemoverItensRequest>
{
    public async Task Handle(RemoverItensRequest request, CancellationToken cancellationToken)
    {
        var transferenciaLoteItem = await transferenciaLoteRepository
            .ObterTransferenciaLoteItensAsync(request.TransferenciaLoteItemId).ConfigureAwait(false);

        transferenciaLoteRepository.Remove(transferenciaLoteItem);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}