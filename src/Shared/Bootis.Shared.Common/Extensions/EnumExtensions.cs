using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace Bootis.Shared.Common.Extensions;

public static class EnumExtensions
{
    public static T? GetValueFromDescription<T>(string description) where T : struct, Enum
    {
        foreach (var field in typeof(T).GetFields())
        {
            var attribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;

            if (attribute?.Description == description)
                return (T)field.GetValue(null)!;
        }

        return null;
    }

    public static string GetDescription(this Enum value)
    {
        var field = value.GetType().GetField(value.ToString());
        var attribute = field?.GetCustomAttribute<DescriptionAttribute>();

        return attribute == null ? value.ToString() : attribute.Description;
    }

    public static string GetDisplayName(this Enum value, bool includeKey = false)
    {
        ArgumentNullException.ThrowIfNull(value);

        var fieldInfo = value.GetType().GetField(value.ToString());
        var displayAttribute = fieldInfo?.GetCustomAttributes(typeof(DisplayAttribute), false)
            .FirstOrDefault() as DisplayAttribute;

        var displayName = displayAttribute?.Name ?? value.ToString();

        return includeKey ? $"{value} - {displayName}" : displayName;
    }
}