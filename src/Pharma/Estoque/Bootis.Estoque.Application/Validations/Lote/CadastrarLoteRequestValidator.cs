using Bootis.Estoque.Application.Requests.Lote.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Estoque.Application.Validations.Lote;

public class CadastrarLoteRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarLoteRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(body => body)
            .NotEmpty();

        RuleFor(c => c.Numero)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Numero)))
            .MaximumLength(20)
            .WithMessage(l => localizer.GetMessage_Validation_TamanhoMaximo(nameof(l.Numero), 20));

        RuleFor(c => c.ProdutoId)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.ProdutoId)));

        RuleFor(c => c.FornecedorId)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.FornecedorId)));

        RuleFor(c => c.DataFabricacao)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.DataFabricacao)));

        RuleFor(c => c.DataValidade)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.DataValidade)));

        RuleFor(c => c.NumeroNf)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.NumeroNf)));

        RuleFor(c => c.SerieNf)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.SerieNf)));
    }
}