using System.Data;
using Bootis.Compra.Application.Requests.PedidoCompra.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using Dapper;
using MediatR;

namespace Bootis.Compra.Infrastructure.Queries.PedidoCompra.Listar;

public class ListarEmAbertoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarPedidosCompraEmAbertoRequest,
        PaginatedResult<ListarPedidosCompraEmAbertoResponse>>
{
    public async Task<PaginatedResult<ListarPedidosCompraEmAbertoResponse>> Handle(
        ListarPedidosCompraEmAbertoRequest request,
        CancellationToken cancellationToken)
    {
        var sql = $"""
                   SELECT 
                       pc.id,
                       fc.id AS fornecedor_id,
                       fc.nome AS nome_fornecedor,
                       pc.sequencia_group_tenant,
                       pc.previsao_entrega,
                       pc.data_lancamento,
                       pct.total_pedido
                   FROM pedidos_compra pc
                   LEFT JOIN fornecedores fc 
                       ON fc.id = pc.fornecedor_id
                   LEFT JOIN pedidos_compra_totalizadores pct 
                       ON pct.id = pc.id
                   WHERE fc.id = '{request.FornecedorId}'
                       AND pc.group_tenant_id = '{userContext.GroupTenantId}'
                       AND pc.status IN (3, 5)  
                       !@SEARCH_CONDITION@!
                   """;

        const string sqlItens = """
                                SELECT 
                                    pci.id AS pedido_compra_item_id,
                                    pc.id AS pedido_compra_id,
                                    pro.id AS produto_id,
                                    pro.descricao,
                                    pro.sequencia_group_tenant AS codigo_produto,
                                    pro.classe_produto_id,
                                    pci.preco_unitario AS valor_unitario,
                                    pci.quantidade,
                                    pci.unidade_medida_id,
                                    un.descricao AS unidade_medida_descricao,
                                    un.abreviacao AS unidade_medida_abreviacao
                                FROM pedidos_compra_itens pci
                                LEFT JOIN pedidos_compra pc 
                                    ON pc.id = pci.pedido_compra_id
                                LEFT JOIN produtos pro 
                                    ON pro.id = pci.produto_id
                                LEFT JOIN unidades_medida un 
                                    ON un.id = pci.unidade_medida_id
                                WHERE pc.id = ANY ( @pedidosId )
                                """;

        var searchPedidoCodigo = new NumberSearchField
        {
            Field = "pc.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        var searchDataLancamento = new DateSearchField
        {
            Field = "pc.data_lancamento",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchDataPrevisao = new DateSearchField
        {
            Field = "pc.previsao_entrega",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var response =
            await PaginatedQueryBuilder<ListarPedidosCompraEmAbertoRequest, ListarPedidosCompraEmAbertoResponse>
                .Create(connection, sql, request, userContext)
                .AddSearchField(searchPedidoCodigo)
                .AddSearchField(searchDataLancamento)
                .AddSearchField(searchDataPrevisao)
                .ExecuteAsync();

        if (response.Count == 0)
            return response;

        var pedidoCompraItens = await connection.QueryAsync<PedidoCompraItemResponse>(sqlItens,
            new { pedidosId = (IList<Guid>)response.Data.Select(c => c.Id).ToList() });

        foreach (var pedidoCompraResponse in response.Data)
            pedidoCompraResponse.Itens =
                pedidoCompraItens.Where(c => c.PedidoCompraId == pedidoCompraResponse.Id);

        return response;
    }
}