using Bootis.Compra.Application.Extensions;
using Bootis.Compra.Application.Requests.PedidoCompra.Atualizar;
using Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Compra.Application.UseCases.PedidoCompra;

public class EstornarLiberadoRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IPedidoCompraRepository pedidoCompraRepository,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<EstornarLiberadoRequest>
{
    public async Task Handle(EstornarLiberadoRequest request, CancellationToken cancellationToken)
    {
        var pedidoCompra = await pedidoCompraRepository.ObterPedidoCompraAsync(request.PedidoCompraId);
        var usuario = await usuarioRepository.ObterUsuarioAsync(userContext.UserId);

        pedidoCompra.EstornarLiberado(usuario);

        pedidoCompraRepository.Update(pedidoCompra);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}