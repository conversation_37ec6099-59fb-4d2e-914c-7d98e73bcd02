{"App": {"Name": "Report"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] ({SourceContext}) {Message:lj}{NewLine}{Exception}"}}]}, "ConnectionStrings": {"PharmaDBConnection": "", "BlobConnection": "", "RabbitMQConnection": ""}, "UrlAccessAllowed": ["https://bootisdev.blob.core.windows.net/image/"], "Security": {"ApiKey": "", "IdentityServer": "https://tst.bootis.com.br/identity", "JwtKey": "4a+Tf9Mw+XnplQ7sNkXtEt+H4rJ1ltbppozxzb7WgI8QB6TxhasmoKC18U42WfSJYSa9orGRtiDx+iE+XKSgAA=="}, "AllowedHosts": "*"}