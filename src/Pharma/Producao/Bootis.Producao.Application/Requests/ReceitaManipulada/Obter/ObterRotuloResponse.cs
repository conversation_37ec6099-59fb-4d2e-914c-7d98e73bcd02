using Bootis.Catalogo.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;

public class ObterRotuloResponse
{
    public Guid ModeloRotuloId { get; set; }
    public string ModeloRotuloDescricao { get; set; }
    public int ClienteCodigo { get; set; }
    public string ClienteNome { get; set; }
    public string ClienteEndereco { get; set; }
    public string ClienteTelefone { get; set; }
    public int PacienteCodigo { get; set; }
    public string PacienteNome { get; set; }
    public int ReceitaManipuladaRegistro { get; set; }
    public string ReceitaManipuladaCodigoBarras { get; set; }
    public DateTime DataEmissao { get; set; }
    public DateTime DataPrescricao { get; set; }
    public DateTime DataEntrega { get; set; }
    public DateTime DataValidadePrescricao { get; set; }
    public DateOnly DataValidade { get; set; }
    public string ObservacaoReceita { get; set; }
    public string ObservacoesProdutosMateriaPrima { get; set; }
    public decimal QuantidadeReceita { get; set; }
    public decimal QuantidadeDose { get; set; }
    public decimal QuantidadeDivisaoDose { get; set; }
    public int QuantidadeRepetir { get; set; }
    public decimal QuantidadeDivisao { get; set; }
    public DateTime Validade { get; set; }
    public string NumeroRegistroLivro { get; set; }
    public string DescricaoEtiqueta { get; set; }
    public string FormaFarmaceuticaApresentacao { get; set; }
    public UsoFormaFarmaceutica FormaFarmaceuticaUso { get; set; }
    public string Posologia { get; set; }
    public string ItensReceitaDescricao { get; set; }
    public TipoTarjaMedicamento TipoTarjaMedicamento { get; set; }
    public string PrescritorCodigoRegistro { get; set; }
    public string PrescritorNomeCompleto { get; set; }
    public int PrescritorUfRegistroId { get; set; }
    public int TipoRegistroPrescritorId { get; set; }
    public string EmpresaNomeFantasia { get; set; }
    public string EmpresaCnpj { get; set; }
    public string EmpresaEndereco { get; set; }
    public string EmpresaTelefone { get; set; }
    public string EmpresaEmail { get; set; }
    public string EmpresaSite { get; set; }
}