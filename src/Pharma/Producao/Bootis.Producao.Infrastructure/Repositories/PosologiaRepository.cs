using Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Producao.Infrastructure.Repositories;

public class PosologiaRepository(IUserContext userContext, IDbContext context)
    : Repository<Posologia>(context), IPosologiaRepository
{
    public Task<Posologia> ObterPorIdAsync(Guid id)
    {
        return DbSet
            .SingleOrDefaultAsync(c =>
                c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<List<Posologia>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<Posologia>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }

    public async Task<bool> ValidarPorFormaFarmaceuticaIdEDescricaoAsync(Guid formaFarmaceuticaId, string descricao)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                    FROM posologias po
                                                   WHERE po.forma_farmaceutica_id = @formaFarmaceuticaId
                                                     AND po.descricao = @descricao
                                                     AND po.group_tenant_id = @groupTenantId) THEN 1 ELSE 0 END AS existe_registro
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { formaFarmaceuticaId, descricao, groupTenantId = userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarPorIdEFormaFarmaceuticaIdEDescricaoAsync(Guid id,
        Guid formaFarmaceuticaId, string descricao)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT 1 
                                                      FROM posologias po
                                                     WHERE po.id = @id
                                                       AND po.forma_farmaceutica_id = @formaFarmaceuticaId
                                                       AND po.descricao = @descricao
                                                       AND po.group_tenant_id = @group_tenant_id ) 
                                                       THEN 1 ELSE 0 END AS existe_registro
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new
            {
                id, formaFarmaceuticaId, descricao,
                group_tenant_id = userContext.GroupTenantId
            },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public Task<int> VerificarDependenciaAsync(Guid id)
    {
        const string sql = """
                           SELECT COUNT(rcm.posologia_id) AS total_dependencias
                           FROM posologias po
                           LEFT JOIN receitas_manipuladas rcm ON rcm.posologia_id = po.id
                           WHERE po.id = @id
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return dbConnection.QueryFirstOrDefaultAsync<int>(sql, new { id },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }
}