using System.Runtime.Serialization;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Compra.Application.Requests.NotaFiscalEntrada.Obter;

public class ObterLoteResponse
{
    public Guid Id { get; set; }
    public int Numero { get; set; }
    public int Serie { get; set; }
    public DateTime? DataEmissao { get; set; }
    public DateTime? DataEntrega { get; set; }
    public DateTime? DataLancamento { get; set; }
    public int Status { get; set; }
    public Guid FornecedorId { get; set; }
    public string FornecedorDescricao { get; set; }
    public string RazaoSocial { get; set; }
    public string Cnpj { get; set; }
    public Guid? TransportadoraId { get; set; }
    public string TransportadoraDescricao { get; set; }
    public Guid NaturezaOperacaoId { get; set; }
    public string NaturezaOperacaoDescricao { get; set; }
    public float ValorBaseIcms { get; set; }
    public float ValorIcms { get; set; }
    public float ValorBaseIcmsSubstituicao { get; set; }
    public float ValorIcmsSubstituicao { get; set; }
    public float ValorProdutos { get; set; }
    public float ValorFrete { get; set; }
    public float ValorSeguro { get; set; }
    public float ValorDesconto { get; set; }
    public float ValorOutrasDespesas { get; set; }
    public float ValorIpi { get; set; }
    public float ValorTotalNotaFiscal { get; set; }
    public Guid TipoFreteId { get; set; }
    public string TipoFreteDescricao { get; set; }
    public string InformacoesComplementares { get; set; }
    public IEnumerable<ItemResponse> Itens { get; set; }
}

public class ItemResponse
{
    public Guid NotaFiscalEntradaItemId { get; set; }
    public Guid ProdutoId { get; set; }
    public string ProdutoDescricao { get; set; }
    public int ClasseProdutoId { get; set; }
    public string ClasseProdutoDescricao { get; set; }
    public int ProdutoCodigo { get; set; }
    public Guid NcmId { get; set; }
    public int NcmCodigo { get; set; }
    public Guid CfopId { get; set; }
    public int CfopCodigo { get; set; }
    public Guid CstCsosnId { get; set; }
    public int CstCsosnCodigo { get; set; }
    public int Status { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public string UnidadeMedidaDescricao { get; set; }
    public TipoUnidade TipoUnidade { get; set; }
    public int QuantidadeComprada { get; set; }
    public int ValorUnitario { get; set; }
    public int BaseCalculoIcms { get; set; }
    public int ValorIcms { get; set; }
    public int ValorIpi { get; set; }
    public int AliquotaIpi { get; set; }
    public int Total { get; set; }
    public IEnumerable<LoteResponse> Lotes { get; set; }
}

public class LoteResponse
{
    public Guid NotaFiscalEntradaLoteId { get; set; }
    public string NumeroLote { get; set; }
    public Guid LocalEstoqueId { get; set; }
    public DateTime DataFabricacao { get; set; }
    public DateTime DataValidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public float Quantidade { get; set; }
    public int QuantidadeRotulo { get; set; }
    public LoteInformacaoTecnicaResponse InformacaoTecnica { get; set; }

    [IgnoreDataMember] public Guid NotaFiscalEntradaItemId { get; set; }
}

public class LoteInformacaoTecnicaResponse
{
    public Guid? PaisOrigemId { get; set; }
    public string PaisOrigemDescricao { get; set; }
    public float Densidade { get; set; }
    public float DiluicaoFornecedor { get; set; }
    public float FatorDiluicaoFornecedor { get; set; }
    public float? ConcentracaoAgua { get; set; }
    public float? FatorConcentracaoAgua { get; set; }
    public LoteUnidadeAlternativaResponse LoteUnidadeAlternativa1 { get; set; }
    public LoteUnidadeAlternativaResponse LoteUnidadeAlternativa2 { get; set; }

    [IgnoreDataMember] public Guid NotaFiscalEntradaLoteId { get; set; }
}

public class LoteUnidadeAlternativaResponse
{
    public UnidadeMedidaAbreviacao UnidadeAlternativaId { get; set; }
    public UnidadeMedidaAbreviacao UnidadeAlternativaConversaoId { get; set; }
    public float? QuantidadeUnidadeAlternativa { get; set; }

    [IgnoreDataMember] public Guid NotaFiscalEntradaLoteId { get; set; }
}