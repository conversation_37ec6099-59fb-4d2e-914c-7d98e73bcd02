using Bootis.Localidade.Domain.AggregatesModel;
using Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate;
using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;

public class ClienteEndereco : EnderecoBase, IAggregateRoot, ITenant
{
    public ClienteEndereco()
    {
    }

    public ClienteEndereco(Guid clienteId,
        Pais pais,
        Estado estado,
        Cidade cidade,
        string bairro,
        string numero,
        string complemento,
        bool principal,
        string cep,
        string descricao,
        string logradouro)
    {
        ClienteId = clienteId;
        Pais = pais;
        Estado = estado;
        Cidade = cidade;
        Bairro = bairro;
        Cep = cep;
        Logradouro = logradouro;
        Numero = numero;
        Complemento = complemento;
        Principal = principal;
        Descricao = descricao;
    }

    public Guid ClienteId { get; private set; }
    public virtual Cliente Cliente { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void Atualizar(Guid clienteId, Pais pais,
        Estado estado, Cidade cidade, string bairro,
        string cep, string logradouro, string numero,
        string complemento, string descricao, bool principal)
    {
        ClienteId = clienteId;
        Pais = pais;
        Estado = estado;
        Cidade = cidade;
        Bairro = bairro;
        Cep = cep;
        Logradouro = logradouro;
        Numero = numero;
        Complemento = complemento;
        Descricao = descricao;
        Principal = principal;
    }

    public void AtualizarPrincipal(bool principal)
    {
        Principal = principal;
    }
}