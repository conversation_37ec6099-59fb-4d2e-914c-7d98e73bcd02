using System.Data;
using Bootis.Catalogo.Application.Requests.FormaFarmaceutica.Listar;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.FormaFarmaceutica.Listar;

public class
    ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                                ff.descricao,
                                ff.id,
                                ff.ordem,
                                ff.apresentacao,
                                ff.uso_forma_farmaceutica,
                                la.nome_laboratorio AS laboratorio_descricao,
                                ff.ativo
                             FROM formas_farmaceutica ff
                                  LEFT JOIN laboratorios la ON la.id = ff.laboratorio_id
                            WHERE ff.group_tenant_id = @GroupTenantId 
                                  !@SEARCH_CONDITION@!
                           """;

        var searchDescricao = new StringSearchField
        {
            Field = "ff.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchOrdem = new NumberSearchField
        {
            Field = "ff.ordem",
            CompareType = NumericCompareType.Contains
        };

        var searchApresentacao = new StringSearchField
        {
            Field = "ff.apresentacao",
            CompareType = StringCompareType.Contains
        };

        var searchLaboratorio = new StringSearchField
        {
            Field = "la.nome_laboratorio",
            CompareType = StringCompareType.Contains
        };

        var searchUso = new EnumSearchField<UsoFormaFarmaceutica>
        {
            Field = "ff.uso_forma_farmaceutica"
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricao)
            .AddSearchField(searchOrdem)
            .AddSearchField(searchApresentacao)
            .AddSearchField(searchLaboratorio)
            .AddSearchField(searchUso)
            .ExecuteAsync();
    }
}