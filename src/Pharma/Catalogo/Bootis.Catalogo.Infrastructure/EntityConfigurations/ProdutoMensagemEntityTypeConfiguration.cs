using Bootis.Catalogo.Domain.AggregatesModel.ProdutoMensagemAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Catalogo.Infrastructure.EntityConfigurations;

public class ProdutoMensagemEntityTypeConfiguration : IEntityTypeConfiguration<ProdutoMensagem>
{
    public void Configure(EntityTypeBuilder<ProdutoMensagem> builder)
    {
        builder.ToTable("mensagem_produtos");

        builder.<PERSON><PERSON><PERSON>(c => new { c.MensagemId, c.ProdutoId });

        builder
          .HasOne(c => c.Produto)
          .WithMany(c => c.ProdutoMensagem)
          .HasForeignKey(c => c.ProdutoId)
          .IsRequired()
          .OnDelete(DeleteBehavior.Cascade);
    }
}