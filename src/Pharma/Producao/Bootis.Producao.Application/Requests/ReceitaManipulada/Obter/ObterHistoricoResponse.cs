using System.ComponentModel;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;

public class ObterHistoricoResponse
{
    public List<HistoricoReceita> Historico { get; set; } = new();
}

public class HistoricoReceita
{
    public Guid Id { get; set; }
    public DateTime DataHora { get; set; }
    public StatusReceita Status { get; set; }

    public string StatusDescricao
    {
        get
        {
            var descriptionAttribute = typeof(StatusReceita)
                    .GetField(Status.ToString())
                    ?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];
            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : Status.ToString();
        }
    }

    public string Usuario { get; set; }
    public bool EhCriacao { get; set; }
}