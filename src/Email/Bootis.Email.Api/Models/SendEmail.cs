using MediatR;

namespace Bootis.Email.Api.Models;

public record SendEmail : IRequest
{
    public string Template { get; set; }

    public string Subject { get; set; }

    public EmailAddress From { get; set; }

    public IDictionary<string, string> Properties { get; set; }

    public IEnumerable<EmailAddress> Tos { get; set; } = [];

    public IEnumerable<EmailAddress> Ccs { get; set; } = [];

    public IEnumerable<EmailAddress> Bccs { get; set; } = [];

    public IEnumerable<Attachment> Attachements { get; set; } = [];

    public EmailLanguageType AcceptLanguage { get; set; }
}