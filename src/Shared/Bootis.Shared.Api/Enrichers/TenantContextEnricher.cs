using System.Security.Claims;
using Bootis.Shared.Common.Security;
using Microsoft.AspNetCore.Http;
using Serilog.Core;
using Serilog.Events;

public class TenantContextEnricher(IHttpContextAccessor httpContextAccessor) : ILogEventEnricher
{
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        var httpContext = httpContextAccessor.HttpContext;

        if (httpContext?.User?.Identity?.IsAuthenticated != true) return;

        AddClaimIfNotNull(httpContext, logEvent, propertyFactory, ClaimTypes.Name);
        AddClaimIfNotNull(httpContext, logEvent, propertyFactory, ClaimTypes.NameIdentifier);
        AddClaimIfNotNull(httpContext, logEvent, propertyFactory, CustomClaims.Id);
        AddClaimIfNotNull(httpContext, logEvent, propertyFactory, CustomClaims.TenantId);
        AddClaimIfNotNull(httpContext, logEvent, propertyFactory, CustomClaims.GroupTenantId);
        AddClaimIfNotNull(httpContext, logEvent, propertyFactory, CustomClaims.SessionId);
    }

    private static void AddClaimIfNotNull(HttpContext httpContext, LogEvent logEvent,
        ILogEventPropertyFactory propertyFactory,
        string claimType)
    {
        var claim = httpContext.User.Claims.FirstOrDefault(c => c.Type == claimType);
        if (claim == null || string.IsNullOrWhiteSpace(claim.Value)) return;

        var tenantIdProperty = propertyFactory.CreateProperty(claimType, claim.Value);
        logEvent.AddPropertyIfAbsent(tenantIdProperty);
    }
}