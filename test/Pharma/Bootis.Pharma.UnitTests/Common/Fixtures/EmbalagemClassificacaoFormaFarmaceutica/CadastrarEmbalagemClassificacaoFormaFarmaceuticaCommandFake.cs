using Bootis.Catalogo.Application.Requests.EmbalagemClassificacaoFormaFarmaceutica.Cadastrar;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.EmbalagemClassificacaoFormaFarmaceutica;

public static class CadastrarEmbalagemClassificacaoFormaFarmaceuticaCommandFake
{
    public static CadastrarRequest CreateCadastrarEmbalagemClassificacaoFormaFarmaceuticaCommandValido()
    {
        var formasFarmaceuticaId = new List<Guid>
        {
            Uuid.NewSequential()
        };

        return new CadastrarRequest
        {
            FormaFarmaceuticaId = Uuid.NewSequential(),
            EmbalagemClassificacaoId = formasFarmaceuticaId
        };
    }
}
