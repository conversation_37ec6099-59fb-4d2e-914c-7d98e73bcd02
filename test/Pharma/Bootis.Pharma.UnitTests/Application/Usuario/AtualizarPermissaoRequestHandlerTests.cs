using Bootis.Organizacional.Application.Requests.Usuario.Atualizar;
using Bootis.Organizacional.Application.UseCases.Usuario;
using Bootis.Organizacional.Domain.Dtos;
using Bootis.Organizacional.Domain.Statics;
using Bootis.Organizacional.UnitTests.Fixtures;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using DomainAggregate = Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;

namespace Bootis.Organizacional.UnitTests.Application.Usuario;

[Collection(nameof(AccountManagerDomainCollection))]
public class AtualizarPermissaoRequestHandlerTests : BaseTest
{
    private readonly AtualizarPermissaoRequestHandler _handler;
    private readonly Mock<DomainAggregate.IUsuarioRepository> _mockUsuarioRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();


    public AtualizarPermissaoRequestHandlerTests()
    {
        _mockUsuarioRepository = new Mock<DomainAggregate.IUsuarioRepository>();


        _handler = new AtualizarPermissaoRequestHandler(unitOfWork.Object, _mockUsuarioRepository.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AtualizarPermissoesUsuario_ExecutadoComSucesso()
    {
        //Arrange
        var model = UsuarioFakes.GerarUsuarioValidoComPermissao(new List<int>
            { PermissoesStatic.AdministrativoUsuariosVerDetalhes.Id });
        var cmd = new AtualizarPermissaoRequest
        {
            UsuarioId = model.Id,
            Ativo = true,
            Herdado = false,
            PermissaoId = PermissoesStatic.AdministrativoUsuarioEditarPermissao.Id,
            Dependencias = new List<int>(1)
        };

        cmd.Dependencias.Add(PermissoesStatic.AdministrativoUsuariosVerDetalhes.Id);

        _mockUsuarioRepository.Setup(l => l.GetByIdAsync(cmd.UsuarioId)).Returns(
            Task.FromResult(model));

        _mockUsuarioRepository.Setup(l => l.Update(It.IsAny<DomainAggregate.Usuario>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        var actionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.NotNull(actionResult);
        Assert.NotNull(actionResult.Dependencias);
        Assert.Single(actionResult.Dependencias);
        Assert.Equal(cmd.Dependencias.Single(), actionResult.Dependencias.Single().PermissaoId);
        Assert.Equal(actionResult.PermissaoId, cmd.PermissaoId);
        _mockUsuarioRepository.Verify(l => l.Update(It.Is<DomainAggregate.Usuario>(m => m.Permissoes.Any(x =>
                x.Ativo == true && x.PermissaoId == PermissoesStatic.AdministrativoUsuarioEditarPermissao.Id))),
            Times.Once);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AtualizarPermissoesUsuarioComDependencias_ExecutadoComErro()
    {
        //Arrange
        var model = UsuarioFakes.GerarUsuarioValido();
        var cmd = new AtualizarPermissaoRequest
        {
            UsuarioId = model.Id,
            Ativo = true,
            Herdado = false,
            PermissaoId = PermissoesStatic.AdministrativoUsuarioEditarPermissao.Id,
            Dependencias = new List<int>(1)
        };

        _mockUsuarioRepository.Setup(l => l.GetByIdAsync(cmd.UsuarioId)).Returns(
            Task.FromResult(model));

        //Action
        var actionResult = await _handler.Handle(cmd, default);

        //Assert
        Assert.NotNull(actionResult);

        Assert.IsType<PermissaoDependeciaUsuarioDto>(actionResult);

        Assert.Single(actionResult.Dependencias);
        Assert.Equal(PermissoesStatic.AdministrativoUsuariosVerDetalhes.Nome, actionResult.Dependencias.Single().Nome);
        Assert.Equal(PermissoesStatic.AdministrativoUsuarioEditarPermissao.Nome, actionResult.Nome);
    }

    [Fact]
    public async Task AtualizarPermissoesUsuarioNaoExistente_ExecutadoComErro()
    {
        //Arrange
        var cmd = new AtualizarPermissaoRequest();

        _mockUsuarioRepository.Setup(l => l.GetByIdAsync(cmd.UsuarioId)).Returns(
            Task.FromResult<DomainAggregate.Usuario>(default));

        //Action
        var actionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(actionResult);
    }
}
