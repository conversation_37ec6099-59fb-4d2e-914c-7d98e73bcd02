using System.ComponentModel;
using Bootis.Pessoa.Application.Requests.Tipo.Pessoa.Listar;
using Bootis.Pessoa.Domain.Enumerations;
using Bootis.Shared.Common.Extensions;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Tipo.Pessoa;

public class ListarRequestHandler : IRequestHandler<ListarRequest, ListarResponse>
{
    public Task<ListarResponse> Handle(ListarRequest request, CancellationToken cancellationToken)
    {
        return Task.FromResult(Listar());
    }

    public static ListarResponse Listar()
    {
        var listarTipoPessoaList = new ListarResponse();
        listarTipoPessoaList.AddRange(from TipoPessoa tipo in Enum.GetValues(typeof(TipoPessoa))
            select new TipoPessoaResponse { Id = tipo.ToInt(), Tipo = ToDescription(tipo) });

        return listarTipoPessoaList;
    }

    private static string ToDescription<TEnum>(TEnum EnumValue) where TEnum : struct
    {
        return GetEnumDescription((Enum)(object)EnumValue);
    }

    private static string GetEnumDescription(Enum value)
    {
        var fi = value.GetType().GetField(value.ToString());

        if (fi.GetCustomAttributes(typeof(DescriptionAttribute), false) is DescriptionAttribute[] attributes &&
            attributes.Length != 0)
            return attributes.First().Description;

        return value.ToString();
    }
}