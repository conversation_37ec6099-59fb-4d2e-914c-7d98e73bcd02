using System.Data;
using Bootis.Pessoa.Application.Requests.Prescritor.Obter;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Prescritor.Obter;

public class ObterSimplificadoRequestHandler(
    IDbConnection connection)
    : IRequestHandler<ObterSimplificadoRequest, ObterSimplificadoResponse>
{
    public async Task<ObterSimplificadoResponse> Handle(ObterSimplificadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT p.id,
                                  p.nome_completo,
                                  tr.sigla AS sigla_registro,
                                  p.codigo_registro,
                                  es.abreviacao AS abreviacao_uf_registro
                           FROM prescritores p
                                LEFT JOIN estados es ON es.id = p.uf_registro_id 
                                LEFT JOIN tipos_registro tr ON tr.id = p.tipo_registro_id 
                           WHERE p.id = @prescritorId;

                           SELECT ep.id AS especialidade_id,
                                  ep.descricao
                           FROM especialidades_prescritor ep
                                LEFT JOIN prescritores_especialidades pe ON pe.especialidade_id = ep.id
                                LEFT JOIN prescritores p ON p.id = pe.prescritor_id
                           WHERE p.id = @prescritorId;

                           SELECT pc.id AS contato_id,
                                  pc.tipo_contato_id,
                                  pc.identificacao AS contato_identificacao,
                                  tc.icon AS tipo_contato_icon
                           FROM prescritor_contatos pc
                                LEFT JOIN prescritores p ON p.id = pc.prescritor_id 
                                LEFT JOIN tipos_contato tc ON tc.id = pc.tipo_contato_id 
                           WHERE p.id = @prescritorId
                             AND pc.principal = true;
                           """;

        await using var query =
            await connection.QueryMultipleAsync(sql, new { prescritorId = request.Id });
        var prescritor = await query.ReadFirstOrDefaultAsync<ObterSimplificadoResponse>();

        if (prescritor == null)
            throw new DomainException(Localizer.Instance.GetMessage_Prescritor_GuidNaoEncontrado(request.Id));

        prescritor.Especialidades = await query.ReadAsync<ObterResponse.PrescritorEspecialidadeResponse>();
        prescritor.ContatoPrincipal = await query.ReadAsync<ObterResponse.PrescritorContatoResponse>();

        return prescritor;
    }
}