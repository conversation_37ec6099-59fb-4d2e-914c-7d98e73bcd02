using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bootis.Pharma.Api.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddMotivoReceitaHistorico : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "motivo_cancelamento",
                table: "receitas_manipuladas_historico",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "motivo_cancelamento",
                table: "receitas_manipuladas_historico");
        }
    }
}
