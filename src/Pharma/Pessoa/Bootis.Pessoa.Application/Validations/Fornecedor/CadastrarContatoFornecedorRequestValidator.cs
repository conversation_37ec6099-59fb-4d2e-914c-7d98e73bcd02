using Bootis.Pessoa.Application.Requests.Fornecedor.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.Fornecedor;

public class CadastrarContatoFornecedorRequestValidator : AbstractValidator<CadastrarContatoRequest>
{
    public CadastrarContatoFornecedorRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.TipoContatoId)
            .NotEmpty()
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.TipoContatoId)));

        RuleFor(c => c.Identificacao)
            .NotEmpty()
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Identificacao)));

        RuleFor(c => c.Observacao)
            .MaximumLength(1000)
            .WithMessage(f => localizer.GetMessage_Validation_TamanhoMaximo(f.Observacao, 1000));
    }
}