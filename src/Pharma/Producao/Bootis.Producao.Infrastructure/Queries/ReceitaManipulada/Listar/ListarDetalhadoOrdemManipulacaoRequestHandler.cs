using System.Data;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Listar;

public class ListarDetalhadoOrdemManipulacaoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoOrdemManipulacaoRequest, PaginatedResult<ListarDetalhadoOrdemManipulacaoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoOrdemManipulacaoResponse>> Handle(
        ListarDetalhadoOrdemManipulacaoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                            SELECT rm.id AS receita_manipulada_id,
                                   rm.previsao_entrega AS data_entrega,
                                   rm.sequencia_group_tenant AS receita_numero,
                                   at.id AS atendimento_id,
                                   pvi.descricao AS descricao,
                                   pv.sequencia_group_tenant AS pedido_venda_numero,
                                   pv.id AS pedido_venda_id,
                                   pv.status AS pedido_venda_status_id,
                                   ca.icon AS canal_atendimento_icon,
                                   cl.nome AS paciente_nome_completo,
                                   cl.id AS paciente_id,
                                   mom.descricao AS modelo_ordem_manipulacao_descricao,
                                   mom.id AS modelo_ordem_manipulacao_id,
                                   rm.previsao_entrega AS date_entrega,
                                   rm.status_impressao_ordem_manipulacao AS status_impressao_id
                              FROM receitas_manipuladas rm
                                   LEFT JOIN formas_farmaceutica ff ON
                                             ff.id = rm.forma_farmaceutica_id
                                   LEFT JOIN clientes cl ON
                                             cl.id = rm.paciente_id
                                   LEFT JOIN pedidos_venda_itens_receita_manipulada pvir ON
                                             pvir.receita_manipulada_id = rm.id
                                   LEFT JOIN pedidos_venda_itens pvi ON
                                             pvi.id = pvir.id
                                   LEFT JOIN pedidos_venda pv ON
                                             pv.id = pvi.pedido_venda_id
                                   LEFT JOIN modelos_ordem_manipulacao mom ON
                                             mom.id = rm.modelo_ordem_manipulacao_id
                                   LEFT JOIN laboratorios l ON
                                             l.id = ff.laboratorio_id
                                   LEFT JOIN atendimentos at ON
                                             at.id = pv.atendimento_id
                                   LEFT JOIN canais_atendimento ca ON
                                             ca.id = at.canal_atendimento_id
                             WHERE pv.group_tenant_id = @GroupTenantId
                               AND pv.id IS NOT NULL
                                   !@SEARCH_CONDITION@!
                           """;

        var searchNumeroReceita = new NumberSearchField
        {
            Field = "rm.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        var searchDescricaoReceita = new StringSearchField
        {
            Field = "pvi.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchNumeroPedidoVenda = new NumberSearchField
        {
            Field = "pv.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        var searchPacienteNome = new StringSearchField
        {
            Field = "cl.nome",
            CompareType = StringCompareType.Contains
        };

        var searchModeloOrdemManipulacao = new StringSearchField
        {
            Field = "mom.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchDataEntrega = new DateSearchField
        {
            Field = "rm.previsao_entrega",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchDataEmissao = new DateSearchField
        {
            Field = "rm.data_emissao",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var conditionalReceitaManipulada = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "rm.id = ANY ( @ReceitaManipuladaIds )",
            Predicate = filter => filter.ReceitaManipuladaIds != null
        };

        var conditionalDescricaoReceita = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "pvi.descricao ILIKE @ReceitaManipuladaDescricao",
            Predicate = filter => !string.IsNullOrEmpty(filter.ReceitaManipuladaDescricao)
        };

        var conditionalPedidoVenda = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "pv.id = ANY ( @PedidoVendaIds )",
            Predicate = filter => filter.PedidoVendaIds != null
        };

        var conditionalLaboratorio = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "l.id = ANY ( @LaboratorioIds )",
            Predicate = filter => filter.LaboratorioIds != null
        };

        var conditionalPaciente = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "cl.id = ANY ( @PacienteIds )",
            Predicate = filter => filter.PacienteIds != null
        };

        var conditionalModeloOrdemManipulacao = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "mom.id = ANY ( @ModeloOrdemManipulacaoId )",
            Predicate = filter => filter.ModeloOrdemManipulacaoId != null
        };

        var conditionalDataEntrega = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "rm.previsao_entrega = @DataEntrega",
            Predicate = filter => filter.DataEntrega.HasValue
        };

        var conditionalDataEmissao = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "rm.data_emissao = @DataEmissaoReceita",
            Predicate = filter => filter.DataEmissaoReceita.HasValue
        };

        var conditionalStatusReceita = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "rm.status = ANY ( @StatusReceitaIds )",
            Predicate = filter => filter.StatusReceitaIds != null
        };

        var conditionalStatusImpressao = new ConditionalFilterField<ListarDetalhadoOrdemManipulacaoRequest>
        {
            Filter = "rm.status_impressao_ordem_manipulacao = ANY ( @StatusImpressaoIds )",
            Predicate = filter => filter.StatusImpressaoIds != null
        };

        return PaginatedQueryBuilder<ListarDetalhadoOrdemManipulacaoRequest, ListarDetalhadoOrdemManipulacaoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchNumeroReceita)
            .AddSearchField(searchDescricaoReceita)
            .AddSearchField(searchNumeroPedidoVenda)
            .AddSearchField(searchPacienteNome)
            .AddSearchField(searchModeloOrdemManipulacao)
            .AddSearchField(searchDataEntrega)
            .AddSearchField(searchDataEmissao)
            .AddFilter(conditionalReceitaManipulada)
            .AddFilter(conditionalDescricaoReceita)
            .AddFilter(conditionalPedidoVenda)
            .AddFilter(conditionalLaboratorio)
            .AddFilter(conditionalPaciente)
            .AddFilter(conditionalModeloOrdemManipulacao)
            .AddFilter(conditionalDataEntrega)
            .AddFilter(conditionalDataEmissao)
            .AddFilter(conditionalStatusReceita)
            .AddFilter(conditionalStatusImpressao)
            .ExecuteAsync();
    }
}