using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class PerdaEntityTypeConfiguration : BaseEntityTypeConfiguration<Perda>
{
    public override void Configure(EntityTypeBuilder<Perda> builder)
    {
        builder.ToTable("perdas");

        builder
            .Property(c => c.DataPerda)
            .Data()
            .IsRequired();

        builder
            .Property(c => c.DataLancamento)
            .DataHora()
            .IsRequired();

        builder.HasOne(c => c.Lote)
            .WithMany()
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(c => c.LocalEstoque)
            .WithMany()
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.Quantidade)
            .EstoquePrecisao()
            .IsRequired();

        builder
            .Property(c => c.UnidadeMedidaId)
            .IsRequired();

        builder.HasOne(c => c.MotivoPerda)
            .WithMany(c => c.LancamentosPerda)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.Observacao)
            .Observacao(TamanhoTexto.Mil);

        builder.HasOne(c => c.Produto)
            .WithMany()
            .HasForeignKey(x => x.ProdutoId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.Usuario)
            .WithMany()
            .HasForeignKey(x => x.UsuarioId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.MovimentoEstoque)
            .WithMany(c => c.LancamentosPerda)
            .HasForeignKey(x => x.MovimentoEstoqueId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        base.Configure(builder);
    }
}