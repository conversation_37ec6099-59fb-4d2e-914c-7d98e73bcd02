using System.Reflection;
using Bootis.Setup.Api.Attributes;

namespace Bootis.Setup.Api.Extensions;

public static class TypeExtensions
{
    public static string GetTableName(this Type type)
    {
        var tableNameAttribute = type.GetCustomAttribute<TableNameAttribute>();
        return tableNameAttribute?.Name ?? type.Name.ToLower();
    }

    public static string GetColumnName(this PropertyInfo property)
    {
        var columnName = property.GetCustomAttribute<ColumnNameAttribute>()?.Name;

        return !string.IsNullOrEmpty(columnName) ? columnName : ConvertToSnakeCase(property.Name);
    }

    private static string ConvertToSnakeCase(string input)
    {
        return string.Concat(
            input.Select((x, i) =>
                i > 0 && char.IsUpper(x) &&
                (!char.IsUpper(input[i - 1]) || (i < input.Length - 1 && char.IsLower(input[i + 1])))
                    ? "_" + x.ToString().ToLower()
                    : x.ToString().ToLower()
            )
        );
    }
}