using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoExcipienteAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Estoque.Domain.AggregatesModel.ProdutoLoteEmUsoAggregate;
using Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public interface IReceitaManipuladaRepository : IRepository<ReceitaManipulada>, IScopedService
{
    Task<FormulaPadrao> ObterFormulaPadraoPorProdutoIdAsync(Guid produtoId);
    Task<ProdutoExcipiente> ObterPorClasseBiofarmaceuticaAsync(TipoClasseBiofarmaceutica classeBiofarmaceutica);
    Task<ProdutoTipoCapsula> ObterProdutoTipoCapsulaPorProdutoIdAsync(Guid produtoId);
    Task<ProdutoEmbalagem> ObterProdutoEmbalagemPorIdAsync(Guid? id);
    Task<Produto> ObterProdutoIncludeAssociadoPorIdAsync(Guid id, Guid formaFarmaceuticaId);
    Task<ReceitaManipulada> ObterReceitaManipuladaPorIdAsync(Guid id);
    Task<ReceitaManipulada> ObterReceitaManipuladaSemTrackingPorIdAsync(Guid id);
    Task<ReceitaManipulada> ObterReceitaManipuladaSemIncludesPorIdAsync(Guid id);
    Task<List<ReceitaManipulada>> ObterReceitasManipuladasAsync(IEnumerable<Guid> receitaIds);
    Task<ProdutoLoteEmUso> ObterPorProdutoELoteAsync(Guid produtoId, Guid loteId);
    Task<Produto> ObterProdutoPorIdAsync(Guid id);
    Task<ProdutoSinonimoFatoresDto> ObterFatoresSinonimoAsync(Guid produtoOrigemId, Guid produtoSinonimoId);
    Task<List<ProdutoEmbalagem>> ObterTodasEmbalagensDisponiveisAsync();
    Task RemoverRastreiosPorReceitaId(Guid receitaId);
    Task RemoverCalculosPorReceitaId(Guid receitaId);
    Task RemoverBaseCalculoPorReceitaId(Guid receitaId);
    Task RemoverValoresPorReceitaId(Guid receitaId);
}