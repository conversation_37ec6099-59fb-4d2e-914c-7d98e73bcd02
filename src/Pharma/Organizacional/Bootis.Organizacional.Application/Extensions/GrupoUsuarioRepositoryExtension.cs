using Bootis.Organizacional.Application.Requests.Grupo.Remover;
using Bootis.Organizacional.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Organizacional.Application.Extensions;

public static class GrupoUsuarioRepositoryExtension
{
    public static async Task<Grupo> ObterGrupoUsuarioAsync(this IGrupoRepository repository, Guid id)
    {
        var grupo = await repository.ObterPorIdAsync(id);

        if (grupo == null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Grupo_GuidNaoEncontrado(id));

        return grupo;
    }

    public static async Task<List<Grupo>> ObterGruposAsync(this IGrupoRepository repository,
        IEnumerable<Guid> ids)
    {
        var grupos = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !grupos.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_Grupo_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return grupos;
    }

    public static async Task VerificarDependeciasGruposAsync(this IGrupoRepository repository, List<Grupo> grupos)
    {
        var removeResponses = new List<RemoverResponse>();
        var ids = grupos.Select(c => c.Id).ToList();
        var gruposDependentes = await repository.VerificarDependenciaAsync(ids);

        removeResponses.AddRange(gruposDependentes.Select(e => new RemoverResponse
        {
            Id = e.Id,
            Descricao = e.Nome
        }));

        if (removeResponses.Count > 0)
            throw new DomainException(nameof(GlobalErrorCode), (int)GlobalErrorCode.Global_Remover,
                removeResponses.ToArray());
    }
}