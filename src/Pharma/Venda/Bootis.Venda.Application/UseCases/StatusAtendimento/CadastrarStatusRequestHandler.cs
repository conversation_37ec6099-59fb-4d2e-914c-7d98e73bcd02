using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Venda.Application.Extensions;
using Bootis.Venda.Application.Requests.Atendimento.Cadastrar;
using Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate;
using MediatR;

namespace Bootis.Venda.Application.UseCases.StatusAtendimento;

public class CadastrarStatusRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IStatusAtendimentoRepository statusAtendimentoRepository,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<CadastrarStatusAtendimentoRequest>
{
    public async Task Handle(CadastrarStatusAtendimentoRequest request, CancellationToken cancellationToken)
    {
        await statusAtendimentoRepository.ValidarDescricaoStatusAtendimentoAsync(request.Descricao);

        var vendedor = await usuarioRepository.ObterUsuarioAsync(userContext.UserId);

        var statusAtendimento =
            new Domain.AggregatesModel.StatusAtendimentoAggregate.StatusAtendimento(request.Descricao, request.Ativo,
                request.Ordem, vendedor);

        statusAtendimentoRepository.Add(statusAtendimento);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}