using Bootis.Shared.Api.Components;

namespace Bootis.Shared.Api.Extensions;

internal static class ScalarIndexPageBuilder
{
    public static string BuildIndexPage(List<ApiVersionInfo> versions)
    {
        return $$"""
                 <!DOCTYPE html>
                 <html lang='pt-BR'>
                 <head>
                     <meta charset='UTF-8'>
                     <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                     <title>Bootis API Documentation</title>
                     <link rel='icon' type='image/svg+xml' href='{{BootisLogo.GetFaviconDataUri()}}'>
                     <style>
                         * { 
                             margin: 0; 
                             padding: 0; 
                             box-sizing: border-box; 
                         }
                         
                         :root {
                             --primary: #818cf8;
                             --primary-dark: #6366f1;
                             --primary-light: #a5b4fc;
                             --secondary: #22d3ee;
                             --warning: #fbbf24;
                             --success: #34d399;
                             --danger: #f87171;
                             --text-primary: #f3f4f6;
                             --text-secondary: #9ca3af;
                             --bg-primary: #0f172a;
                             --bg-secondary: #1e293b;
                             --bg-tertiary: #334155;
                             --border-color: #334155;
                             --shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
                         }
                         
                         html {
                             height: 100%;
                         }
                         
                         body {
                             font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                             background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
                             min-height: 100vh;
                             padding: 20px;
                             position: relative;
                             overflow-x: hidden;
                         }
                         
                         body::before {
                             content: '';
                             position: fixed;
                             width: 100%;
                             height: 100%;
                             background: url('data:image/svg+xml,<svg width="60" height="60" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"><path d="M 60 0 L 0 0 0 60" fill="none" stroke="%23818cf8" stroke-width="0.5" opacity="0.05"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
                             top: 0;
                             left: 0;
                             animation: float 30s infinite linear;
                             z-index: -1;
                         }
                         
                         @keyframes float {
                             0% { transform: translate(0, 0); }
                             100% { transform: translate(30px, 30px); }
                         }
                         
                         .container {
                             background: var(--bg-primary);
                             border: 1px solid var(--border-color);
                             border-radius: 24px;
                             box-shadow: var(--shadow);
                             padding: 48px;
                             max-width: 1400px;
                             width: 100%;
                             margin: 0 auto;
                             position: relative;
                             z-index: 1;
                         }
                         
                         .header {
                             text-align: center;
                             margin-bottom: 48px;
                         }
                         
                         .logo {
                             width: 80px;
                             height: 80px;
                             border-radius: 20px;
                             margin: 0 auto 24px;
                             overflow: hidden;
                             box-shadow: 0 10px 20px -5px rgba(129, 140, 248, 0.3);
                             background: linear-gradient(135deg, #6366f1, #4f46e5);
                             display: flex;
                             align-items: center;
                             justify-content: center;
                         }
                         
                         .logo svg {
                             width: 80px;
                             height: 80px;
                         }
                         
                         h1 {
                             font-size: 2.5rem;
                             font-weight: 800;
                             margin-bottom: 12px;
                             background: linear-gradient(135deg, var(--primary), var(--primary-light));
                             -webkit-background-clip: text;
                             -webkit-text-fill-color: transparent;
                             background-clip: text;
                         }
                         
                         .subtitle {
                             color: var(--text-secondary);
                             font-size: 1.125rem;
                             line-height: 1.75;
                         }
                         
                         .versions-section {
                             margin-bottom: 40px;
                         }
                         
                         .section-title {
                             font-size: 1.25rem;
                             font-weight: 700;
                             color: var(--text-primary);
                             margin-bottom: 24px;
                             display: flex;
                             align-items: center;
                             gap: 12px;
                         }
                         
                         .versions-grid {
                             display: grid;
                             grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                             gap: 20px;
                         }
                         
                         .version-card {
                             position: relative;
                             background: var(--bg-secondary);
                             border: 1px solid var(--border-color);
                             border-radius: 16px;
                             padding: 24px;
                             text-decoration: none;
                             transition: all 0.3s ease;
                             display: flex;
                             flex-direction: column;
                             align-items: center;
                             justify-content: center;
                             text-align: center;
                             overflow: hidden;
                             min-height: 120px;
                             cursor: pointer;
                             word-break: break-word;
                             hyphens: auto;
                         }
                         
                         .version-card::before {
                             content: '';
                             position: absolute;
                             top: 0;
                             left: 0;
                             right: 0;
                             height: 3px;
                             background: linear-gradient(90deg, var(--primary), var(--primary-light));
                             transform: scaleX(0);
                             transition: transform 0.3s ease;
                         }
                         
                         .version-card:hover {
                             border-color: var(--primary);
                             transform: translateY(-4px);
                             box-shadow: 0 10px 20px -5px rgba(129, 140, 248, 0.3);
                             background: var(--bg-tertiary);
                         }
                         
                         .version-card:hover::before {
                             transform: scaleX(1);
                         }
                         
                         .version-main {
                             font-size: 1.4rem;
                             font-weight: 700;
                             color: var(--primary);
                             margin-bottom: 4px;
                             line-height: 1.2;
                             word-wrap: break-word;
                             overflow-wrap: break-word;
                             max-width: 100%;
                         }
                         
                         .version-sub {
                             font-size: 0.95rem;
                             font-weight: 500;
                             color: var(--text-secondary);
                             word-wrap: break-word;
                             overflow-wrap: break-word;
                             max-width: 100%;
                         }
                         
                         .version-single {
                             font-size: 1.4rem;
                             font-weight: 700;
                             color: var(--primary);
                             word-wrap: break-word;
                             overflow-wrap: break-word;
                             max-width: 100%;
                         }
                         
                         .version-deprecated {
                             position: absolute;
                             top: 8px;
                             right: 8px;
                             background: var(--warning);
                             color: var(--bg-primary);
                             padding: 2px 8px;
                             border-radius: 6px;
                             font-size: 0.7rem;
                             font-weight: 600;
                         }
                         
                         .swagger-option {
                             background: var(--bg-secondary);
                             border: 1px solid var(--border-color);
                             border-radius: 16px;
                             padding: 24px;
                             margin-top: 32px;
                         }
                         
                         .swagger-content {
                             display: flex;
                             align-items: center;
                             justify-content: center;
                             gap: 20px;
                             flex-wrap: wrap;
                         }
                         
                         .swagger-label {
                             color: var(--text-secondary);
                             font-size: 1rem;
                         }
                         
                         .swagger-button {
                             background: linear-gradient(135deg, #34d399, #10b981);
                             color: white;
                             padding: 10px 20px;
                             border-radius: 12px;
                             text-decoration: none;
                             font-weight: 600;
                             display: inline-flex;
                             align-items: center;
                             gap: 8px;
                             transition: all 0.3s ease;
                             box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.2);
                         }
                         
                         .swagger-button:hover {
                             transform: translateY(-2px);
                             box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.3);
                             background: linear-gradient(135deg, #10b981, #059669);
                         }
                         
                         .footer {
                             text-align: center;
                             margin-top: 32px;
                             padding-top: 24px;
                             border-top: 1px solid var(--border-color);
                             color: var(--text-secondary);
                             font-size: 0.875rem;
                         }
                         
                         .footer-links {
                             display: flex;
                             justify-content: center;
                             gap: 24px;
                             margin-top: 12px;
                         }
                         
                         .footer-link {
                             color: var(--primary);
                             text-decoration: none;
                             transition: color 0.3s ease;
                         }
                         
                         .footer-link:hover {
                             color: var(--primary-light);
                         }
                         
                         @media (max-width: 768px) {
                             .container {
                                 padding: 24px;
                             }
                             
                             h1 {
                                 font-size: 2rem;
                             }
                             
                             .versions-grid {
                                 grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                             }
                             
                             .swagger-content {
                                 flex-direction: column;
                                 gap: 12px;
                             }
                         }
                         
                         @media (max-width: 480px) {
                             body {
                                 padding: 10px;
                             }
                             
                             .container {
                                 padding: 20px;
                             }
                             
                             .versions-grid {
                                 grid-template-columns: 1fr;
                             }
                         }
                     </style>
                 </head>
                 <body>
                     <div class='container'>
                         <div class='header'>
                             <div class='logo'>
                                 {{BootisLogo.GetLargeSvg()}}
                             </div>
                             <h1>Bootis API Documentation</h1>
                             <p class='subtitle'>
                                 Explore nossa API RESTful com documentação interativa<br>
                                 Escolha o grupo e versão da API para começar
                             </p>
                         </div>
                         
                         <div class='versions-section'>
                             <h2 class='section-title'>
                                 <span>🚀</span>
                                 <span>APIs Disponíveis</span>
                             </h2>
                             <div class='versions-grid'>
                                 {{string.Join("", versions.Select(v => {
                                     var parts = v.Version.Split('-');
                                     var hasSubVersion = parts.Length > 1;

                                     return $@"
                                 <a href='scalar/{v.GroupName}' class='version-card'>
                                     {(v.IsDeprecated ? "<span class='version-deprecated'>Deprecada</span>" : "")}
                                     {(hasSubVersion ?
                                             $@"<div class='version-main'>{parts[0]}</div>
                                         <div class='version-sub'>{string.Join("-", parts.Skip(1))}</div>" :
                                             $@"<div class='version-single'>{v.Version}</div>"
                                         )}
                                 </a>";
                                 }))}}
                             </div>
                         </div>
                         
                         <div class='swagger-option'>
                             <div class='swagger-content'>
                                 <span class='swagger-label'>Prefere a interface clássica?</span>
                                 <a href='swagger' class='swagger-button'>
                                     <span>📄</span>
                                     <span>Abrir Swagger UI</span>
                                 </a>
                             </div>
                         </div>
                         
                         <div class='footer'>
                             <p>Powered by Bootis Platform © {{DateTime.UtcNow.Year}}</p>
                             <div class='footer-links'>
                                 <a href='https://dev.azure.com/bootiscloud/Bootis/_git/Backend' class='footer-link' target='_blank'>Azure DevOps</a>
                                 <a href='/health' class='footer-link'>Health Check</a>
                                 <a href='/metrics' class='footer-link'>Metrics</a>
                             </div>
                         </div>
                     </div>
                 </body>
                 </html>
                 """;
    }

    public class ApiVersionInfo
    {
        public string GroupName { get; set; }
        public string Version { get; set; }
        public bool IsDeprecated { get; set; }
    }
}