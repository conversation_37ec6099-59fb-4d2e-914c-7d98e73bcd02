using System.ComponentModel.DataAnnotations;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Catalogo.Application.Requests.UnidadeMedida;

public class UnidadeMedida
{
    [Key] public string Abreviacao { get; set; }

    public string Descricao { get; set; }

    public bool Ativo { get; set; }

    public bool UnidadeAlternativa { get; set; }

    public TipoUnidade TipoUnidade { get; set; }
}