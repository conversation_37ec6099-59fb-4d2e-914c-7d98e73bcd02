using Asp.Versioning;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Atualizar;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Cadastrar;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Listar;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Obter;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Remover;
using Bootis.Shared.Api.Attributes;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Query;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Catalogo.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Catalogo")]
[Route("catalogo/v{version:apiVersion}/[controller]")]
public class ProdutoAssociadoController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_ProdutosAssociado_Cadastrar)]
    public async Task<IActionResult> Cadastrar(CadastrarRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_ProdutosAssociado_EditarDetalhes)]
    public async Task<IActionResult> Atualizar(AtualizarRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpDelete]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_ProdutosAssociado_Excluir)]
    public async Task<IActionResult> Remover([FromBody] RemoverRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_ProdutosAssociado_Visualizar)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var result = await mediator.Send(new ObterRequest { Id = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_InformacoesTecnicas_Visualizar)]
    public async Task<IActionResult> ListarDetalhado([FromQuery] ListarDetalhadoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(PaginatedResult<ListarResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_InformacoesTecnicas_Visualizar)]
    public async Task<IActionResult> Listar([FromQuery] ListarRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarFormasFarmaceuticas")]
    [ProducesResponseType(typeof(PaginatedResult<ListarFormasFarmaceuticasResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_InformacoesTecnicas_Visualizar)]
    public async Task<IActionResult> Listar([FromQuery] ListarFormasFarmaceuticasRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }
}