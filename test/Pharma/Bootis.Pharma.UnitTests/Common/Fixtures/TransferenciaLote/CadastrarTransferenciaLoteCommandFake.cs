using Bootis.Estoque.Application.Requests.TransferenciaLote.Cadastrar;
using Bootis.Estoque.Domain.Dtos.TransferenciaLote;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.TransferenciaLote;

public class CadastrarTransferenciaLoteCommandFake
{
    public static CadastrarRequest CreateCommandValido()
    {
        var trasferenciaLotesItens = new[]
        {
            new TransferenciaLoteItensDto
            {
                LoteId = Guid.Empty,
                ProdutoId = Guid.Empty,
                QuantidadeTransferida = 10,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
            }
        };

        return new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = Uuid.NewSequential(),
            LocalDeEstoqueOrigemId = Uuid.NewSequential(),
            Observacao = "Teste",
            TransferenciaLoteItens = trasferenciaLotesItens
        };
    }

    public static CadastrarRequest CadastrarTransferenciaLotesCommandValido()
    {
        var trasferenciaLotesItens = new[]
        {
            new TransferenciaLoteItensDto
            {
                LoteId = Guid.Empty,
                ProdutoId = Guid.Empty,
                QuantidadeTransferida = 10,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
            }
        };

        return new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = Uuid.NewSequential(),
            LocalDeEstoqueOrigemId = Uuid.NewSequential(),
            Observacao = "Teste",
            TransferenciaLoteItens = trasferenciaLotesItens
        };
    }

    public static CadastrarRequest CreateCommandValido_LocaisEstoqueIguais()
    {
        return new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = new Guid("5b98e5e1-44f4-489c-9371-ab819597a04a"),
            LocalDeEstoqueOrigemId = new Guid("5b98e5e1-44f4-489c-9371-ab819597a04a"),
            Observacao = "Teste"
        };
    }

    public static CadastrarRequest CadastrarTransferenciaLotesCommandValido_QuantidadeMaiorQueSaldoEstoque()
    {
        var trasferenciaLotesItens = new[]
        {
            new TransferenciaLoteItensDto
            {
                LoteId = Guid.Empty,
                ProdutoId = Guid.Empty,
                QuantidadeTransferida = 15,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.kg
            }
        };

        return new CadastrarRequest
        {
            LocalDeEstoqueDestinoId = Uuid.NewSequential(),
            LocalDeEstoqueOrigemId = Uuid.NewSequential(),
            Observacao = "Teste",
            TransferenciaLoteItens = trasferenciaLotesItens
        };
    }
}
