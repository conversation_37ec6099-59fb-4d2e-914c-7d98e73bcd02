using System.Data;
using Bootis.Catalogo.Application.Requests.EmbalagemClassificacao.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.EmbalagemClassificacao.Listar;

public class
    ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                                EC.descricao,
                                EC.id,
                                EC.ativo,
                                (SELECT COUNT(PE.produto_id)
                                    FROM produtos_embalagem PE
                                    WHERE PE.embalagem_classificacao_id = EC.id) AS produtos_vinculados
                           FROM embalagens_classificacao EC
                           WHERE EC.group_tenant_id = @GroupTenantId 
                                 !@SEARCH_CONDITION@!
                           """;

        var searchDescricao = new StringSearchField
        {
            Field = "EC.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricao)
            .ExecuteAsync();
    }
}