using Bootis.Identity.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Identity.Infrastructure.Persistence.EntityConfigurations;

public class TemporaryLoginEntityTypeConfiguration : IEntityTypeConfiguration<TemporaryLogin>
{
    public void Configure(EntityTypeBuilder<TemporaryLogin> builder)
    {
        builder.ToTable("temporary_logins");

        builder.HasKey(t => t.Id);
    }
}