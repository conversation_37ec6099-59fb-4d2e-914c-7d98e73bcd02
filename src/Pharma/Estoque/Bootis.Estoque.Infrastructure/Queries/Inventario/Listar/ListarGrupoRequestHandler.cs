using System.Data;
using Bootis.Estoque.Application.Requests.Inventario.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Inventario.Listar;

public class ListarGrupoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarGrupoRequest, PaginatedResult<ListarGrupoResponse>>
{
    public Task<PaginatedResult<ListarGrupoResponse>> Handle(ListarGrupoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT DISTINCT 
                               gru.descricao AS grupo_descricao,
                               gru.id AS grupo_id
                             FROM produto_grupos gru
                                  LEFT JOIN sub_grupos sub ON sub.grupo_id = gru.id
                                  LEFT JOIN produtos prod ON prod.sub_grupo_id = sub.id
                                  LEFT JOIN saldos_estoque se ON se.produto_id = prod.id
                                  LEFT JOIN locais_estoque lc ON lc.id = se.local_estoque_id
                            WHERE gru.group_tenant_id = @GroupTenantId
                                                            !@SEARCH_CONDITION@!
                           """;

        var searchDescricao = new StringSearchField
        {
            Field = "GRU.descricao",
            CompareType = StringCompareType.Contains
        };

        var filterLocalEstoqueId = new ConditionalFilterField<ListarGrupoRequest>
        {
            Predicate = filter => filter.LocalEstoqueId is not null,
            Filter = "LC.id = @LocalEstoqueId"
        };

        return PaginatedQueryBuilder<ListarGrupoRequest, ListarGrupoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricao)
            .AddFilter(filterLocalEstoqueId)
            .ExecuteAsync();
    }
}