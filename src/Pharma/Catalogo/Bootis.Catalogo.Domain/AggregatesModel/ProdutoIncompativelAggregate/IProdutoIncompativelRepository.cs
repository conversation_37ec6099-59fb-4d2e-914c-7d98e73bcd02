using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;

public interface IProdutoIncompativelRepository : IRepository<ProdutoIncompativel>, IScopedService
{
    Task<ProdutoIncompativel> ObterPorIdAsync(Guid id);
    Task<bool> ValidarVinculoPorProdutoAsync(Guid produtoId, Guid produtoIncompativelId);
    Task<bool> ValidarVinculoPorIdEProdutoAsync(Guid id, Guid produtoId, Guid produtoIncompativelId);
    Task<IEnumerable<ProdutoIncompativel>> ObterTodasIncompatibilidadesEntreAsync(IEnumerable<Guid> produtoIds);
}