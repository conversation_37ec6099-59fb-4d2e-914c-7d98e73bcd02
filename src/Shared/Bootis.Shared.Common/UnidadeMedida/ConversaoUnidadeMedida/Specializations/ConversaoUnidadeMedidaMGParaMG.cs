using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida.Specializations;

public class ConversaoUnidadeMedidaMGParaMG : ConversaoUnidadeMedida
{
    public override UnidadeMedidaAbreviacao UnidadeMedidaOrigem => UnidadeMedidaAbreviacao.mg;
    public override UnidadeMedidaAbreviacao UnidadeMedidaConversao => UnidadeMedidaAbreviacao.mg;
    public override decimal TaxaConversao => 1;
    public override TipoCalculoDensidade TipoCalculoDensidade => TipoCalculoDensidade.Nenhum;
}