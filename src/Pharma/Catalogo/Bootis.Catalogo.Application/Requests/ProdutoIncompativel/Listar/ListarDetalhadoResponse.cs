using System.ComponentModel;
using Bootis.Catalogo.Domain.Enumerations;

namespace Bootis.Catalogo.Application.Requests.ProdutoIncompativel.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public Guid ProdutoIncompativelId { get; set; }
    public string ProdutoIncompativelDescricao { get; set; }
    public NivelIncompatibilidade NivelIncompatibilidade { get; set; }

    public string NivelIncompatibilidadeDescricao
    {
        get
        {
            var descriptionAttribute = typeof(NivelIncompatibilidade)
                    .GetField(NivelIncompatibilidade.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : NivelIncompatibilidade.ToString();
        }
    }
}