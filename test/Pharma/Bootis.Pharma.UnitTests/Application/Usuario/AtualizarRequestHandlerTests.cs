using Bootis.Organizacional.Application.Requests.Usuario.Atualizar;
using Bootis.Organizacional.Application.UseCases.Usuario;
using Bootis.Organizacional.Domain.Dtos;
using Bootis.Organizacional.UnitTests.Fixtures;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using DomainAggregate = Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;

namespace Bootis.Organizacional.UnitTests.Application.Usuario;

[Collection(nameof(AccountManagerDomainCollection))]
public class AtualizarRequestHandlerTests : BaseTest
{
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<DomainAggregate.IUsuarioRepository> _mockUsuarioRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();


    public AtualizarRequestHandlerTests()
    {
        _mockUsuarioRepository = new Mock<DomainAggregate.IUsuarioRepository>();

        _handler = new AtualizarRequestHandler(unitOfWork.Object, _mockUsuarioRepository.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task AtualizarUsuario_ExecutadoComSucesso()
    {
        //Arrange
        var model = UsuarioFakes.GerarUsuarioValido();

        var cmd = new AtualizarRequest
        {
            Nome = "Teste 123",
            Id = model.Id,
            Preferencias = new PreferenciaDto()
        };

        _mockUsuarioRepository.Setup(l => l.GetByIdAsync(cmd.Id)).Returns(
            Task.FromResult(model));

        _mockUsuarioRepository.Setup(l => l.Update(It.IsAny<DomainAggregate.Usuario>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _mockUsuarioRepository.Verify(l => l.Update(It.Is<DomainAggregate.Usuario>(m => m.Nome == cmd.Nome)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarUsuarioNaoExistente_ExecutadoComErro()
    {
        //Arrange
        var cmd = new AtualizarRequest();

        _mockUsuarioRepository.Setup(l => l.GetByIdAsync(cmd.Id)).Returns(
            Task.FromResult<DomainAggregate.Usuario>(default));

        //Action
        var actionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(actionResult);
    }
}
