namespace Bootis.Estoque.Application.Requests.TransferenciaLote.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public int SequenciaNumeroTransferencia { get; set; }
    public DateTime DataTransferencia { get; set; }
    public Guid LocalEstoqueOrigemId { get; set; }
    public string LocalEstoqueOrigemDescricao { get; set; }
    public string LocalEstoqueOrigemEmpresa { get; set; }
    public Guid LocalEstoqueDestinoId { get; set; }
    public string LocalEstoqueDestinoDescricao { get; set; }
    public string LocalEstoqueDestinoEmpresa { get; set; }
    public string UsuarioNome { get; set; }
    public string UsuarioSobrenome { get; set; }
}