using Bootis.Setup.Api.RequestHandlers.Estoque;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Setup.Api.Infrastructure.EntityConfigurations.Estoque;

public class LoteInformacaoTecnicaConfiguration : IEntityTypeConfiguration<LoteInformacaoTecnica>
{
    public void Configure(EntityTypeBuilder<LoteInformacaoTecnica> builder)
    {
        builder.ToTable("lotes_informacao_tecnica");

        builder.<PERSON><PERSON>ey(x => x.Id);
    }
}