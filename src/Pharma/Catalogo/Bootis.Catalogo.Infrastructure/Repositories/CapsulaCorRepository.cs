using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Catalogo.Infrastructure.Repositories;

public class CapsulaCorRepository(IUserContext userContext, IDbContext context)
    : Repository<CapsulaCor>(context), ICapsulaCorRepository
{
    public async Task<List<CapsulaCor>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<CapsulaCor>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }

    public async Task<bool> ValidarPorDescricaoAsync(string descricao)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                    FROM capsulas_cor cc
                                                    WHERE cc.cor_capsula = @descricao
                                                      AND cc.group_tenant_id = @groupTenantId) THEN 1 ELSE 0 END AS existe_registro
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { descricao, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarPorIdEDescricaoAsync(Guid id, string descricao)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT 1 
                                                    FROM capsulas_cor cc
                                                    WHERE cc.id = @id
                                                      AND cc.cor_capsula = @descricao
                                                      AND cc.group_tenant_id = @groupTenantId) THEN 1 ELSE 0 END AS existe_registro
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { id, descricao, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<IEnumerable<(Guid Id, string CorCapsula)>> VerificarDependenciaAsync(
        IEnumerable<Guid> ids)
    {
        const string sql = """
                           SELECT cc.id,
                                  cc.cor_capsula
                             FROM capsulas_cor cc
                                  LEFT JOIN produtos_tipo_capsula ptc ON
                                         ptc.capsula_cor_id = cc.id
                            WHERE cc.id = ANY ( @ids )
                              AND cc.group_tenant_id = @groupTenantId
                              AND ptc.capsula_cor_id IS NOT NULL
                           GROUP BY cc.id,
                                    cc.cor_capsula
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return (await dbConnection.QueryAsync<(Guid Id, string CorCapsula)>(sql,
            new { ids, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction())).ToList();
    }
}