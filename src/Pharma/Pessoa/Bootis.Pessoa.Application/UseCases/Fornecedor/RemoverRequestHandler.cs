using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Fornecedor.Remover;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Fornecedor;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork,
    IFornecedorRepository fornecedorRepository) : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var fornecedores = await fornecedorRepository.ObterFornecedoresAsync(request.Id);

        await fornecedorRepository.VerificarDependenciasAsync(fornecedores);

        foreach (var id in request.Id)
        {
            var fornecedor = fornecedores.Single(c => c.Id == id);

            fornecedorRepository.Remove(fornecedor);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}