using Bootis.Catalogo.Application.UseCases.Grupo;
using Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Grupo;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.Grupo;

public class AtualizarGrupoCommandHandlerTests : BaseTest
{
    private readonly Mock<IGrupoRepository> _grupoRepository;
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarGrupoCommandHandlerTests()
    {
        _grupoRepository = new Mock<IGrupoRepository>();

        unitOfWork.Setup(l => l.GetRepository<IGrupoRepository>())
            .Returns(_grupoRepository.Object);

        _handler = new AtualizarRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task AtualizarGrupo_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarGrupoCommandFake.CriarCommandValido();
        var grupo = GrupoFake.CriarGrupoValido();

        _grupoRepository.Setup(l => l.ObterPorIdAsync(cmd.GrupoId)).Returns(
            Task.FromResult(grupo));

        _grupoRepository.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao)).Returns(
            Task.FromResult(false));

        _grupoRepository.Setup(l => l.Update(grupo))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _grupoRepository.Verify(
            l => l.Update(
                It.Is<Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo>(m => m.Descricao == cmd.Descricao)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarGrupo_ExecutadoComErro_GuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarGrupoCommandFake.CriarCommandValido();
        var grupo = GrupoFake.CriarGrupoValido();

        _grupoRepository.Setup(l => l.ObterPorIdAsync(cmd.GrupoId)).Returns(
            Task.FromResult<Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo>(default));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<DomainException>(executionResult);
        _grupoRepository.Verify(
            l => l.Update(
                It.Is<Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo>(m => m.Descricao == cmd.Descricao)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarGrupo_ExecutadoComErro_DescricaoExistente()
    {
        //Arrange
        var cmd = AtualizarGrupoCommandFake.CriarCommandValido();
        var grupo = GrupoFake.CriarGrupoValido();

        _grupoRepository.Setup(l => l.ObterPorIdAsync(cmd.GrupoId))
            .Returns(Task.FromResult(grupo));

        _grupoRepository.Setup(l => l.ValidarPorDescricaoAsync(cmd.Descricao))
            .Returns(Task.FromResult(true));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _grupoRepository.Verify(
            l => l.Update(
                It.Is<Catalogo.Domain.AggregatesModel.GrupoAggregate.Grupo>(m => m.Descricao == cmd.Descricao)),
            Times.Never);
    }
}
