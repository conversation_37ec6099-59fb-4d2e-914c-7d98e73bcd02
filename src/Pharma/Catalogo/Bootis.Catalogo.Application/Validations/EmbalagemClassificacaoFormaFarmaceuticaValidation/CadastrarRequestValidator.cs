using Bootis.Catalogo.Application.Requests.EmbalagemClassificacaoFormaFarmaceutica.Cadastrar;
using Bootis.Shared;
using Bootis.Shared.Common;
using FluentValidation;

namespace Bootis.Catalogo.Application.Validations.EmbalagemClassificacaoFormaFarmaceuticaValidation;

public class CadastrarRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarRequestValidator()
    {
        RuleFor(c => c.EmbalagemClassificacaoId)
            .NotEmpty()
            .WithMessage(l =>
                Localizer.Instance.GetMessage_Validation_CampoRequerido(nameof(l.EmbalagemClassificacaoId)));

        RuleFor(c => c.FormaFarmaceuticaId)
            .NotEmpty()
            .WithMessage(l =>
                Localizer.Instance.GetMessage_Validation_CampoRequerido(nameof(l.FormaFarmaceuticaId)));
    }
}