using Bootis.Organizacional.Domain.Dtos;
using MediatR;

namespace Bootis.Organizacional.Application.Requests.Usuario.Cadastrar;

public class CadastrarRequest : IRequest
{
    public string Nome { get; init; }
    public string Sobrenome { get; init; }
    public string Cpf { get; init; }
    public string Email { get; init; }
    public DateOnly? DataNascimento { get; init; }
    public string EmailAlternativo { get; init; }
    public string Celular { get; init; }
    public ICollection<Guid> Grupos { get; init; } = Array.Empty<Guid>();
    public ICollection<PermissaoUsuarioDto> Permissoes { get; init; } = Array.Empty<PermissaoUsuarioDto>();
}