using System.Data;
using System.Reflection;
using System.Runtime.CompilerServices;
using Bootis.Setup.Api.Attributes;
using Bootis.Setup.Api.Extensions;
using MediatR;
using Npgsql;

namespace Bootis.Setup.Api.RequestHandlers;

public abstract record BaseRequest(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId) : IRequest
{
    public bool DefaultValues => SourceConnectionString is null;
}

public abstract class BaseRequestHandler<TRequest, TModel>(IConfiguration configuration)
    : BaseRequestHandler<TRequest>(configuration)
    where TRequest : BaseRequest
    where TModel : new()
{
    private readonly IConfiguration _configuration = configuration;

    protected async IAsyncEnumerable<TModel> GetDataFromSourceDatabase(
        string sourceConnectionString, string cmdText, [EnumeratorCancellation] CancellationToken cancellationToken,
        Func<TModel, Task> modifyFunc = null)
    {
        await using var sourceConnection = new NpgsqlConnection(sourceConnectionString);
        await sourceConnection.OpenAsync(cancellationToken);

        await using var command = new NpgsqlCommand(cmdText, sourceConnection);
        await using var reader = await command.ExecuteReaderAsync(cancellationToken);

        var properties = typeof(TModel).GetProperties(BindingFlags.Public | BindingFlags.Instance).Where(p =>
            {
                if (!p.CanRead) return false;
                var attribute = p.GetCustomAttribute<IgnoreColumnAttribute>();
                return attribute?.Source != true;
            })
            .ToArray();
        ;
        var columnMap = new Dictionary<string, PropertyInfo>();

        for (var i = 0; i < reader.FieldCount; i++)
        {
            var columnName = reader.GetName(i);
            var prop = Array.Find(properties,
                p => p.GetColumnName().Equals(columnName, StringComparison.OrdinalIgnoreCase));
            if (prop != null) columnMap[columnName] = prop;
        }

        while (await reader.ReadAsync(cancellationToken))
        {
            var entity = new TModel();
            foreach (var kvp in columnMap)
            {
                var value = reader[kvp.Key];
                if (value != DBNull.Value)
                    kvp.Value.SetValue(entity, Convert.ChangeType(value, kvp.Value.PropertyType));
            }

            if (modifyFunc != null) await modifyFunc(entity);

            yield return entity;
        }
    }

    protected async Task InsertOrUpdateData(IAsyncEnumerable<TModel> data, CancellationToken cancellationToken)
    {
        var type = typeof(TModel);
        var tableAlias = type.GetCustomAttribute<TableNameAttribute>()?.Name ?? type.Name;

        var props = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p =>
            {
                if (!p.CanRead) return false;
                var attribute = p.GetCustomAttribute<IgnoreColumnAttribute>();
                return attribute?.Destination != true;
            })
            .ToArray();

        var columnNames = props.Select(p => p.GetColumnName()).ToArray();

        await using var conn = new NpgsqlConnection(_configuration.GetConnectionString("DestinationConnectionString"));
        await conn.OpenAsync(cancellationToken);

        var copyCommand = $"COPY {tableAlias} ({string.Join(", ", columnNames)}) FROM STDIN (FORMAT BINARY)";

        await using var writer = await conn.BeginBinaryImportAsync(copyCommand, cancellationToken);
        await foreach (var item in data.WithCancellation(cancellationToken))
        {
            await writer.StartRowAsync(cancellationToken);
            foreach (var prop in props)
            {
                var value = prop.GetValue(item) ?? DBNull.Value;
                await writer.WriteAsync(value, cancellationToken);
            }
        }

        await writer.CompleteAsync(cancellationToken);
    }

    protected async Task<Dictionary<TKey, TValue>> GetDictionaryFromDatabase<TKey, TValue>(
        string query,
        Func<IDataReader, TKey> keySelector,
        Func<IDataReader, TValue> valueSelector,
        object parameters = null,
        CancellationToken cancellationToken = default)
        where TKey : notnull
    {
        var dictionary = new Dictionary<TKey, TValue>();

        await using var conn = new NpgsqlConnection(_configuration.GetConnectionString("DestinationConnectionString"));

        await conn.OpenAsync(cancellationToken);

        await using var command = new NpgsqlCommand(query, conn);

        if (parameters is not null)
            foreach (var prop in parameters.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                var paramName = "@" + prop.Name;
                var paramValue = prop.GetValue(parameters) ?? DBNull.Value;
                command.Parameters.AddWithValue(paramName, paramValue);
            }

        await using var reader = await command.ExecuteReaderAsync(cancellationToken);

        while (await reader.ReadAsync(cancellationToken))
        {
            var key = keySelector(reader);
            var value = valueSelector(reader);
            dictionary[key] = value;
        }

        return dictionary;
    }

    protected async Task<IEnumerable<Guid>> GetEntityIdEnumerableFromDataBase(
        string query,
        Func<IDataReader, Guid> idSelector,
        object parameters = null,
        CancellationToken cancellationToken = default)
    {
        var results = new List<Guid>();

        await using var conn = new NpgsqlConnection(_configuration.GetConnectionString("DestinationConnectionString"));
        await conn.OpenAsync(cancellationToken);

        await using var command = new NpgsqlCommand(query, conn);

        if (parameters is not null)
            foreach (var prop in parameters.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                var paramName = "@" + prop.Name;
                var paramValue = prop.GetValue(parameters) ?? DBNull.Value;
                command.Parameters.AddWithValue(paramName, paramValue);
            }

        await using var reader = await command.ExecuteReaderAsync(cancellationToken);

        while (await reader.ReadAsync(cancellationToken))
        {
            var id = idSelector(reader);
            results.Add(id);
        }

        return results;
    }

    public async Task InsertOrUpdateSequenceControlAsync(
        BaseRequest request,
        string entityName,
        string tableName,
        CancellationToken cancellationToken = default)
    {
        var sql = $@"
            INSERT INTO sequence_control (entity_name, tenant_id, group_tenant_id, current_sequence)
                 VALUES (@entity_name, @tenant_id, @group_tenant_id, (
                        SELECT COALESCE(MAX(id), 0) FROM {tableName} WHERE tenant_id = @tenant_id
                 ))
            ON CONFLICT (entity_name, tenant_id, group_tenant_id)
            DO UPDATE SET current_sequence = EXCLUDED.current_sequence;
        ";

        await using var conn = new NpgsqlConnection(_configuration.GetConnectionString("DestinationConnectionString"));
        await conn.OpenAsync(cancellationToken);

        await using var cmd = new NpgsqlCommand(sql, conn);
        cmd.Parameters.AddWithValue("@entity_name", entityName);
        cmd.Parameters.AddWithValue("@tenant_id", request.TenantId);
        cmd.Parameters.AddWithValue("@group_tenant_id", request.GroupTenantId);

        await cmd.ExecuteNonQueryAsync(cancellationToken);
    }
}

public abstract class BaseRequestHandler<TRequest>(IConfiguration configuration) : IRequestHandler<TRequest>
    where TRequest : BaseRequest
{
    public abstract Task Handle(TRequest request, CancellationToken cancellationToken);

    protected async Task<DataTable> GetDataFromSourceDatabase(BaseRequest request, string cmdText,
        CancellationToken cancellationToken)
    {
        await using var sourceConnection = new NpgsqlConnection(request.SourceConnectionString);
        await sourceConnection.OpenAsync(cancellationToken);

        var dataTable = new DataTable();
        await using var command = new NpgsqlCommand(cmdText, sourceConnection);
        await using var reader = await command.ExecuteReaderAsync(cancellationToken);
        dataTable.Load(reader);

        return dataTable;
    }


    protected async Task InsertOrUpdateData(DataTable dataTable, string destinationTableName,
        CancellationToken cancellationToken)
    {
        await using var destinationConnection =
            new NpgsqlConnection(configuration.GetConnectionString("DestinationConnectionString"));

        await destinationConnection.OpenAsync(cancellationToken);
    }
}