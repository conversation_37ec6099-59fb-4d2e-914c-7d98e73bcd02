using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;

public interface ILocalEstoqueRepository : IRepository<LocalEstoque>, IScopedService
{
    Task<LocalEstoque> ObterLocalEstoquePorDescricaoAsync(string descricao, Guid id, Guid empresaId);
    Task<LocalEstoque> ObterLocalEstoquePorDescricaoCadastroAsync(string descricao, Guid empresaId);
    Task<LocalEstoque> ObterPorIdAsync(Guid id);
    Task<List<LocalEstoque>> ObterPorIdsAsync(IEnumerable<Guid> ids);
    Task<bool> VerificarDependenciaLocalEstoqueAsync(Guid id);
}