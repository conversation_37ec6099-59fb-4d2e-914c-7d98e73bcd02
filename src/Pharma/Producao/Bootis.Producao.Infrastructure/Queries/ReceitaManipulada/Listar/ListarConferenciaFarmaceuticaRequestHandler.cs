using System.Data;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Listar;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Listar;

public class ListarConferenciaFarmaceuticaRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarConferenciaRequest, PaginatedResult<ListarConferenciaResponse>>
{
    public Task<PaginatedResult<ListarConferenciaResponse>> Handle(ListarConferenciaRequest request,
        CancellationToken cancellationToken)
    {
        const int aguardandoConferencia = (int)StatusReceita.AguardandoConferencia;

        var sql = $"""
                   SELECT rm.id AS receita_manipulada_id,
                          rm.sequencia_group_tenant AS numero_receita,
                          cl.nome AS paciente_nome, --Alterar após definição de paciente
                          pvi.descricao AS receita_descricao,
                          rm.data_emissao AS data_emissao,
                          ff.descricao AS forma_farmaceutica_descricao
                     FROM receitas_manipuladas rm
                          JOIN formas_farmaceutica ff ON ff.id = rm.forma_farmaceutica_id
                          JOIN clientes cl ON cl.id = rm.paciente_id
                          JOIN pedidos_venda_itens_receita_manipulada pvir ON pvir.receita_manipulada_id = rm.id
                          JOIN pedidos_venda_itens pvi ON pvi.id = pvir.id
                    WHERE rm.status = {aguardandoConferencia}
                      AND rm.group_tenant_id = @GroupTenantId
                      !@SEARCH_CONDITION@!
                   """;

        var searchNumeroReceita = new NumberSearchField
        {
            Field = "rm.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };
        
        var searchNomePaciente = new StringSearchField
        {
            Field = "cl.nome",
            CompareType = StringCompareType.Contains
        };
        
        var searchDescricaoReceita = new StringSearchField
        {
            Field = "pvi.descricao",
            CompareType = StringCompareType.Contains
        };
        
        var searchDataEmissao = new DateSearchField
        {
            Field = "rm.data_emissao",
            CompareType = DateCompareType.Contains
        };

        var searchFormaFarmaceutica = new StringSearchField
        {
            Field = "ff.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarConferenciaRequest, ListarConferenciaResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchNumeroReceita)
            .AddSearchField(searchNomePaciente)
            .AddSearchField(searchDescricaoReceita)
            .AddSearchField(searchDataEmissao)
            .AddSearchField(searchFormaFarmaceutica)
            .ExecuteAsync();
    }
}