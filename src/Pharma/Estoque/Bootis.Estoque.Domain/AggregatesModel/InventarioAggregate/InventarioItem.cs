using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Resources;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;

public class InventarioItem : Entity
{
    public InventarioItem()
    {
    }

    public InventarioItem(InventarioLancamento inventarioLancamento,
        SaldoEstoque saldoEstoque)
    {
        InventarioLancamento = inventarioLancamento;
        SaldoEstoque = saldoEstoque;
        LocalEstoqueId = saldoEstoque.LocalEstoqueId;
        LoteId = saldoEstoque.LoteId;
        Saldo = saldoEstoque.Saldo;
    }

    public InventarioItem(InventarioLancamento inventarioLancamento,
        SaldoEstoque saldoEstoque,
        Guid localEstoqueId,
        Guid loteId,
        decimal? quantidadeInventariada,
        UnidadeMedidaAbreviacao? unidadeMedidaId,
        Guid? responsavelOperadorId)
    {
        InventarioLancamento = inventarioLancamento;
        SaldoEstoque = saldoEstoque;
        LocalEstoqueId = localEstoqueId;
        LoteId = loteId;
        QuantidadeInventariada = quantidadeInventariada;
        ResponsavelOperadorId = responsavelOperadorId;
        ProdutoAdicionado = true;

        if (saldoEstoque != null)
        {
            Saldo = saldoEstoque.Saldo;
            var saldoConvertido = ConverterSaldo(unidadeMedidaId.GetValueOrDefault());

            Diferenca = QuantidadeInventariada - saldoConvertido;
        }

        UnidadeMedidaId = unidadeMedidaId;
    }

    public Guid InventarioLancamentoId { get; private set; }
    public Guid? SaldoEstoqueId { get; private set; }
    public Guid LocalEstoqueId { get; private set; }
    public Guid LoteId { get; private set; }
    public Guid? ResponsavelOperadorId { get; private set; }
    public decimal? Saldo { get; private set; }
    public decimal? QuantidadeInventariada { get; private set; }
    public UnidadeMedidaAbreviacao? UnidadeMedidaId { get; private set; }
    public bool? Aprovado { get; private set; }
    public decimal? Diferenca { get; private set; }
    public bool ProdutoAdicionado { get; private set; }

    #region Navigation properties
    public virtual InventarioLancamento InventarioLancamento { get; set; }
    public virtual SaldoEstoque SaldoEstoque { get; set; }
    public virtual LocalEstoque LocalEstoque { get; set; }
    public virtual Lote Lote { get; set; }
    public virtual Usuario ResponsavelOperador { get; set; }
    #endregion

    public void LancarQuantidadeInventariada(Guid responsavelOperadorId, decimal quantidadeInventariada,
        UnidadeMedidaAbreviacao unidadeMedidaId)
    {
        ResponsavelOperadorId = responsavelOperadorId;
        QuantidadeInventariada = quantidadeInventariada;
        UnidadeMedidaId = unidadeMedidaId;

        if (SaldoEstoque != null)
        {
            var saldoConvertido = ConverterSaldo(unidadeMedidaId);

            Diferenca = QuantidadeInventariada - saldoConvertido;
        }
    }

    public void LancarQuantidadeInventariadaRascunho(Guid? responsavelOperadorId, decimal? quantidadeInventariada,
        UnidadeMedidaAbreviacao? unidadeMedidaId)
    {
        ResponsavelOperadorId = responsavelOperadorId;
        QuantidadeInventariada = quantidadeInventariada;
        UnidadeMedidaId = unidadeMedidaId;
    }

    private decimal ConverterSaldo(UnidadeMedidaAbreviacao unidadeMedidaId)
    {
        var conversaoSaldoEstoque = ConversaoUnidadeMedidaCreator.Criar(SaldoEstoque.UnidadeMedidaId, unidadeMedidaId);

        if (conversaoSaldoEstoque is null)
            throw new ValidationException(nameof(unidadeMedidaId),
                Localizer.Instance.GetMessage_ConversaoUnidadeMedida_IdNaoEncontrado((int)SaldoEstoque.UnidadeMedidaId,
                    (int)unidadeMedidaId));

        return conversaoSaldoEstoque.CalcularConversao(SaldoEstoque.Saldo);
    }

    public void ValidarQuantidadeInventariada(bool? aprovado)
    {
        Aprovado = aprovado;
    }
}