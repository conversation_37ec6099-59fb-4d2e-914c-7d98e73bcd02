using Bootis.Identity.Domain.Entities;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Identity.Application.Groups;

public class CreateGroupRequestHandler(IUnitOfWork unitOfWork)
    : IRequestHandler<CreateGroupRequest>
{
    public async Task Handle(CreateGroupRequest request, CancellationToken cancellationToken)
    {
        var permissions = request.Permissions
            .Where(c => c.Active)
            .Select(c => new GroupPermission { PermissionId = c.Id })
            .ToList();

        var userRepository = unitOfWork.GetRepository<IUserRepository>();
        var users = await userRepository.GetUserGroupAsync(request.Users, cancellationToken);

        var group = new Group
        {
            Id = request.Id,
            IsActive = request.Active,
            GroupPermissions = permissions,
            UserGroups = users
        };

        var groupRepository = unitOfWork.GetRepository<IGroupRepository>();
        await groupRepository.AddAsync(group, cancellationToken);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}