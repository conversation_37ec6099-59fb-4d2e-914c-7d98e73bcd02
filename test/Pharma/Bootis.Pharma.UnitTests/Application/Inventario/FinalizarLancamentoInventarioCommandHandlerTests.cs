using Bootis.Estoque.Application.UseCases.Inventario;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Inventario;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.Inventario;

public class FinalizarLancamentoInventarioCommandHandlerTests : BaseTest
{
    private readonly FinalizarLancamentoRequestHandler _handler;
    private readonly Mock<IInventarioRepository> _inventarioRepositoryMock;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public FinalizarLancamentoInventarioCommandHandlerTests()
    {
        _inventarioRepositoryMock = new Mock<IInventarioRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _handler = new FinalizarLancamentoRequestHandler(unitOfWork.Object, UserContext.Object,
            _inventarioRepositoryMock.Object,
            _usuarioRepositoryMock.Object);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task FinalizarLancamentoInventario_ExecutadoComSucesso()
    {
        //Arrange 
        var cmd = FinalizarLancamentoInventarioCommandFake.CreateFinalizarLancamentoInventarioCommandValido();
        var inventario = InventarioFake.CriarInventarioValido();
        var saldosEstoque = new List<SaldoEstoque>
        {
            SaldoEstoqueFake.CreateSaldoEstoqueValido()
        };
        var responsaveisOperadorIds = new Dictionary<Guid, int>
        {
            { cmd.Itens.First().UsuarioId, 1 }
        };
        var usuarioId = 1.ToGuid();

        inventario.IniciarLancamento(saldosEstoque, usuarioId);

        _inventarioRepositoryMock.Setup(l => l.ObterInventarioPorIdAsync(cmd.InventarioId)).Returns(
            Task.FromResult(inventario));

        _inventarioRepositoryMock.Setup(l => l.Update(inventario))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _inventarioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario>(m =>
                    m.StatusInventario == inventario.StatusInventario)),
            Times.Once);
    }
}
