using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.Produto.Cadastrar;
using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.TipoCapsulaAggregate;
using Bootis.Shared.Application.Interfaces;

namespace Bootis.Catalogo.Application.UseCases.Produto;

public class CadastrarTipoCapsulaRequestHandler(
    IUnitOfWork unitOfWork)
    : CadastrarRequestHandler<CadastrarTipoCapsulaRequest>(unitOfWork)
{
    protected override async Task InternalCadastrarAsync(CadastrarTipoCapsulaRequest request,
        Domain.AggregatesModel.ProdutoAggregate.Produto produto, CancellationToken cancellationToken)
    {
        var capsulaCorRepository = unitOfWork.GetRepository<ICapsulaCorRepository>();
        var capsulaTamanhoRepository = unitOfWork.GetRepository<ICapsulaTamanhoRepository>();
        var tipoCapsulaRepository = unitOfWork.GetRepository<ITipoCapsulaRepository>();

        var tipoCapsula = await tipoCapsulaRepository.ObterTipoCapsulaAsync(request.TipoCapsulaId);
        var capsulaCor = await capsulaCorRepository.ObterCapsulaCorAsync(request.CapsulaCorId);
        var capsulaTamanho = await capsulaTamanhoRepository.ObterCapsulaTamanhoAsync(request.CapsulaTamanhoId);

        produto.AtualizarProdutoTipoCapsula(capsulaCor, tipoCapsula, capsulaTamanho);
    }
}