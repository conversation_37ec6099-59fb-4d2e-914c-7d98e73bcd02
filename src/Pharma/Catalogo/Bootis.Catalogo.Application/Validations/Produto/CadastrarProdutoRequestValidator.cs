using Bootis.Catalogo.Application.Requests.Produto.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.Produto;

public abstract class CadastrarProdutoRequestValidator<T> : AbstractValidator<T> where T : CadastrarRequest
{
    protected CadastrarProdutoRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(produto => produto)
            .NotEmpty();

        RuleFor(c => c.Descricao)
            .MaximumLength(100)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.Descricao)));

        RuleFor(c => c.DescricaoRotulo)
            .MaximumLength(200);

        RuleFor(c => c.ClasseProdutoId)
            .IsInEnum()
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ClasseProdutoId)));

        RuleFor(c => c.TipoTarjaMedicamentoId)
            .IsInEnum();

        RuleFor(c => c.TipoClassificacaoPsicotropicaMedicamentoId)
            .IsInEnum();

        RuleFor(c => c.SubGrupoId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.SubGrupoId)));

        RuleFor(c => c.UnidadeEstoqueId)
            .IsInEnum()
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.UnidadeEstoqueId)));

        RuleFor(c => c.ValorCusto)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ValorCusto)));

        RuleFor(c => c.MargemLucro)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.MargemLucro)));

        RuleFor(c => c.ValorVenda)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ValorVenda)));

        RuleFor(c => c.ControlaLote)
            .NotNull();

        RuleFor(c => c.UsoContinuo)
            .NotNull();

        RuleFor(c => c.Etiqueta)
            .NotNull();

        RuleFor(c => c.ControlaQualidade)
            .NotNull();
    }
}