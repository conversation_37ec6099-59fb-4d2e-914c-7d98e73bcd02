using System.Data;
using Bootis.Pessoa.Application.Requests.Cliente.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Cliente.Listar;

public class
    ListarEnderecoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarEnderecoRequest, PaginatedResult<ListarEnderecoResponse>>
{
    public Task<PaginatedResult<ListarEnderecoResponse>> Handle(ListarEnderecoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT ce.id AS cliente_endereco_id,
                                  ce.logradouro,
                                  ce.numero,
                                  ce.bairro,
                                  ci.id AS cidade_id,
                                  ci.descricao AS cidade_descricao,
                                  es.id AS estado_id,
                                  es.descricao AS estado_descricao,
                                  pa.id AS pais_id,
                                  pa.descricao AS pais_descricao,
                                  ce.principal,
                                  ce.descricao
                             FROM cliente_enderecos ce
                                  LEFT JOIN cidades ci ON
                                          ci.id = ce.cidade_id
                                  LEFT JOIN estados es ON
                                          es.id = ce.estado_id
                                  LEFT JOIN paises pa ON
                                          pa.id = ce.pais_id
                                  LEFT JOIN clientes c ON
                                          c.id = ce.cliente_id
                            WHERE c.id = @ClienteId
                              AND ce.group_tenant_id = @GroupTenantId
                              !@SEARCH_CONDITION@!
                           """;

        var searchLogradouro = new StringSearchField
        {
            Field = "CE.logradouro",
            CompareType = StringCompareType.Contains
        };

        var searchNumero = new StringSearchField
        {
            Field = "CE.numero",
            CompareType = StringCompareType.Contains
        };

        var searchBairro = new StringSearchField
        {
            Field = "CE.bairro",
            CompareType = StringCompareType.Contains
        };

        var searchCidadeDescricao = new StringSearchField
        {
            Field = "CI.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchEstadoDescricao = new StringSearchField
        {
            Field = "ES.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchPaisDescricao = new StringSearchField
        {
            Field = "PA.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchEnderecoDescricao = new StringSearchField
        {
            Field = "CE.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarEnderecoRequest, ListarEnderecoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchLogradouro)
            .AddSearchField(searchNumero)
            .AddSearchField(searchBairro)
            .AddSearchField(searchCidadeDescricao)
            .AddSearchField(searchEstadoDescricao)
            .AddSearchField(searchPaisDescricao)
            .AddSearchField(searchEnderecoDescricao)
            .ExecuteAsync();
    }
}