using Asp.Versioning;
using Bootis.Identity.Application.Users;
using Bootis.Identity.Application.Users.Models;
using Bootis.Shared.Application.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Identity.Api.Controllers.V1;

[ApiVersion(1)]
[Route("v{version:apiVersion}/users")]
[ApiController]
public class UserController(IMediator mediator, ILogger<UserController> logger) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(typeof(object), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request)
    {
        logger.LogInformation("Creating new user with email: {Email}", request.Email);

        await mediator.Send(request);

        logger.LogInformation("User created successfully with email: {Email}", request.Email);
        return Created(string.Empty, new { message = "Usuário criado com sucesso" });
    }

    [HttpGet("me")]
    [Authorize]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public IActionResult GetCurrentUser([FromServices] IUserContext userContext)
    {
        if (userContext.UserSession is null)
        {
            logger.LogWarning("Attempt to get current user info with null user context");
            return Unauthorized();
        }

        logger.LogDebug("Returning current user info for user {UserId}", userContext.UserId);
        return Ok(userContext.UserSession);
    }

    [HttpPut("confirm-email")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ConfirmEmail([FromBody] ConfirmEmailRequest request)
    {
        logger.LogInformation("Processing email confirmation for token: {Code}", request.Code);

        await mediator.Send(request);

        logger.LogInformation("Email confirmed successfully");
        return Ok(new { message = "Email confirmado com sucesso" });
    }

    [HttpPost("forgot-password")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ForgotPassword([FromBody] ForgetPasswordRequest request)
    {
        logger.LogInformation("Processing forgot password request for email: {Email}", request.Email);

        await mediator.Send(request);

        logger.LogInformation("Forgot password email sent successfully to: {Email}", request.Email);
        return Ok(new { message = "Email de recuperação enviado com sucesso" });
    }

    [HttpPut("reset-password")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ResetPassword([FromBody] CreatePasswordRequest request)
    {
        logger.LogInformation("Processing password reset for code: {Code}", request.Code);

        await mediator.Send(request);

        logger.LogInformation("Password reset successfully");
        return Ok(new { message = "Senha redefinida com sucesso" });
    }

    [HttpPut("change-password")]
    [Authorize]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        logger.LogInformation("Processing password change request for authenticated user");

        await mediator.Send(request);

        logger.LogInformation("Password changed successfully");
        return Ok(new { message = "Senha alterada com sucesso" });
    }
}