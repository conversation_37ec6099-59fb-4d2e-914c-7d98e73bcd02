using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Inventario;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;
using Bootis.Shared.UnitTests.Extensions;

namespace Bootis.Estoque.UnitTests.Domain;

public class InventarioItemTests : BaseTest
{
    private readonly InventarioItem _inventarioItem = InventarioFake.CriarInventarioItemValido();

    [Fact]
    public void InventarioItem_DeveLancarQuantidadeInventariada()
    {
        //Arrange 
        var cmd = FinalizarLancamentoInventarioCommandFake.CreateFinalizarLancamentoInventarioCommandValido().Itens
            .First();
        var responsavelOperadorId = 1.ToGuid();

        _inventarioItem
            .SetPropertyValue(nameof(InventarioItem.SaldoEstoque),
                SaldoEstoqueFake.CreateSaldoEstoqueValido());

        //Action
        _inventarioItem.LancarQuantidadeInventariada(responsavelOperadorId, cmd.QuantidadeInventariada,
            cmd.UnidadeMedidaId);

        //Assert
        Assert.Equal(cmd.QuantidadeInventariada, _inventarioItem.QuantidadeInventariada);
    }

    [Fact(Skip = "Need to be fixed")]
    public void InventarioItem_TentarLancarQuantidadeInventariadaConversaoInvalida_ExecutadoComErro()
    {
        //Arrange
        var cmd = FinalizarLancamentoInventarioCommandFake.CreateFinalizarLancamentoInventarioCommandValido().Itens
            .First();
        var responsavelOperadorId = 1.ToGuid();
        _inventarioItem
            .SetPropertyValue(nameof(InventarioItem.SaldoEstoque),
                SaldoEstoqueFake.CreateSaldoEstoqueValido());
        _inventarioItem.SaldoEstoque
            .SetPropertyValue(nameof(SaldoEstoque.UnidadeMedidaId), UnidadeMedidaAbreviacao.un.ToInt());

        //Action
        var exception = Record.Exception(() =>
            _inventarioItem.LancarQuantidadeInventariada(responsavelOperadorId, cmd.QuantidadeInventariada,
                cmd.UnidadeMedidaId));

        //Assert
        Assert.IsType<ValidationException>(exception);
    }

    [Fact]
    public void InventarioItem_DeveLancarQuantidadeInventariadaRascunho()
    {
        //Arrange 
        var cmd = FinalizarLancamentoRascunhoInventarioCommandFake
            .CreateFinalizarLancamentoRascunhoInventarioCommandValido().Itens.First();
        var responsavelOperadorId = 1.ToGuid();
        _inventarioItem
            .SetPropertyValue(nameof(InventarioItem.SaldoEstoque),
                SaldoEstoqueFake.CreateSaldoEstoqueValido());

        //Action
        _inventarioItem.LancarQuantidadeInventariadaRascunho(responsavelOperadorId, cmd.QuantidadeInventariada,
            cmd.UnidadeMedidaId);

        //Assert
        Assert.Equal(cmd.QuantidadeInventariada, _inventarioItem.QuantidadeInventariada);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void InventarioItem_DeveValidarQuantidadeInventariada(bool aprovado)
    {
        //Action
        _inventarioItem.ValidarQuantidadeInventariada(aprovado);

        //Assert
        Assert.Equal(aprovado, _inventarioItem.Aprovado);
    }
}
