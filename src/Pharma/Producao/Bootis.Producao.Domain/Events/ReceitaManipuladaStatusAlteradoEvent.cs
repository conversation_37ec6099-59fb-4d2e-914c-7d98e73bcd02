using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Common.Events;
using MediatR;

namespace Bootis.Producao.Domain.Events;

public class ReceitaManipuladaStatusAlteradoEvent(Guid receitaManipuladaId,
    StatusReceita novoStatus, StatusReceita statusAnterior, Guid usuarioId)
    : IDomainEvent, INotification
{
    public Guid ReceitaManipuladaId { get; } = receitaManipuladaId;
    public StatusReceita NovoStatus { get; } = novoStatus;
    public StatusReceita StatusAnterior { get; } = statusAnterior;
    
    public Guid UsuarioId { get; } = usuarioId;
}
