using Bootis.Organizacional.Application.Requests.Usuario.Remover;
using Bootis.Organizacional.Common;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Organizacional.Application.Extensions;

public static class UsuarioRepositoryExtension
{
    public static async Task<Usuario> ObterUsuarioAsync(this IUsuarioRepository repository, Guid id)
    {
        var usuario = await repository.ObterPorIdAsync(id);

        if (usuario is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Usuario_GuidNaoEncontrado(id));

        return usuario;
    }

    public static async Task<List<Usuario>> ObterUsuariosAsync(this IUsuarioRepository repository,
        IEnumerable<Guid> ids)
    {
        var usuarios = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !usuarios.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_Usuario_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return usuarios;
    }

    public static async Task<Usuario> ObterUsuarioLogadoAsync(this IUsuarioRepository repository)
    {
        var usuario = await repository.ObterUsuarioLogado();

        if (usuario is null)
            throw new ValidationException(nameof(usuario.Id),
                Localizer.Instance.GetMessage_Usuario_NenhumLogadoEncontrado());

        return usuario;
    }

    public static async Task ValidarUsuarioResponsavelAsync(this IUsuarioRepository repository, Guid usuarioId)
    {
        var usuarioResposavel = await repository.ValidarUsuarioResponsavel(usuarioId);

        if (usuarioResposavel)
            throw new ValidationException(
                Localizer.Instance.GetMessage_Usuario_JaResponsavel());
    }

    public static async Task VerificarDependenciasAsync(this IUsuarioRepository repository,
        ICollection<Usuario> usuarios)
    {
        var removerResponse = new List<RemoverResponse>();
        var ids = usuarios.Select(u => u.Id).ToList();
        var usuariosDependentes = await repository.VerificarDependenciasUsuarioAsync(ids);

        removerResponse.AddRange(usuariosDependentes.Select(x => new RemoverResponse
        {
            UsuarioId = x.UsuarioId,
            UsuarioNomeCompleto = x.UsuarioNomeCompleto
        }));

        if (removerResponse.Count > 0)
            throw new DomainException(nameof(OrganizacionalErrorCode), (int)OrganizacionalErrorCode.Usuario_Remover,
                removerResponse.ToArray());
    }

    public static async Task ValidarUsuarioEmailAsync(this IUsuarioRepository repository, string email)
    {
        var usuarioEmail = await repository.ValidarUsuarioEmail(email);

        if (usuarioEmail)
            throw new ValidationException(nameof(email), Localizer.Instance.GetMessage_Usuario_EmailJaCadastrado());
    }
}