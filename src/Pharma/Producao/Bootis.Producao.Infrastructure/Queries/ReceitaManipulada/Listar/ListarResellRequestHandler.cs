using System.Data;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Listar;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Listar;

public class ListarResellRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarResellRequest, PaginatedResult<ListarResellResponse>>
{
    public async Task<PaginatedResult<ListarResellResponse>> Handle(ListarResellRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                               SELECT rm.id AS receita_manipulada_id,
                                      cl.nome AS cliente_descricao,
                                      pvi.descricao AS receita_descricao,
                                      rm.data_emissao AS data_emissao, 
                                      rm.duracao_prevista_re_sell_dias AS duracao_prevista_dias,
                                      rm.previsao_termino_re_sell AS previsao_termino
                               FROM receitas_manipuladas rm
                                         JOIN clientes cl
                                           ON cl.id = rm.paciente_id
                                         JOIN pedidos_venda_itens_receita_manipulada pvir
                                           ON pvir.receita_manipulada_id = rm.id
                                         JOIN pedidos_venda_itens pvi
                                           ON pvi.id = pvir.id
                               WHERE rm.uso_continuo = true
                                 AND rm.group_tenant_id = @GroupTenantId
                                     !@SEARCH_CONDITION@!
                           """;

        var searchCliente = new StringSearchField
        {
            Field = "cl.nome",
            CompareType = StringCompareType.Contains
        };

        var searchReceita = new StringSearchField
        {
            Field = "pvi.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchDataEmissao = new DateSearchField
        {
            Field = "rm.data_emissao",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchDuracaoPrevista = new NumberSearchField
        {
            Field = "rm.duracao_prevista_re_sell_dias",
            CompareType = NumericCompareType.Contains
        };

        var searchPrevisaoTermino = new DateSearchField
        {
            Field = "rm.previsao_termino_re_sell",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };
        
        var filterPrevisaoTerminoInicio = new ConditionalFilterField<ListarResellRequest>
        {
            Filter = "rm.previsao_termino_re_sell >= @PrevisaoTerminoInicio",
            Predicate = filter => filter.PrevisaoTerminoInicio.HasValue
        };

        var filterPrevisaoTerminoFim = new ConditionalFilterField<ListarResellRequest>
        {
            Filter = "rm.previsao_termino_re_sell <= @PrevisaoTerminoFim",
            Predicate = filter => filter.PrevisaoTerminoFim.HasValue
        };
        
        var conditionalSituacaoEncerrado = new ConditionalFilterField<ListarResellRequest>
        {
            Filter = "rm.previsao_termino_re_sell < CURRENT_DATE",
            Predicate = filter => filter.Situacao.HasValue && filter.Situacao.Value == SituacaoUsoReceita.Encerrado
        };

        var conditionalSituacaoEmUso = new ConditionalFilterField<ListarResellRequest>
        {
            Filter = "rm.previsao_termino_re_sell >= CURRENT_DATE",
            Predicate = filter => filter.Situacao.HasValue && filter.Situacao.Value == SituacaoUsoReceita.EmUso
        };
        
        var resultado = await PaginatedQueryBuilder<ListarResellRequest, ListarResellResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchCliente)
            .AddSearchField(searchReceita)
            .AddSearchField(searchDataEmissao)
            .AddSearchField(searchDuracaoPrevista)
            .AddSearchField(searchPrevisaoTermino)
            .AddFilter(filterPrevisaoTerminoInicio)
            .AddFilter(filterPrevisaoTerminoFim)
            .AddFilter(conditionalSituacaoEncerrado)
            .AddFilter(conditionalSituacaoEmUso)
            .ExecuteAsync();
        
        if (resultado?.Data?.Any() != true)
            return resultado;
        
        var receitaIds = resultado.Data.Select(x => x.ReceitaManipuladaId).ToList();
        
        const string sqlMateriasPrimas = """
                                         SELECT rmi.receita_manipulada_id,
                                                p.descricao AS descricao_materia_prima
                                         FROM receitas_manipuladas_item rmi
                                              JOIN produtos p ON p.id = rmi.produto_id
                                              JOIN produtos_materia_prima pmp ON pmp.produto_id = p.id
                                         WHERE rmi.receita_manipulada_id = ANY(@ReceitaIds)
                                         ORDER BY rmi.receita_manipulada_id, p.descricao
                                         """;
        
        var materiasPrimas = 
            await connection.QueryAsync<(Guid receita_manipulada_id, string descricao_materia_prima)>(
                sqlMateriasPrimas, new { ReceitaIds = receitaIds.ToArray() });
        
        var materiasPrimasAgrupadas = materiasPrimas
            .GroupBy(x => x.receita_manipulada_id)
            .ToDictionary(
                group => group.Key,
                group => group
                    .Select(x => x.descricao_materia_prima).ToList()
            );
        
        foreach (var item in resultado.Data)
        {
            if (materiasPrimasAgrupadas.TryGetValue(item.ReceitaManipuladaId, out var materias))
            {
                item.MateriasPrimas = materias;
            }
        }

        return resultado;
    }
}