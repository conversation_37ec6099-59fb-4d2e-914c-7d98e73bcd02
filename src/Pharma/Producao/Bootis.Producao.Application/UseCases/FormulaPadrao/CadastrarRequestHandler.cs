using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Producao.Application.Extensions;
using Bootis.Producao.Application.Requests.FormulaPadrao.Cadastrar;
using Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Producao.Application.UseCases.FormulaPadrao;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    IFormulaPadraoRepository formulaPadraoRepository,
    IProdutoRepository produtoRepository,
    IFormaFarmaceuticaRepository formaFarmaceuticaRepository)
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var produto = await produtoRepository.ObterProdutoAsync(request.ProdutoId);
        var produtosFormulaPadraoItem =
            await produtoRepository.ObterProdutosAsync(request.Produtos.Select(c => c.ProdutoId));
        var formaFarmaceutica =
            await formaFarmaceuticaRepository.ObterFormaFarmaceuticaAsync(request.FormaFarmaceuticaId);

        await formulaPadraoRepository.ValidarPorProdutoIdAsync(produto);

        if (request.QuantidadePadrao == 0)
            throw new ValidationException(nameof(request.ProdutoId),
                Localizer.Instance.GetMessage_Produto_DosagemInvalida());

        var formulaPadrao = new Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao(produto,
            formaFarmaceutica,
            request);

        foreach (var formulaPadraoItemRequest in request.Produtos)
        {
            var produtoFormulaPadraoItem =
                produtosFormulaPadraoItem.Single(c => c.Id == formulaPadraoItemRequest.ProdutoId);

            formulaPadrao.CadastrarFormulaPadraoItem(formulaPadraoItemRequest, produtoFormulaPadraoItem);
        }

        formulaPadraoRepository.Add(formulaPadrao);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}