using Bootis.Catalogo.Domain.Enumerations;

namespace Bootis.Catalogo.Application.Requests.FormaFarmaceutica.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public int Ordem { get; set; }
    public string Descricao { get; set; }
    public string Apresentacao { get; set; }
    public UsoFormaFarmaceutica UsoFormaFarmaceutica { get; set; }
    public string LaboratorioDescricao { get; set; }
    public bool Ativo { get; set; }
}