namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record ProdutoDiluido(
    Guid Id,
    string ProdutoDescricao,
    string FormaFarmaceuticaDescricao,
    decimal DosagemMinima,
    decimal DosagemMaxima,
    int UnidadeMedidaId,
    decimal Diluicao,
    bool SeTodasFormasFarmaceuticas,
    bool SeQualquerDosagem);

public record MigrateProdutosDiluido(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateProdutosDiluidoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateProdutosDiluido>(configuration)
{
    public override async Task Handle(MigrateProdutosDiluido request, CancellationToken cancellationToken)
    {
        var dataTable = await GetDataFromSourceDatabase(request, "SELECT * FROM produtos_diluido", cancellationToken);

        await InsertOrUpdateData(dataTable, "produtos_diluido", cancellationToken);
    }
}