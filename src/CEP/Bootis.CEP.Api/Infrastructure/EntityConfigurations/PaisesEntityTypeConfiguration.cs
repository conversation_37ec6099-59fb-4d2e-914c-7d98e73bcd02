using Bootis.CEP.Api.Models;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.CEP.Api.Infrastructure.EntityConfigurations;

public class PaisesEntityTypeConfiguration : IEntityTypeConfiguration<Pais>
{
    public void Configure(EntityTypeBuilder<Pais> builder)
    {
        builder.ToTable("paises");

        builder.Property<int>("id")
            .IsRequired()
            .ValueGeneratedNever();

        builder.HasKey("id");

        builder
            .Property(c => c.Nome)
            .Endereco(TamanhoTexto.Cem)
            .IsRequired();
    }
}