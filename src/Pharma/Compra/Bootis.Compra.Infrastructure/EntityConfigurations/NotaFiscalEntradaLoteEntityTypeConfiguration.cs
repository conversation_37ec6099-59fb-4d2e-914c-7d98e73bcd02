using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Compra.Infrastructure.EntityConfigurations;

public class NotaFiscalEntradaLoteEntityTypeConfiguration : BaseEntityTypeConfiguration<NotaFiscalEntradaLote>
{
    public override void Configure(EntityTypeBuilder<NotaFiscalEntradaLote> builder)
    {
        builder.ToTable("notas_fiscais_entrada_lotes");

        builder
            .HasOne(c => c.NotaFiscalEntradaItem)
            .WithMany(c => c.NotaFiscalEntradaLote)
            .HasForeignKey(c => c.NotaFiscalEntradaItemId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasIndex(c => c.NotaFiscalEntradaItemId)
            .IsUnique(false);

        builder
            .HasOne(c => c.LocalEstoque)
            .WithMany()
            .HasForeignKey(x => x.LocalEstoqueId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        builder
            .Property(c => c.NumeroLote)
            .Documento(TamanhoTexto.Cinquenta)
            .IsRequired(false);

        builder
            .Property(c => c.DataLancamento)
            .DataHora()
            .IsRequired(false);

        builder
            .Property(c => c.DataFabricacao)
            .Data()
            .IsRequired(false);

        builder
            .Property(c => c.DataValidade)
            .Data()
            .IsRequired(false);

        builder
            .Property(c => c.UnidadeId)
            .IsRequired(false);

        builder
            .Property(c => c.Quantidade)
            .EstoquePrecisao()
            .IsRequired(false);

        builder
            .Property(c => c.QuantidadeRotulo)
            .Estoque()
            .IsRequired(false);

        base.Configure(builder);
    }
}