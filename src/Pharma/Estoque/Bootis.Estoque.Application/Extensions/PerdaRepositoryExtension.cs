using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Estoque.Application.Extensions;

public static class PerdaRepositoryExtension
{
    public static async Task<Perda> ObterPerdaAsync(this IPerdaRepository repository, Guid id)
    {
        var perda = await repository.ObterPorId(id);

        if (perda == null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_LancamentoPerda_GuidNaoEncontrado(id));

        return perda;
    }
}