using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.CapsulaCor.Atualizar;
using Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.CapsulaCor;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork) : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        var capsulaCorRepository = unitOfWork.GetRepository<ICapsulaCorRepository>();
        var capsulaCor = await capsulaCorRepository.ObterCapsulaCorAsync(request.Id);

        await capsulaCorRepository.ValidarCapsulaCorPorIdEDescricaoAsync(request.Id,
            request.CorCapsula);

        capsulaCor.AtualizarCapsulaCor(request.CorCapsula, request.Transparente);

        capsulaCorRepository.Update(capsulaCor);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}