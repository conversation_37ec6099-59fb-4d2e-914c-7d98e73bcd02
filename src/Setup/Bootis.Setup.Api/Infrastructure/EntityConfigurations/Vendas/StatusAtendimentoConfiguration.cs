using Bootis.Setup.Api.Extensions;
using Bootis.Setup.Api.Models.Vendas;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Setup.Api.Infrastructure.EntityConfigurations.Vendas;

public class StatusAtendimentoConfiguration : IEntityTypeConfiguration<StatusAtendimento>
{
    public void Configure(EntityTypeBuilder<StatusAtendimento> builder)
    {
        builder.ToTable(typeof(StatusAtendimento).GetTableName());

        builder.HasKey(x => x.Id);
    }
}