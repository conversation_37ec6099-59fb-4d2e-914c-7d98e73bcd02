using Bootis.Producao.Application.Extensions;
using Bootis.Producao.Application.Requests.Posologia.Remover;
using Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Producao.Application.UseCases.Posologia;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork,
    IPosologiaRepository posologiaRepository) : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var posologias = await posologiaRepository.ObterPosologiasAsync(request.Id);

        await posologiaRepository.VerificarDependenciasAsync(posologias);

        foreach (var id in request.Id)
        {
            var posologia = posologias.Single(c => c.Id == id);

            posologiaRepository.Remove(posologia);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}