using Bootis.Localidade.Application.Extensions;
using Bootis.Localidade.Domain.AggregatesModel.CidadeAggregate;
using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Prescritor;

public class AtualizarEnderecoRequestHandler(
    IUnitOfWork unitOfWork,
    IPrescritorRepository prescritorRepository,
    IPaisRepository paisRepository,
    IEstadoRepository estadoRepository,
    ICidadeRepository cidadeRepository)
    : IRequestHandler<AtualizarEnderecoRequest>
{
    public async Task Handle(AtualizarEnderecoRequest request, CancellationToken cancellationToken)
    {
        var prescritorEndereco =
            await prescritorRepository.ObterPrescritorEnderecoAsync(request.PrescritorEnderecoId);

        if (prescritorEndereco == null)
            throw new ValidationException(nameof(request.PrescritorEnderecoId), "PrescritorEndereco não encontrado");

        var prescritor = await prescritorRepository.GetByIdAsync(prescritorEndereco.PrescritorId);

        if (prescritor == null)
            throw new ValidationException(nameof(prescritorEndereco.PrescritorId), "Prescritor não encontrado");

        var pais = await paisRepository.ObterPaisAsync(request.PaisId);

        if (pais == null)
            throw new ValidationException(nameof(request.PaisId), "País não encontrado");

        var estado = await estadoRepository.ObterEstadoAsync(request.EstadoId);

        if (estado == null)
            throw new ValidationException(nameof(request.EstadoId), "Estado não encontrado");

        var cidade = await cidadeRepository.ObterCidadeAsync(request.CidadeId);

        if (cidade == null)
            throw new ValidationException(nameof(request.CidadeId), "Cidade não encontrada");

        prescritorEndereco.Atualizar(prescritor.Id, pais, estado, cidade, request.Bairro, request.Cep,
            request.Logradouro,
            request.Numero, request.Complemento, request.Descricao, request.Principal);

        prescritor.AtualizarEndereco(prescritorEndereco);

        prescritorRepository.Update(prescritor);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}