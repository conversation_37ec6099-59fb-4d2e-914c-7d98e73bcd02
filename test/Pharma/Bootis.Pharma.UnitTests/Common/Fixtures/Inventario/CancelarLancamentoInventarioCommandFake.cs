using Bootis.Estoque.Application.Requests.Inventario.Atualizar;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Inventario;

public class CancelarLancamentoInventarioCommandFake
{
    public static CancelarLancamentoRequest CreateCancelarLancamentoInventarioCommandValido()
    {
        return new CancelarLancamentoRequest
        {
            Id = Uuid.NewSequential()
        };
    }
}
