using System.Data;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using MediatR;

namespace Bootis.Compra.Infrastructure.Queries.NotaFiscalEntrada.Listar;

public class
    ListarHistoricoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarHistoricoRequest, PaginatedResult<ListarHistoricoResponse>>
{
    public Task<PaginatedResult<ListarHistoricoResponse>> Handle(ListarHistoricoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               nfe.id,
                               nfh.data,
                               nfh.status_alterado AS status,
                               CONCAT(us.nome, ' ', us.sobrenome) AS nome_usuario
                           FROM 
                               notas_fiscais_historico nfh
                           LEFT JOIN 
                               usuarios us ON us.id = nfh.usuario_id 
                           LEFT JOIN 
                               notas_fiscais_entrada nfe ON nfe.id = nfh.nota_fiscal_entrada_id 
                           WHERE 
                               nfe.id = @NotaFiscalEntradaId
                               AND nfh.group_tenant_id = @GroupTenantId
                           """;

        return PaginatedQueryBuilder<ListarHistoricoRequest, ListarHistoricoResponse>
            .Create(connection, sql, request, userContext)
            .ExecuteAsync();
    }
}