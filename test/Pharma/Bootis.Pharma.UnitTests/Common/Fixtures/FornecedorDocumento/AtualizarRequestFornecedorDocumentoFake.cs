using Bootis.Pessoa.Application.Requests.Fornecedor.Atualizar;
using Bootis.Shared.Common.Extensions;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Fixtures.FornecedorDocumento;

public static class AtualizarRequestFornecedorDocumentoFake
{
    public static AtualizarDocumentoRequest CriarCommandValido()
    {
        return new AtualizarDocumentoRequest
        {
            FornecedorDocumentoId = Uuid.NewSequential(),
            Identificacao = "Teste",
            TipoDocumentoId = 1.ToGuid()
        };
    }
}
