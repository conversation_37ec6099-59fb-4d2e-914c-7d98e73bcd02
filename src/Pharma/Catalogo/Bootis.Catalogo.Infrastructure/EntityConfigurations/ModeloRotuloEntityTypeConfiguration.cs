using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Catalogo.Infrastructure.EntityConfigurations;

public class ModeloRotuloEntityTypeConfiguration : BaseEntityTypeConfiguration<ModeloRotulo>
{
    public override void Configure(EntityTypeBuilder<ModeloRotulo> builder)
    {
        builder.ToTable("modelos_rotulo");

        builder
            .Property(c => c.Descricao)
            .NomeDescricao(TamanhoTexto.Cem)
            .IsRequired();

        builder
            .Property(c => c.TipoRotulo)
            .IsRequired();

        builder
            .Property(c => c.Ativo)
            .HasDefaultValue(true)
            .IsRequired();
    }
}