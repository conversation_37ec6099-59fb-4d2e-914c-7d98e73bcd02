using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.FormaFarmaceutica.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.FormaFarmaceutica.Obter;

public class ObterFormaFarmaceuticaRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder("""
                                    SELECT ff.id,
                                           la.id AS laboratorio_id,
                                           la.nome_laboratorio AS laboratorio_descricao,
                                           ff.ativo,
                                           ff.ordem,
                                           ff.descricao,
                                           ff.apresentacao,
                                           ff.unidade_medida_id,
                                           un.abreviacao AS abreviacao_unidade_medida,
                                           un.descricao AS descricao_unidade_medida,
                                           ff.uso_forma_farmaceutica,
                                           ff.tipo_calculo,
                                           ff.percentual_minimo_excipiente,
                                           ff.validade_dias,
                                           ff.custo_operacional,
                                           mom.id AS modelo_ordem_manipulacao_id,
                                           mom.descricao AS modelo_ordem_manipulacao_descricao
                                      FROM formas_farmaceutica ff
                                           LEFT JOIN laboratorios la ON
                                                     la.id = ff.laboratorio_id
                                           LEFT JOIN unidades_medida un ON
                                                     un.id = ff.unidade_medida_id
                                           LEFT JOIN modelos_ordem_manipulacao mom ON
                                                     mom.id = ff.modelo_ordem_manipulacao_id
                                     WHERE ff.id = @id
                                       AND ff.group_tenant_id = @groupTenantId
                                    """);

        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql.ToString(),
            new { request.Id, userContext.GroupTenantId });

        if (result is not null) return result;
        var message = Localizer.Instance.GetMessage_FormaFarmaceutica_GuidNaoEncontrado(request.Id);
        throw new DomainException(message);
    }
}