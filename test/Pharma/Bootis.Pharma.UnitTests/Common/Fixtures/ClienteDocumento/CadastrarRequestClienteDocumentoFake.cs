using Bootis.Pessoa.Application.Requests.Cliente.Cadastrar;
using Bootis.Shared.Common.Extensions;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Fixtures.ClienteDocumento;

public static class CadastrarRequestClienteDocumentoFakes
{
    public static CadastrarDocumentoRequest CriarCommandValido()
    {
        return new CadastrarDocumentoRequest
        {
            ClienteId = Uuid.NewSequential(),
            Identificacao = "Teste",
            TipoDocumentoId = 1.ToGuid(),
            Observacao = "obs"
        };
    }
}
