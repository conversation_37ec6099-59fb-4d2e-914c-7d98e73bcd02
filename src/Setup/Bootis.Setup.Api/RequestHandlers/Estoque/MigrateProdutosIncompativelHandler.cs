namespace Bootis.Setup.Api.RequestHandlers.Estoque;

public record ProdutoIncompativel(
    Guid Id,
    string ProdutoDescricao,
    string ProdutoIncompativelDescricao,
    string Descricao,
    int NivelIncompatibilidade);

public record MigrateProdutosIncompativel(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateProdutosIncompativelHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateProdutosIncompativel>(configuration)
{
    private readonly IConfiguration _configuration = configuration;

    public override async Task Handle(MigrateProdutosIncompativel request, CancellationToken cancellationToken)
    {
        var dataTable =
            await GetDataFromSourceDatabase(request, "SELECT * FROM produtos_incompativel", cancellationToken);

        await InsertOrUpdateData(dataTable, "produtos_incompativel", cancellationToken);
    }
}