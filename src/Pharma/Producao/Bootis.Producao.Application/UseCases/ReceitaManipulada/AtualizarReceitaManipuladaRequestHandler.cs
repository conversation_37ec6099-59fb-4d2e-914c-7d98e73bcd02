using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Producao.Application.Extensions;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Producao.Application.Services.Interfaces;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Factories;
using Bootis.Producao.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class AtualizarReceitaManipuladaRequestHandler(
    IReceitaManipuladaRepository receitaManipuladaRepository,
    IClienteRepository clienteRepository,
    IPrescritorRepository prescritorRepository,
    IFormaFarmaceuticaRepository formaFarmaceuticaRepository,
    IProdutoRepository produtoRepository,
    IProdutoSinonimoRepository produtoSinonimoRepository,
    IReceitaUpsellAppService upsellService,
    ICalculoReceitaManipuladaFactory calculoReceitaFactory,
    IValidarIncompatibilidadeAppService validadorIncompatibilidade)
    : IRequestHandler<AtualizarReceitaManipuladaRequest, AtualizarReceitaManipuladaResponse>
{
    public async Task<AtualizarReceitaManipuladaResponse> Handle(AtualizarReceitaManipuladaRequest request,
        CancellationToken cancellationToken)
    {
        var receita = await receitaManipuladaRepository.ObterReceitaManipuladaSemTrackingPorIdAsync(request.Id)
                      ?? throw new ValidationException(
                          Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.Id));

        if (receita.Status is StatusReceita.AguardandoConferencia or StatusReceita.ConferenciaFinalizada)
            throw new ValidationException(Localizer.Instance.GetMessage_ReceitaManipulada_StatusInvalido(nameof(receita.Status)));

        var paciente = await clienteRepository.ObterClienteAsync(request.PacienteId);
        var forma = await formaFarmaceuticaRepository.ObterFormaFarmaceuticaAsync(request.FormaFarmaceuticaId);
        var prescritor = request.PrescritorId.HasValue
            ? await prescritorRepository.ObterPorIdAsync(request.PrescritorId.Value)
            : null;

        receita.AlterarPaciente(paciente);
        receita.AlterarFormaFarmaceutica(forma);
        receita.AlterarQuantidadeReceita(request.QuantidadeReceita);
        receita.AlterarQuantidadeRepetir(request.QuantidadeRepetir);
        if (request.QuantidadeDose.HasValue)
            receita.AlterarQuantidadeDose(request.QuantidadeDose);
        receita.AlterarPrescritor(prescritor);

        receita.LimparItens();
        
        await ValidarIncompatibilidades(request.Componentes);
        
        receita.AplicarUsoContinuo(
            request.UsoContinuo,
            request.TipoUsoContinuo,
            request.QuantidadeReSell,
            request.PeriodicidadeReSell,
            request.QuantidadeDose
        );

        foreach (var item in request.Componentes)
        {
            ProdutoSinonimo sinonimo = null;

            var descricao = !string.IsNullOrWhiteSpace(item.DescricaoRotulo)
                ? item.DescricaoRotulo
                : null;

            var produto = await produtoRepository.ObterProdutoAsync(item.ProdutoId);

            if (item.ProdutoSinonimoId.HasValue)
            {
                await produtoSinonimoRepository.ValidarProdutoVinculadoAsync(item.ProdutoId, item.ProdutoSinonimoId.Value);
                sinonimo = await produtoSinonimoRepository.ObterProdutoSinonimoAsync(item.ProdutoSinonimoId.Value);

                if (descricao is null && !string.IsNullOrWhiteSpace(sinonimo?.Sinonimo))
                    descricao = sinonimo.Sinonimo;
            }

            descricao ??= produto.Descricao;

            receita.AdicionarItemReceita(
                produto,
                descricao,
                item.Quantidade,
                item.UnidadeMedidaId,
                item.TipoQuantificacao,
                item.Ordem,
                item.OcultaRotulo,
                sinonimo,
                sinonimo?.FatorEquivalencia,
                sinonimo?.PercentualCorrecao
            );
        }

        var resultado = calculoReceitaFactory.Criar(
            receita.Itens,
            receita.QuantidadeReceita,
            receita.FormaFarmaceutica,
            receita.Paciente,
            receita.PrescritorId,
            receita.TipoDescontoManual,
            receita.DescontoManual,
            receita.PercentualDescontoManual
        );

        await resultado.ExecutarAsync();
        
        receita.CalcularEAplicarTerminoUsoContinuo();

        var receitaDto = receita.MapearParaResultadoDetalhesDto(resultado);

        var upsell = await upsellService.GerarUpsellAsync(resultado, receita.QuantidadeReceita);

        return new AtualizarReceitaManipuladaResponse
        {
            Receita = receitaDto,
            Upsell = upsell
        };
    }

    private Task ValidarIncompatibilidades(IEnumerable<AtualizarReceitaItemRequest> componentes)
    {
        var produtoIds = componentes.Select(c => c.ProdutoId).Distinct().ToList();
        return validadorIncompatibilidade.ValidarIncompatibilidadesBloqueioAsync(produtoIds);
    }

}