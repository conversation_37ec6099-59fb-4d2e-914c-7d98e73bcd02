using System.Runtime.Serialization;
using Bootis.Catalogo.Domain.Enumerations;

namespace Bootis.Catalogo.Application.Requests.Produto.Listar;

public class ListarPrescricaoResponse
{
    public Guid Id { get; set; }
    public Guid ProdutoId { get; set; }
    public Guid? SinonimoId { get; set; }
    public string <PERSON>cricao { get; set; }
    public int Codigo { get; set; }
    public string ProdutoDescricao { get; set; }
    public Guid? FormulaPadraoId { get; set; }
    public int ClasseProduto { get; set; }

    [IgnoreDataMember] public string Sinonimo { get; set; }

    public int? UnidadePrescricaoId { get; set; }
    public string UnidadePrescricaoAbreviacao { get; set; }
    public bool TemIncompatibilidade { get; set; }

    public TipoComponenteReceita TipoComponente
    {
        get
        {
            if (FormulaPadraoId.HasValue)
                return TipoComponenteReceita.Formula;
            return (TipoComponenteReceita)ClasseProduto;
        }
    }

    public int QuantidadeComponentes { get; init; }
}