using Bootis.Catalogo.Application.Requests.ProdutoIncompativel.Atualizar;
using Bootis.Catalogo.Domain.Enumerations;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.ProdutoIncompativel;

public static class AtualizarProdutoIncompativelCommandFake
{
    public static AtualizarRequest CreateAtualizarProdutoIncompativelCommandValido()
    {
        return new AtualizarRequest
        {
            Id = Uuid.NewSequential(),
            ProdutoIncompativelId = Uuid.NewSequential(),
            Descricao = "Teste Atualizar",
            NivelIncompatibilidade = NivelIncompatibilidade.Bloqueio
        };
    }
}
