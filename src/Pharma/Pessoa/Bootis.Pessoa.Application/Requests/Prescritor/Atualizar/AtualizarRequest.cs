using MediatR;

namespace Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;

public class AtualizarRequest : IRequest
{
    public Guid Id { get; set; }
    public string NomeCompleto { get; set; }
    public Guid TipoRegistroId { get; set; }
    public Guid UfRegistroId { get; set; }
    public string CodigoRegistro { get; set; }
    public DateOnly? DataNascimento { get; set; }
    public string Observacao { get; set; }
    public decimal DescontoProdutosAcabados { get; set; }
    public decimal DescontoFormulas { get; set; }
    public string LinkImagem { get; set; }
    public List<Guid> EspecialidadesId { get; init; } = new();
}