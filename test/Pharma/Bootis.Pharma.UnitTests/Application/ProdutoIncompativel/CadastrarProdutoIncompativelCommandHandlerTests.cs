using Bootis.Catalogo.Application.UseCases.ProdutoIncompativel;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Estoque.UnitTests.Fixtures.ProdutoIncompativel;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.ProdutoIncompativel;

public class CadastrarProdutoIncompativelCommandHandlerTests : BaseTest
{
    private readonly CadastrarRequestHandler _cadastrarProdutoIncompativelCommandHandler;
    private readonly Mock<IProdutoIncompativelRepository> _produtoIncompativelRepositoryMock;
    private readonly Mock<IProdutoRepository> _produtoRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public CadastrarProdutoIncompativelCommandHandlerTests()
    {
        _produtoRepositoryMock = new Mock<IProdutoRepository>();
        _produtoIncompativelRepositoryMock = new Mock<IProdutoIncompativelRepository>();

        unitOfWork.Setup(l => l.GetRepository<IProdutoIncompativelRepository>())
            .Returns(_produtoIncompativelRepositoryMock.Object);
        unitOfWork.Setup(l => l.GetRepository<IProdutoRepository>())
            .Returns(_produtoRepositoryMock.Object);

        _cadastrarProdutoIncompativelCommandHandler = new CadastrarRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task CadastrarProdutoIncompativel_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = CadastrarProdutoIncompativelCommandFake.CreateProdutoIncompativelCommandValido();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var produtoIncompatibilidade = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var model = ProdutoIncompativelFake.CreateProdutoMateriaPrimaIncompativelValido();

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoIncompativelId)).Returns(
            Task.FromResult(produtoIncompatibilidade));

        _produtoIncompativelRepositoryMock
            .Setup(l => l.ValidarVinculoPorProdutoAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
            .Returns(
                Task.FromResult(false));

        _produtoIncompativelRepositoryMock.Setup(l => l.Add(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _cadastrarProdutoIncompativelCommandHandler.Handle(cmd, default);

        //Assert
        _produtoIncompativelRepositoryMock
            .Verify(
                l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel>(m =>
                    m.ProdutoId == model.ProdutoId)),
                Times.Once);
    }

    [Fact]
    public async Task CadastrarProdutoIncompativel_ExecutadoComErro_ProdutoGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarProdutoIncompativelCommandFake.CreateProdutoIncompativelCommandValido();
        var model = ProdutoIncompativelFake.CreateProdutoMateriaPrimaIncompativelValido();

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult<Produto>(default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarProdutoIncompativelCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoIncompativelRepositoryMock
            .Verify(
                l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel>(m =>
                    m.ProdutoId == model.ProdutoId)),
                Times.Never);
    }

    [Fact]
    public async Task CadastrarProdutoIncompativel_ExecutadoComErro_ProdutoIncompatibilidadeGuidNaoEncontrado()
    {
        //Arrange
        var cmd = CadastrarProdutoIncompativelCommandFake.CreateProdutoIncompativelCommandValido();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var model = ProdutoIncompativelFake.CreateProdutoMateriaPrimaIncompativelValido();

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoIncompativelId)).Returns(
            Task.FromResult<Produto>(default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarProdutoIncompativelCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoIncompativelRepositoryMock
            .Verify(
                l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel>(m =>
                    m.ProdutoId == model.ProdutoId)),
                Times.Never);
    }

    [Fact]
    public async Task CadastrarProdutoIncompativel_ExecutadoComErro_VinculoExistente()
    {
        //Arrange
        var cmd = CadastrarProdutoIncompativelCommandFake.CreateProdutoIncompativelCommandValido();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var produtoIncompatibilidade = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var model = ProdutoIncompativelFake.CreateProdutoMateriaPrimaIncompativelValido();

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoIncompativelId)).Returns(
            Task.FromResult(produtoIncompatibilidade));

        _produtoIncompativelRepositoryMock
            .Setup(l => l.ValidarVinculoPorProdutoAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
            .Returns(Task.FromResult(true));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarProdutoIncompativelCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoIncompativelRepositoryMock
            .Verify(
                l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel>(m =>
                    m.ProdutoId == model.ProdutoId)),
                Times.Never);
    }

    [Fact]
    public async Task CadastrarProdutoIncompativel_ExecutadoComErro_ProdutoInvalido()
    {
        //Arrange
        var cmd = CadastrarProdutoIncompativelCommandFake.CreateProdutoIncompativelCommandValido();
        var produto = ProdutoFake.CreateProdutoFake_Embalagem();
        var produtoIncompatibilidade = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var model = ProdutoIncompativelFake.CreateProdutoMateriaPrimaIncompativelValido();

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoIncompativelId)).Returns(
            Task.FromResult(produtoIncompatibilidade));

        _produtoIncompativelRepositoryMock
            .Setup(l => l.ValidarVinculoPorProdutoAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
            .Returns(Task.FromResult(false));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _cadastrarProdutoIncompativelCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoIncompativelRepositoryMock
            .Verify(
                l => l.Add(It.Is<Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate.ProdutoIncompativel>(m =>
                    m.ProdutoId == model.ProdutoId)),
                Times.Never);
    }
}
