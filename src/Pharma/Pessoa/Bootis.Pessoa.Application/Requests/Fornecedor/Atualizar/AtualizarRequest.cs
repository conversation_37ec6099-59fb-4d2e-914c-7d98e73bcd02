using Bootis.Pessoa.Domain.Enumerations;
using MediatR;

namespace Bootis.Pessoa.Application.Requests.Fornecedor.Atualizar;

public class AtualizarRequest : IRequest
{
    public Guid Id { get; set; }
    public TipoPessoa TipoPessoa { get; set; }
    public string Nome { get; set; }
    public int TipoFornecedorId { get; set; }
    public string RazaoSocial { get; set; }
    public string Observacao { get; set; }
    public string Cnpj { get; set; }
    public string Cpf { get; set; }
    public string LinkImagem { get; set; }
}