using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Catalogo.Infrastructure.Repositories;

public class EmbalagemClassificacaoRepository(IUserContext userContext, IDbContext context)
    : Repository<EmbalagemClassificacao>(context), IEmbalagemClassificacaoRepository
{
    public async Task<EmbalagemClassificacao> ObterPorIdAsync(Guid id)
    {
        return await DbSet
            .Include(c => c.EmbalagemClassificacaoFormaFarmaceutica)
            .Include(c => c.ProdutoEmbalagem)
            .SingleOrDefaultAsync(c =>
                c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<List<EmbalagemClassificacao>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<EmbalagemClassificacao>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }

    public async Task<bool> ValidarPorDescricaoAsync(string descricao)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM embalagens_classificacao ec
                                                     WHERE ec.descricao = @descricao
                                                       AND ec.group_tenant_id = @groupTenantId) THEN 1 ELSE 0 END AS existe_registro;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { descricao, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarPorIdEDescricaoAsync(Guid id, string descricao)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM embalagens_classificacao ec
                                                     WHERE ec.id = @id
                                                       AND ec.descricao = @descricao
                                                       AND ec.group_tenant_id = @groupTenantId) THEN 1 ELSE 0 END AS existe_registro;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { id, descricao, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarVinculoComProdutoEmbalagemAsync(Guid embalagemClassificacaoId,
        Guid produtoId)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM embalagens_classificacao ec
                                                     LEFT JOIN produtos_embalagem pe ON
                                                            pe.embalagem_classificacao_id = ec.id
                                                     LEFT JOIN produtos prod ON
                                                            prod.id = pe.produto_id
                                                     WHERE ec.id = @embalagemClassificacaoId
                                                       AND prod.id = @produtoId) THEN 1 ELSE 0 END AS existe_registro;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { embalagemClassificacaoId, produtoId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<IEnumerable<(Guid Id, string Descricao)>> VerificarDependenciaAsync(
        IEnumerable<Guid> ids)
    {
        const string sql = """
                           SELECT ec.id, 
                                  ec.descricao
                           FROM embalagens_classificacao ec
                                LEFT JOIN embalagens_classificacao_forma_farmaceutica ecf ON
                                      ecf.embalagem_classificacao_id = ec.id
                           WHERE ec.id = ANY ( @ids )
                             AND ec.group_tenant_id = @groupTenantId
                             AND ecf.embalagem_classificacao_id IS NOT NULL
                           GROUP BY ec.id,
                                    ec.descricao;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return (await dbConnection.QueryAsync<(Guid Id, string Descricao)>(sql,
            new { ids, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction())).ToList();
    }
}