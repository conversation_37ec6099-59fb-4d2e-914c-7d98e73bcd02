using Bootis.Compra.Application.Extensions;
using Bootis.Compra.Application.Requests.PedidoCompra.Atualizar;
using Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Compra.Application.UseCases.PedidoCompra;

public class AprovarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext)
    : IRequestHandler<AprovarRequest>
{
    private readonly IPedidoCompraRepository _pedidoCompraRepository =
        unitOfWork.GetRepository<IPedidoCompraRepository>();

    private readonly IUsuarioRepository _usuarioRepository = unitOfWork.GetRepository<IUsuarioRepository>();

    public async Task Handle(AprovarRequest request, CancellationToken cancellationToken)
    {
        var pedidoCompra = await _pedidoCompraRepository.ObterPedidoCompraAsync(request.PedidoCompraId);
        var usuario = await _usuarioRepository.ObterUsuarioAsync(userContext.UserId);

        pedidoCompra.Aprovar(usuario, request.PedidoCompraItensId);

        _pedidoCompraRepository.Update(pedidoCompra);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}