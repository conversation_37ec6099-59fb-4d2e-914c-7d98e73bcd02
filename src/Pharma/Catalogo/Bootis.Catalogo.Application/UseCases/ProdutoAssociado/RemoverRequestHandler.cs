using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Remover;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.ProdutoAssociado;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork)
    : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var produtoAssociadoRepository =
            unitOfWork.GetRepository<IProdutoAssociadoRepository>();

        foreach (var id in request.ProdutosAssociadoId)
        {
            var produtoAssociado = await produtoAssociadoRepository.ObterProdutoAssociadoAsync(id);

            produtoAssociadoRepository.Remove(produtoAssociado);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}