using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.ProdutoSinonimo.Atualizar;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.ProdutoSinonimo;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork)
    : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        var produtoSinonimoRepository =
            unitOfWork.GetRepository<IProdutoSinonimoRepository>();

        var produtoSinonimo = await produtoSinonimoRepository.ObterProdutoSinonimoAsync(request.Id);
        await produtoSinonimoRepository.ValidarProdutoSinonimoPorIdEDescricaoAsync(request.Id,
            request.Sinonimo);

        if (produtoSinonimo.Produto.ClasseProdutoId != TipoClasseProdutoAbreviacao.MateriaPrima)
            throw new ValidationException(nameof(request.Sinonimo),
                Localizer.Instance.GetMessage_ProdutoSinonimo_ProdutoInvalido());

        if (request.Sinonimo == produtoSinonimo.Produto.Descricao)
            throw new ValidationException(nameof(request.Sinonimo),
                Localizer.Instance.GetMessage_ProdutoSinonimo_SinonimoIgualAoProduto(request.Sinonimo,
                    produtoSinonimo.Produto.Id));

        produtoSinonimo.Atualizar(request.PercentualCorrecao.GetValueOrDefault(),
            request.FatorEquivalencia.GetValueOrDefault(), request.DescricaoRotulo, request.Sinonimo);

        produtoSinonimoRepository.Update(produtoSinonimo);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}