using Bootis.Pessoa.Application.Requests.Cliente.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.Cliente;

public class CadastrarContatoClienteRequestValidator : AbstractValidator<CadastrarContatoRequest>
{
    public CadastrarContatoClienteRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.TipoContatoId)
            .NotEmpty()
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.TipoContatoId)));

        RuleFor(c => c.Identificacao)
            .NotEmpty()
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Identificacao)));

        RuleFor(c => c.Observacao)
            .MaximumLength(1000)
            .WithMessage(f => localizer.GetMessage_Validation_TamanhoMaximo(f.<PERSON><PERSON>er<PERSON>, 1000));
    }
}