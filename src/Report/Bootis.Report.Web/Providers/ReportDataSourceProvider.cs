using Bootis.Report.Web.ReportSources;

namespace Bootis.Report.Web.Services;

public class ReportDataSourceProvider(IEnumerable<IReportSource> reportSources)
{
    public IEnumerable<ReportDataSourceInfo> GetAvailableDataSources()
    {
        return reportSources.Select(source => new ReportDataSourceInfo
        {
            Key = source.Key,
            Name = source.Name,
            ReportSource = source
        });
    }
}

public class ReportDataSourceInfo
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public IReportSource ReportSource { get; set; } = null!;
}