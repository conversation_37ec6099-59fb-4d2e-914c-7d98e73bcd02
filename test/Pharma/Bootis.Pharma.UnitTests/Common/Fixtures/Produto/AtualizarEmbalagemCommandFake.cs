using Bootis.Catalogo.Application.Requests.Produto.Atualizar;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoEmbalagem;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Produto;

public class AtualizarEmbalagemCommandFake
{
    public static AtualizarEmbalagemRequest CreateCommandValido()
    {
        return new AtualizarEmbalagemRequest
        {
            Id = Uuid.NewSequential(),
            ClasseProdutoId = TipoClasseProdutoAbreviacao.Embalagem,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = Uuid.NewSequential(),
            SubGrupoId = Uuid.NewSequential(),
            UnidadeEstoqueId = UnidadeMedidaAbreviacao.un,
            UsoContinuo = false,
            ClassificacaoEmbalagemId = Uuid.NewSequential(),
            Volume = 10,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            EmbalagemAssociacao = new List<ProdutoEmbalagemAssociacaoDto>
            {
                new()
                {
                    QuantidadeEmbalagem = 1,
                    ProdutoEmbalagemId = Guid.Empty
                }
            },
            NumeroCapsulaAssociacao = new List<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto>
            {
                new()
                {
                    QuantidadeCapsula = 1,
                    NumeroCapsulaId = 1.ToGuid()
                }
            }
        };
    }

    public static AtualizarEmbalagemRequest CreateCommandValidoSemFornecedor()
    {
        return new AtualizarEmbalagemRequest
        {
            Id = Uuid.NewSequential(),
            ClasseProdutoId = TipoClasseProdutoAbreviacao.Embalagem,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = null,
            SubGrupoId = Uuid.NewSequential(),
            UnidadeEstoqueId = UnidadeMedidaAbreviacao.un,
            UsoContinuo = false,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            ClassificacaoEmbalagemId = Uuid.NewSequential(),
            Volume = 10,
            EmbalagemAssociacao = new List<ProdutoEmbalagemAssociacaoDto>(),
            NumeroCapsulaAssociacao = new List<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto>()
        };
    }
}
