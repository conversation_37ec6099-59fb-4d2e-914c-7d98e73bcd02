<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <LangVersion>default</LangVersion>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <AssemblyName>Bootis.Pharma.Api</AssemblyName>
        <RootNamespace>Bootis.Pharma.Api</RootNamespace>
        <ProjectGuid>789c304d-f014-4894-8b7b-41d14114ac2c</ProjectGuid>
        <UserSecretsId>364c3f33-792c-4413-9ddb-bb8cde8d8c69</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Aspire\Bootis.ServiceDefaults\Bootis.ServiceDefaults.csproj"/>
        <ProjectReference Include="..\Catalogo\Bootis.Catalogo.Api\Bootis.Catalogo.Api.csproj"/>
        <ProjectReference Include="..\Compra\Bootis.Compra.Api\Bootis.Compra.Api.csproj"/>
        <ProjectReference Include="..\Estoque\Bootis.Estoque.Api\Bootis.Estoque.Api.csproj"/>
        <ProjectReference Include="..\Localidade\Bootis.Localidade.Api\Bootis.Localidade.Api.csproj"/>
        <ProjectReference Include="..\Organizacional\Bootis.Organizacional.Api\Bootis.Organizacional.Api.csproj"/>
        <ProjectReference Include="..\Pessoa\Bootis.Pessoa.Api\Bootis.Pessoa.Api.csproj"/>
        <ProjectReference Include="..\Producao\Bootis.Producao.Api\Bootis.Producao.Api.csproj"/>
        <ProjectReference Include="..\Venda\Bootis.Venda.Api\Bootis.Venda.Api.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Infrastructure\"/>
    </ItemGroup>


</Project>
