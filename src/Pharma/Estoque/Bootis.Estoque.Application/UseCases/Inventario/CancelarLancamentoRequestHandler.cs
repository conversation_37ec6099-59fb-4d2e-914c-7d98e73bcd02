using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.Inventario.Atualizar;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Inventario;

public class CancelarLancamentoRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IInventarioRepository inventarioRepository,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<CancelarLancamentoRequest>
{
    public async Task Handle(CancelarLancamentoRequest request, CancellationToken cancellationToken)
    {
        var inventario = await inventarioRepository.ObterInventarioAsync(request.Id);

        inventario.CancelarLancamento(userContext.UserId);

        inventarioRepository.Update(inventario);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}